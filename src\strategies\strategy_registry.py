"""
Strategy Registry - Central registry for all trading strategies.
Manages strategy initialization, configuration, and execution.
"""

from typing import Dict, List, Any, Optional, Type
from datetime import datetime

from .base_strategy import BaseStrategy, strategy_manager
from .rsi_strategy import AdaptiveRSIStrategy
from .pivot_strategy import EnhancedPivotStrategy
from .macd_strategy import MLEnhancedMACDStrategy
from .moving_averages_strategy import DynamicMovingAveragesStrategy
from .breakout_strategy import VolumeConfirmedBreakoutStrategy

from ..utils.logger import get_logger
from ..utils.config import config

logger = get_logger(__name__)

class StrategyRegistry:
    """Registry for all available trading strategies."""
    
    def __init__(self):
        """Initialize strategy registry."""
        self.strategies: Dict[str, Type[BaseStrategy]] = {}
        self.initialized_strategies: Dict[str, BaseStrategy] = {}
        self._register_default_strategies()
    
    def _register_default_strategies(self):
        """Register all default strategies."""
        self.register_strategy("adaptive_rsi", AdaptiveRSIStrategy)
        self.register_strategy("enhanced_pivot", EnhancedPivotStrategy)
        self.register_strategy("ml_enhanced_macd", MLEnhancedMACDStrategy)
        self.register_strategy("dynamic_moving_averages", DynamicMovingAveragesStrategy)
        self.register_strategy("volume_confirmed_breakout", VolumeConfirmedBreakoutStrategy)
        
        logger.info(f"Registered {len(self.strategies)} default strategies")
    
    def register_strategy(self, name: str, strategy_class: Type[BaseStrategy]):
        """Register a strategy class.
        
        Args:
            name: Strategy name
            strategy_class: Strategy class
        """
        self.strategies[name] = strategy_class
        logger.debug(f"Registered strategy: {name}")
    
    def get_strategy_class(self, name: str) -> Optional[Type[BaseStrategy]]:
        """Get strategy class by name.
        
        Args:
            name: Strategy name
            
        Returns:
            Strategy class or None if not found
        """
        return self.strategies.get(name)
    
    def create_strategy(self, name: str, parameters: Optional[Dict[str, Any]] = None) -> Optional[BaseStrategy]:
        """Create strategy instance.
        
        Args:
            name: Strategy name
            parameters: Strategy parameters
            
        Returns:
            Strategy instance or None if not found
        """
        strategy_class = self.get_strategy_class(name)
        if strategy_class is None:
            logger.error(f"Strategy not found: {name}")
            return None
        
        try:
            strategy = strategy_class(parameters)
            self.initialized_strategies[name] = strategy
            
            # Register with global strategy manager
            strategy_manager.register_strategy(strategy)
            
            logger.info(f"Created strategy instance: {name}")
            return strategy
            
        except Exception as e:
            logger.error(f"Error creating strategy {name}: {e}")
            return None
    
    def get_strategy(self, name: str) -> Optional[BaseStrategy]:
        """Get initialized strategy instance.
        
        Args:
            name: Strategy name
            
        Returns:
            Strategy instance or None if not found
        """
        return self.initialized_strategies.get(name)
    
    def initialize_enabled_strategies(self) -> Dict[str, BaseStrategy]:
        """Initialize all enabled strategies from configuration.
        
        Returns:
            Dictionary of initialized strategies
        """
        enabled_strategies = {}
        strategy_configs = config.get('strategies', {})
        
        for strategy_name, strategy_config in strategy_configs.items():
            if strategy_config.get('enabled', False):
                # Map config names to strategy class names
                strategy_class_map = {
                    'rsi': 'adaptive_rsi',
                    'pivot_points': 'enhanced_pivot',
                    'macd': 'ml_enhanced_macd',
                    'moving_averages': 'dynamic_moving_averages',
                    'breakout': 'volume_confirmed_breakout'
                }
                
                class_name = strategy_class_map.get(strategy_name, strategy_name)
                
                # Extract parameters (exclude 'enabled' flag)
                parameters = {k: v for k, v in strategy_config.items() if k != 'enabled'}
                
                strategy = self.create_strategy(class_name, parameters)
                if strategy:
                    enabled_strategies[strategy_name] = strategy
                else:
                    logger.warning(f"Failed to initialize strategy: {strategy_name}")
        
        logger.info(f"Initialized {len(enabled_strategies)} enabled strategies")
        return enabled_strategies
    
    def get_all_strategies(self) -> Dict[str, BaseStrategy]:
        """Get all initialized strategies.
        
        Returns:
            Dictionary of all initialized strategies
        """
        return self.initialized_strategies.copy()
    
    def get_strategy_info(self, name: str) -> Dict[str, Any]:
        """Get detailed information about a strategy.
        
        Args:
            name: Strategy name
            
        Returns:
            Strategy information dictionary
        """
        strategy = self.get_strategy(name)
        if not strategy:
            return {'error': f'Strategy {name} not found'}
        
        return strategy.get_strategy_summary()
    
    def get_all_strategy_info(self) -> Dict[str, Dict[str, Any]]:
        """Get information about all strategies.
        
        Returns:
            Dictionary with information about all strategies
        """
        info = {}
        
        for name, strategy in self.initialized_strategies.items():
            info[name] = strategy.get_strategy_summary()
        
        return info
    
    def run_strategy(self, name: str, symbols: List[str]) -> Dict[str, Any]:
        """Run a specific strategy on given symbols.
        
        Args:
            name: Strategy name
            symbols: List of stock symbols
            
        Returns:
            Strategy execution results
        """
        strategy = self.get_strategy(name)
        if not strategy:
            return {'error': f'Strategy {name} not found'}
        
        try:
            return strategy.run_strategy(symbols)
        except Exception as e:
            logger.error(f"Error running strategy {name}: {e}")
            return {'error': str(e)}
    
    def run_all_strategies(self, symbols: List[str]) -> Dict[str, Any]:
        """Run all initialized strategies on given symbols.
        
        Args:
            symbols: List of stock symbols
            
        Returns:
            Results from all strategies
        """
        results = {}
        
        for name, strategy in self.initialized_strategies.items():
            try:
                results[name] = strategy.run_strategy(symbols)
            except Exception as e:
                logger.error(f"Error running strategy {name}: {e}")
                results[name] = {'error': str(e)}
        
        return {
            'execution_time': datetime.now().isoformat(),
            'strategies_run': len(results),
            'symbols_processed': len(symbols),
            'results': results
        }
    
    def get_latest_signals(
        self, 
        strategy_name: Optional[str] = None, 
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Get latest signals from strategies.
        
        Args:
            strategy_name: Filter by strategy name (optional)
            limit: Maximum number of signals
            
        Returns:
            List of latest signals
        """
        return strategy_manager.get_latest_signals(strategy_name, limit)
    
    def validate_strategy_requirements(self, name: str) -> Dict[str, Any]:
        """Validate that a strategy has all required components.
        
        Args:
            name: Strategy name
            
        Returns:
            Validation results
        """
        strategy = self.get_strategy(name)
        if not strategy:
            return {'valid': False, 'error': f'Strategy {name} not found'}
        
        validation_results = {
            'valid': True,
            'strategy_name': name,
            'required_indicators': strategy.get_required_indicators(),
            'parameters': strategy.parameters,
            'config': strategy.config
        }
        
        # Check if required indicators are available
        # This would typically check against available data columns
        # For now, we'll just return the requirements
        
        return validation_results
    
    def get_strategy_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary for all strategies.
        
        Returns:
            Performance summary dictionary
        """
        summary = {
            'total_strategies': len(self.initialized_strategies),
            'strategies': {},
            'generated_at': datetime.now().isoformat()
        }
        
        for name, strategy in self.initialized_strategies.items():
            strategy_summary = strategy.get_strategy_summary()
            summary['strategies'][name] = {
                'signals_generated': strategy_summary.get('signals_generated_count', 0),
                'performance_metrics': strategy_summary.get('performance_metrics', {}),
                'parameters': strategy_summary.get('parameters', {}),
                'required_indicators': strategy_summary.get('required_indicators', [])
            }
        
        return summary

# Global strategy registry instance
strategy_registry = StrategyRegistry()

def initialize_strategies() -> Dict[str, BaseStrategy]:
    """Initialize all enabled strategies from configuration.
    
    Returns:
        Dictionary of initialized strategies
    """
    return strategy_registry.initialize_enabled_strategies()

def get_strategy(name: str) -> Optional[BaseStrategy]:
    """Get strategy by name.
    
    Args:
        name: Strategy name
        
    Returns:
        Strategy instance or None
    """
    return strategy_registry.get_strategy(name)

def run_all_strategies(symbols: List[str]) -> Dict[str, Any]:
    """Run all enabled strategies.
    
    Args:
        symbols: List of stock symbols
        
    Returns:
        Results from all strategies
    """
    return strategy_registry.run_all_strategies(symbols)

def get_latest_signals(strategy_name: Optional[str] = None, limit: int = 100) -> List[Dict[str, Any]]:
    """Get latest trading signals.
    
    Args:
        strategy_name: Filter by strategy name
        limit: Maximum number of signals
        
    Returns:
        List of latest signals
    """
    return strategy_registry.get_latest_signals(strategy_name, limit)

if __name__ == "__main__":
    # Initialize strategies for testing
    strategies = initialize_strategies()
    print(f"Initialized {len(strategies)} strategies:")
    for name in strategies:
        print(f"  - {name}")
    
    # Get performance summary
    summary = strategy_registry.get_strategy_performance_summary()
    print(f"\nStrategy Performance Summary:")
    print(f"Total strategies: {summary['total_strategies']}")
    for name, info in summary['strategies'].items():
        print(f"  {name}: {info['signals_generated']} signals generated")
