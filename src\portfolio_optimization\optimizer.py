"""
Main Portfolio Optimization Engine.
Integrates all optimization methods and provides unified interface.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Union
from datetime import datetime, date, timedelta
from dataclasses import dataclass
from enum import Enum

from .mpt_optimizer import mpt_optimizer, OptimizationResult, PortfolioConstraints
from .black_litterman import bl_optimizer, InvestorView, BlackLittermanResult
from .risk_parity import risk_parity_optimizer
from .performance_analytics import performance_analytics, PerformanceMetrics
from .rebalancing import portfolio_rebalancer, RebalancingConfig, RebalancingResult
from ..data.database import db_manager
from ..data.crud import StockCRUD, PriceCRUD
from ..utils.logger import get_logger

logger = get_logger(__name__)

class OptimizationMethod(Enum):
    """Portfolio optimization methods."""
    MAX_SHARPE = "max_sharpe"
    MIN_VARIANCE = "min_variance"
    TARGET_RETURN = "target_return"
    RISK_PARITY_ERC = "risk_parity_erc"
    RISK_PARITY_HRP = "risk_parity_hrp"
    BLACK_LITTERMAN = "black_litterman"
    EQUAL_WEIGHT = "equal_weight"

@dataclass
class PortfolioOptimizationRequest:
    """Portfolio optimization request."""
    symbols: List[str]
    method: OptimizationMethod
    constraints: Optional[PortfolioConstraints] = None
    target_return: Optional[float] = None
    investor_views: Optional[List[InvestorView]] = None
    lookback_days: int = 252
    rebalancing_config: Optional[RebalancingConfig] = None
    benchmark_symbol: Optional[str] = None

@dataclass
class PortfolioOptimizationResponse:
    """Portfolio optimization response."""
    optimization_result: OptimizationResult
    performance_metrics: Optional[PerformanceMetrics] = None
    rebalancing_analysis: Optional[Dict[str, Any]] = None
    risk_analysis: Optional[Dict[str, Any]] = None
    attribution_analysis: Optional[Dict[str, Any]] = None
    recommendations: List[str] = None

class PortfolioOptimizer:
    """Main portfolio optimization engine."""
    
    def __init__(self):
        """Initialize portfolio optimizer."""
        self.supported_methods = {
            OptimizationMethod.MAX_SHARPE: self._optimize_max_sharpe,
            OptimizationMethod.MIN_VARIANCE: self._optimize_min_variance,
            OptimizationMethod.TARGET_RETURN: self._optimize_target_return,
            OptimizationMethod.RISK_PARITY_ERC: self._optimize_risk_parity_erc,
            OptimizationMethod.RISK_PARITY_HRP: self._optimize_risk_parity_hrp,
            OptimizationMethod.BLACK_LITTERMAN: self._optimize_black_litterman,
            OptimizationMethod.EQUAL_WEIGHT: self._optimize_equal_weight
        }
        
        logger.info("Portfolio Optimizer initialized")
    
    def optimize_portfolio(
        self, 
        request: PortfolioOptimizationRequest
    ) -> PortfolioOptimizationResponse:
        """Main portfolio optimization method."""
        try:
            # Validate request
            self._validate_request(request)
            
            # Get optimization method
            if request.method not in self.supported_methods:
                raise ValueError(f"Unsupported optimization method: {request.method}")
            
            optimization_method = self.supported_methods[request.method]
            
            # Execute optimization
            optimization_result = optimization_method(request)
            
            # Calculate performance metrics
            performance_metrics = self._calculate_performance_metrics(
                optimization_result, request
            )
            
            # Risk analysis
            risk_analysis = self._perform_risk_analysis(
                optimization_result, request
            )
            
            # Rebalancing analysis
            rebalancing_analysis = None
            if request.rebalancing_config:
                rebalancing_analysis = self._analyze_rebalancing(
                    optimization_result, request
                )
            
            # Generate recommendations
            recommendations = self._generate_recommendations(
                optimization_result, performance_metrics, risk_analysis
            )
            
            response = PortfolioOptimizationResponse(
                optimization_result=optimization_result,
                performance_metrics=performance_metrics,
                rebalancing_analysis=rebalancing_analysis,
                risk_analysis=risk_analysis,
                recommendations=recommendations
            )
            
            logger.info(f"Portfolio optimization completed: {request.method.value}")
            
            return response
            
        except Exception as e:
            logger.error(f"Error in portfolio optimization: {e}")
            raise
    
    def compare_optimization_methods(
        self,
        symbols: List[str],
        methods: List[OptimizationMethod],
        constraints: Optional[PortfolioConstraints] = None,
        lookback_days: int = 252
    ) -> Dict[str, PortfolioOptimizationResponse]:
        """Compare multiple optimization methods."""
        try:
            results = {}
            
            for method in methods:
                try:
                    request = PortfolioOptimizationRequest(
                        symbols=symbols,
                        method=method,
                        constraints=constraints,
                        lookback_days=lookback_days
                    )
                    
                    response = self.optimize_portfolio(request)
                    results[method.value] = response
                    
                except Exception as e:
                    logger.error(f"Error optimizing with method {method.value}: {e}")
                    continue
            
            logger.info(f"Compared {len(results)} optimization methods")
            
            return results
            
        except Exception as e:
            logger.error(f"Error comparing optimization methods: {e}")
            return {}
    
    def _validate_request(self, request: PortfolioOptimizationRequest):
        """Validate optimization request."""
        if not request.symbols:
            raise ValueError("No symbols provided")
        
        if len(request.symbols) < 2:
            raise ValueError("At least 2 symbols required for optimization")
        
        if request.method == OptimizationMethod.TARGET_RETURN and request.target_return is None:
            raise ValueError("Target return required for target_return method")
        
        if request.method == OptimizationMethod.BLACK_LITTERMAN and not request.investor_views:
            raise ValueError("Investor views required for Black-Litterman method")
    
    def _optimize_max_sharpe(self, request: PortfolioOptimizationRequest) -> OptimizationResult:
        """Optimize for maximum Sharpe ratio."""
        return mpt_optimizer.optimize_portfolio(
            request.symbols,
            "max_sharpe",
            request.constraints,
            lookback_days=request.lookback_days
        )
    
    def _optimize_min_variance(self, request: PortfolioOptimizationRequest) -> OptimizationResult:
        """Optimize for minimum variance."""
        return mpt_optimizer.optimize_portfolio(
            request.symbols,
            "min_variance",
            request.constraints,
            lookback_days=request.lookback_days
        )
    
    def _optimize_target_return(self, request: PortfolioOptimizationRequest) -> OptimizationResult:
        """Optimize for target return."""
        return mpt_optimizer.optimize_portfolio(
            request.symbols,
            "target_return",
            request.constraints,
            target_return=request.target_return,
            lookback_days=request.lookback_days
        )
    
    def _optimize_risk_parity_erc(self, request: PortfolioOptimizationRequest) -> OptimizationResult:
        """Optimize using Equal Risk Contribution."""
        return risk_parity_optimizer.optimize_risk_parity(
            request.symbols,
            "erc",
            request.constraints,
            request.lookback_days
        )
    
    def _optimize_risk_parity_hrp(self, request: PortfolioOptimizationRequest) -> OptimizationResult:
        """Optimize using Hierarchical Risk Parity."""
        return risk_parity_optimizer.optimize_risk_parity(
            request.symbols,
            "hrp",
            request.constraints,
            request.lookback_days
        )
    
    def _optimize_black_litterman(self, request: PortfolioOptimizationRequest) -> OptimizationResult:
        """Optimize using Black-Litterman model."""
        bl_result = bl_optimizer.optimize_with_views(
            request.symbols,
            request.investor_views,
            request.constraints,
            lookback_days=request.lookback_days
        )
        return bl_result.optimization_result
    
    def _optimize_equal_weight(self, request: PortfolioOptimizationRequest) -> OptimizationResult:
        """Create equal-weight portfolio."""
        n_assets = len(request.symbols)
        equal_weights = np.array([1.0 / n_assets] * n_assets)
        
        # Calculate expected returns and covariance for metrics
        expected_returns, covariance_matrix = mpt_optimizer.calculate_returns_covariance(
            request.symbols, request.lookback_days
        )
        
        # Calculate portfolio metrics
        portfolio_return = np.dot(equal_weights, expected_returns)
        portfolio_variance = np.dot(equal_weights.T, np.dot(covariance_matrix, equal_weights))
        portfolio_volatility = np.sqrt(portfolio_variance)
        sharpe_ratio = (portfolio_return - mpt_optimizer.risk_free_rate) / portfolio_volatility
        
        return OptimizationResult(
            weights=equal_weights,
            expected_return=portfolio_return,
            volatility=portfolio_volatility,
            sharpe_ratio=sharpe_ratio,
            symbols=request.symbols,
            optimization_method="equal_weight",
            constraints_satisfied=True,
            optimization_success=True,
            additional_metrics={
                'equal_weight': True,
                'diversification_ratio': 1.0
            }
        )
    
    def _calculate_performance_metrics(
        self,
        optimization_result: OptimizationResult,
        request: PortfolioOptimizationRequest
    ) -> Optional[PerformanceMetrics]:
        """Calculate performance metrics for optimized portfolio."""
        try:
            # Get historical returns for backtesting
            returns_data = []
            
            with db_manager.get_session() as db:
                for symbol in optimization_result.symbols:
                    stock = StockCRUD.get_stock_by_symbol(db, symbol)
                    if not stock:
                        continue
                    
                    prices = PriceCRUD.get_recent_prices(db, stock.id, request.lookback_days + 1)
                    
                    if len(prices) < request.lookback_days:
                        continue
                    
                    price_series = [float(p.close_price) for p in reversed(prices)]
                    returns = np.diff(price_series) / price_series[:-1]
                    returns_data.append(returns)
            
            if len(returns_data) < 2:
                return None
            
            # Calculate portfolio returns
            returns_matrix = np.array(returns_data).T
            portfolio_returns = performance_analytics.calculate_portfolio_returns(
                optimization_result.weights, returns_matrix
            )
            
            # Get benchmark returns if specified
            benchmark_returns = None
            if request.benchmark_symbol:
                with db_manager.get_session() as db:
                    benchmark_stock = StockCRUD.get_stock_by_symbol(db, request.benchmark_symbol)
                    if benchmark_stock:
                        benchmark_prices = PriceCRUD.get_recent_prices(
                            db, benchmark_stock.id, request.lookback_days + 1
                        )
                        
                        if len(benchmark_prices) >= request.lookback_days:
                            benchmark_price_series = [float(p.close_price) for p in reversed(benchmark_prices)]
                            benchmark_returns = np.diff(benchmark_price_series) / benchmark_price_series[:-1]
            
            # Calculate performance metrics
            performance_metrics = performance_analytics.calculate_performance_metrics(
                portfolio_returns, benchmark_returns
            )
            
            return performance_metrics
            
        except Exception as e:
            logger.error(f"Error calculating performance metrics: {e}")
            return None
    
    def _perform_risk_analysis(
        self,
        optimization_result: OptimizationResult,
        request: PortfolioOptimizationRequest
    ) -> Dict[str, Any]:
        """Perform comprehensive risk analysis."""
        try:
            # Get covariance matrix
            expected_returns, covariance_matrix = mpt_optimizer.calculate_returns_covariance(
                optimization_result.symbols, request.lookback_days
            )
            
            # Risk decomposition
            risk_decomposition = performance_analytics.calculate_risk_decomposition(
                optimization_result.weights, covariance_matrix, optimization_result.symbols
            )
            
            # Concentration analysis
            concentration_metrics = {
                'herfindahl_index': np.sum(optimization_result.weights ** 2),
                'effective_positions': np.sum(optimization_result.weights > 0.01),
                'max_weight': np.max(optimization_result.weights),
                'min_weight': np.min(optimization_result.weights),
                'weight_std': np.std(optimization_result.weights)
            }
            
            # Sector analysis
            sector_exposure = {}
            with db_manager.get_session() as db:
                for i, symbol in enumerate(optimization_result.symbols):
                    stock = StockCRUD.get_stock_by_symbol(db, symbol)
                    if stock and stock.sector:
                        sector = stock.sector
                        if sector not in sector_exposure:
                            sector_exposure[sector] = 0.0
                        sector_exposure[sector] += optimization_result.weights[i]
            
            risk_analysis = {
                'risk_decomposition': risk_decomposition,
                'concentration_metrics': concentration_metrics,
                'sector_exposure': sector_exposure,
                'portfolio_volatility': optimization_result.volatility,
                'expected_return': optimization_result.expected_return,
                'sharpe_ratio': optimization_result.sharpe_ratio
            }
            
            return risk_analysis
            
        except Exception as e:
            logger.error(f"Error in risk analysis: {e}")
            return {}
    
    def _analyze_rebalancing(
        self,
        optimization_result: OptimizationResult,
        request: PortfolioOptimizationRequest
    ) -> Dict[str, Any]:
        """Analyze rebalancing implications."""
        try:
            # Create target weights dictionary
            target_weights = {
                symbol: weight for symbol, weight in 
                zip(optimization_result.symbols, optimization_result.weights)
            }
            
            # Simulate current weights (equal weight as baseline)
            current_weights = {symbol: 1.0/len(optimization_result.symbols) for symbol in optimization_result.symbols}
            
            # Analyze rebalancing
            portfolio_value = 1000000  # ₹10L portfolio
            
            rebalancing_result = portfolio_rebalancer.execute_rebalancing(
                current_weights,
                target_weights,
                portfolio_value,
                request.rebalancing_config
            )
            
            if rebalancing_result:
                return {
                    'rebalancing_needed': True,
                    'total_turnover': rebalancing_result.total_turnover,
                    'transaction_cost': rebalancing_result.total_transaction_cost,
                    'number_of_trades': len(rebalancing_result.actions),
                    'expected_improvement': rebalancing_result.expected_improvement
                }
            else:
                return {
                    'rebalancing_needed': False,
                    'reason': 'No significant rebalancing required'
                }
            
        except Exception as e:
            logger.error(f"Error in rebalancing analysis: {e}")
            return {}
    
    def _generate_recommendations(
        self,
        optimization_result: OptimizationResult,
        performance_metrics: Optional[PerformanceMetrics],
        risk_analysis: Dict[str, Any]
    ) -> List[str]:
        """Generate portfolio recommendations."""
        try:
            recommendations = []
            
            # Concentration recommendations
            if 'concentration_metrics' in risk_analysis:
                concentration = risk_analysis['concentration_metrics']
                
                if concentration['herfindahl_index'] > 0.3:
                    recommendations.append("Portfolio is highly concentrated - consider diversification")
                
                if concentration['effective_positions'] < 5:
                    recommendations.append("Low number of effective positions - consider adding more assets")
                
                if concentration['max_weight'] > 0.2:
                    recommendations.append("Single asset weight exceeds 20% - consider position size limits")
            
            # Performance recommendations
            if performance_metrics:
                if performance_metrics.sharpe_ratio < 0.5:
                    recommendations.append("Low Sharpe ratio - consider risk-return optimization")
                
                if performance_metrics.max_drawdown < -0.15:
                    recommendations.append("High maximum drawdown - implement risk management measures")
                
                if performance_metrics.volatility > 0.25:
                    recommendations.append("High portfolio volatility - consider risk reduction strategies")
            
            # Optimization method recommendations
            if optimization_result.optimization_method == "equal_weight":
                recommendations.append("Consider using optimization methods for better risk-return profile")
            
            if not optimization_result.optimization_success:
                recommendations.append("Optimization constraints may be too restrictive - review constraints")
            
            # Sector diversification
            if 'sector_exposure' in risk_analysis:
                sector_exposure = risk_analysis['sector_exposure']
                max_sector_exposure = max(sector_exposure.values()) if sector_exposure else 0
                
                if max_sector_exposure > 0.4:
                    recommendations.append("High sector concentration detected - diversify across sectors")
            
            if not recommendations:
                recommendations.append("Portfolio appears well-optimized within given constraints")
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")
            return ["Error generating recommendations"]

# Global portfolio optimizer instance
portfolio_optimizer = PortfolioOptimizer()
