"""Database initialization and setup utilities."""

import pandas as pd
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any

from .database import db_manager
from .models import StockMetadata
from .crud import StockCRUD
from ..utils.logger import get_logger
from ..utils.config import config

logger = get_logger(__name__)

class DatabaseInitializer:
    """Initialize database with stock metadata and setup."""
    
    def __init__(self):
        self.db_manager = db_manager
    
    def create_tables(self):
        """Create all database tables."""
        try:
            self.db_manager.create_tables()
            logger.info("Database tables created successfully")
        except Exception as e:
            logger.error(f"Error creating tables: {e}")
            raise
    
    def load_stock_metadata(self, csv_path: str = None):
        """Load stock metadata from EQUITY_L.csv file."""
        if csv_path is None:
            csv_path = Path(__file__).parent.parent.parent / "EQUITY_L.csv"
        
        try:
            # Read the CSV file
            df = pd.read_csv(csv_path)
            logger.info(f"Loaded {len(df)} stocks from {csv_path}")
            
            # Clean column names
            df.columns = df.columns.str.strip()
            
            # Define Nifty 50 stocks (as of 2024)
            nifty50_symbols = {
                'ADANIENT', 'ADANIPORTS', 'APOLLOHOSP', 'ASIANPAINT', 'AXISBANK',
                'BAJAJ-AUTO', 'BAJFINANCE', 'BAJAJFINSV', 'BPCL', 'BHARTIARTL',
                'BRITANNIA', 'CIPLA', 'COALINDIA', 'DIVISLAB', 'DRREDDY',
                'EICHERMOT', 'GRASIM', 'HCLTECH', 'HDFCBANK', 'HDFCLIFE',
                'HEROMOTOCO', 'HINDALCO', 'HINDUNILVR', 'ICICIBANK', 'ITC',
                'INDUSINDBK', 'INFY', 'JSWSTEEL', 'KOTAKBANK', 'LT',
                'M&M', 'MARUTI', 'NTPC', 'NESTLEIND', 'ONGC',
                'POWERGRID', 'RELIANCE', 'SBILIFE', 'SBIN', 'SUNPHARMA',
                'TCS', 'TATACONSUM', 'TATAMOTORS', 'TATASTEEL', 'TECHM',
                'TITAN', 'ULTRACEMCO', 'UPL', 'WIPRO', 'ZEEL'
            }
            
            stocks_created = 0
            stocks_updated = 0
            
            with self.db_manager.get_session() as db:
                for _, row in df.iterrows():
                    try:
                        # Parse listing date
                        listing_date = pd.to_datetime(row['DATE OF LISTING'], format='%d-%b-%Y').date()
                        
                        # Check if stock already exists
                        existing_stock = StockCRUD.get_stock_by_symbol(db, row['SYMBOL'])
                        
                        stock_data = {
                            'symbol': row['SYMBOL'],
                            'company_name': row['NAME OF COMPANY'].strip(),
                            'series': row['SERIES'],
                            'isin': row['ISIN NUMBER'],
                            'face_value': float(row['FACE VALUE']),
                            'market_lot': int(row['MARKET LOT']),
                            'listing_date': listing_date,
                            'is_active': True,
                            'is_nifty50': row['SYMBOL'] in nifty50_symbols,
                            'is_nifty100': False,  # Will be updated later
                            'is_nifty500': False,  # Will be updated later
                        }
                        
                        if existing_stock:
                            StockCRUD.update_stock(db, existing_stock.id, stock_data)
                            stocks_updated += 1
                        else:
                            StockCRUD.create_stock(db, stock_data)
                            stocks_created += 1
                            
                    except Exception as e:
                        logger.error(f"Error processing stock {row['SYMBOL']}: {e}")
                        continue
                
                db.commit()
            
            logger.info(f"Stock metadata loaded: {stocks_created} created, {stocks_updated} updated")
            
        except Exception as e:
            logger.error(f"Error loading stock metadata: {e}")
            raise
    
    def setup_indexes(self):
        """Create additional database indexes for performance."""
        try:
            with self.db_manager.get_session() as db:
                # Additional indexes can be created here if needed
                # Most indexes are defined in the model classes
                pass
            
            logger.info("Database indexes created successfully")
            
        except Exception as e:
            logger.error(f"Error creating indexes: {e}")
            raise
    
    def verify_setup(self) -> Dict[str, Any]:
        """Verify database setup and return statistics."""
        try:
            with self.db_manager.get_session() as db:
                # Count stocks
                total_stocks = db.query(StockMetadata).count()
                active_stocks = db.query(StockMetadata).filter(StockMetadata.is_active == True).count()
                nifty50_stocks = db.query(StockMetadata).filter(StockMetadata.is_nifty50 == True).count()
                
                stats = {
                    'total_stocks': total_stocks,
                    'active_stocks': active_stocks,
                    'nifty50_stocks': nifty50_stocks,
                    'database_connection': self.db_manager.test_connection(),
                    'setup_complete': True
                }
                
                logger.info(f"Database verification: {stats}")
                return stats
                
        except Exception as e:
            logger.error(f"Error verifying database setup: {e}")
            return {'setup_complete': False, 'error': str(e)}
    
    def initialize_database(self):
        """Complete database initialization process."""
        logger.info("Starting database initialization...")
        
        try:
            # Test connection
            if not self.db_manager.test_connection():
                raise Exception("Database connection failed")
            
            # Create tables
            self.create_tables()
            
            # Load stock metadata
            self.load_stock_metadata()
            
            # Setup additional indexes
            self.setup_indexes()
            
            # Verify setup
            stats = self.verify_setup()
            
            if stats['setup_complete']:
                logger.info("Database initialization completed successfully")
                return stats
            else:
                raise Exception("Database verification failed")
                
        except Exception as e:
            logger.error(f"Database initialization failed: {e}")
            raise

def init_database():
    """Initialize database - can be called from command line."""
    initializer = DatabaseInitializer()
    return initializer.initialize_database()

if __name__ == "__main__":
    init_database()
