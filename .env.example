# Database Configuration
DB_PASSWORD=your_postgres_password
DB_HOST=localhost
DB_PORT=5432
DB_NAME=stock_analyzer
DB_USER=postgres

# API Keys
BLOOMBERG_API_KEY=your_bloomberg_api_key
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key

# Email Configuration
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_app_password

# SMS Configuration (Twilio)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_FROM_NUMBER=+**********

# Environment
ENVIRONMENT=development
DEBUG=true

# Security
SECRET_KEY=your_secret_key_here
JWT_SECRET=your_jwt_secret_here

# Redis (for caching and task queue)
REDIS_URL=redis://localhost:6379/0

# Monitoring
PROMETHEUS_PORT=9090
