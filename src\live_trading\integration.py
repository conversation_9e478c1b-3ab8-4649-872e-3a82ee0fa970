"""
Live Trading Integration - Integrates live trading with main stock analysis system.
Provides seamless integration with strategies, signals, and portfolio management.
"""

import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import json

from .trading_engine import trading_engine, TradingMode, initialize_trading_engine
from .broker_integration import get_broker
from .order_manager import order_manager
from .risk_manager import risk_manager
from ..strategies.strategy_registry import strategy_registry
from ..data.database import db_manager
from ..data.crud import SignalCRUD, StockCRUD
from ..data.models import StrategySignal
from ..utils.logger import get_logger
from ..utils.config import config

logger = get_logger(__name__)

class LiveTradingIntegration:
    """Integration layer for live trading system."""
    
    def __init__(self):
        """Initialize live trading integration."""
        self.is_initialized = False
        self.auto_trading_enabled = False
        self.signal_generation_enabled = True
        
        # Configuration
        self.config = config.get_trading_config()
        self.signal_generation_interval = self.config.get('signal_generation_interval', 300)  # 5 minutes
        
        # Performance tracking
        self.integration_stats = {
            'signals_generated': 0,
            'signals_processed': 0,
            'orders_placed': 0,
            'successful_trades': 0,
            'failed_trades': 0,
            'total_pnl': 0.0
        }
        
        logger.info("Live Trading Integration initialized")
    
    async def initialize(self) -> bool:
        """Initialize live trading integration."""
        try:
            if self.is_initialized:
                return True
            
            logger.info("Initializing live trading integration...")
            
            # Initialize trading engine
            engine_ready = await initialize_trading_engine()
            if not engine_ready:
                logger.error("Failed to initialize trading engine")
                return False
            
            # Register callbacks
            trading_engine.register_signal_callback(self._on_signal_processed)
            
            # Start background tasks
            asyncio.create_task(self._signal_generation_loop())
            asyncio.create_task(self._performance_monitoring_loop())
            
            self.is_initialized = True
            logger.info("Live trading integration initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing live trading integration: {e}")
            return False
    
    async def start_auto_trading(self, mode: TradingMode = TradingMode.PAPER) -> Dict[str, Any]:
        """Start automatic trading."""
        try:
            if not self.is_initialized:
                return {'success': False, 'message': 'Integration not initialized'}
            
            # Start trading engine
            success = await trading_engine.start_trading(mode)
            
            if success:
                self.auto_trading_enabled = True
                
                return {
                    'success': True,
                    'message': f'Auto trading started in {mode.value} mode',
                    'mode': mode.value,
                    'timestamp': datetime.now().isoformat()
                }
            else:
                return {
                    'success': False,
                    'message': 'Failed to start trading engine'
                }
                
        except Exception as e:
            logger.error(f"Error starting auto trading: {e}")
            return {
                'success': False,
                'message': f'Error: {str(e)}'
            }
    
    async def stop_auto_trading(self) -> Dict[str, Any]:
        """Stop automatic trading."""
        try:
            success = await trading_engine.stop_trading()
            
            if success:
                self.auto_trading_enabled = False
                
                return {
                    'success': True,
                    'message': 'Auto trading stopped',
                    'timestamp': datetime.now().isoformat()
                }
            else:
                return {
                    'success': False,
                    'message': 'Failed to stop trading engine'
                }
                
        except Exception as e:
            logger.error(f"Error stopping auto trading: {e}")
            return {
                'success': False,
                'message': f'Error: {str(e)}'
            }
    
    async def generate_signals_for_all_strategies(self) -> Dict[str, Any]:
        """Generate signals for all enabled strategies."""
        try:
            if not self.signal_generation_enabled:
                return {
                    'success': False,
                    'message': 'Signal generation is disabled'
                }
            
            # Get enabled strategies
            enabled_strategies = strategy_registry.get_enabled_strategies()
            
            if not enabled_strategies:
                return {
                    'success': False,
                    'message': 'No strategies enabled'
                }
            
            # Get Nifty 50 stocks for signal generation
            with db_manager.get_session() as db:
                nifty50_stocks = StockCRUD.get_nifty50_stocks(db)
                symbols = [stock.symbol for stock in nifty50_stocks]
            
            total_signals = 0
            strategy_results = {}
            
            # Generate signals for each strategy
            for strategy_name, strategy_class in enabled_strategies.items():
                try:
                    strategy_signals = 0
                    
                    # Create strategy instance
                    strategy = strategy_registry.create_strategy(strategy_name)
                    
                    # Get RL-optimized parameters
                    from ..rl_optimizer.rl_integration import rl_integration
                    
                    for symbol in symbols:
                        try:
                            # Get optimized parameters for this symbol
                            rl_params = rl_integration.get_optimized_parameters_for_strategy(
                                strategy_name, symbol
                            )
                            
                            if rl_params:
                                strategy.update_parameters(rl_params)
                            
                            # Generate signals
                            signals = strategy.calculate_signals(symbol)
                            
                            # Store signals in database
                            for signal in signals:
                                with db_manager.get_session() as db:
                                    signal_data = {
                                        'symbol': signal.symbol,
                                        'strategy_name': strategy_name,
                                        'signal_type': signal.signal_type.value,
                                        'signal_strength': signal.signal_strength,
                                        'price': signal.price,
                                        'target_price': signal.target_price,
                                        'stop_loss': signal.stop_loss,
                                        'confidence_score': signal.confidence_score,
                                        'signal_date': signal.signal_date,
                                        'signal_time': signal.signal_time,
                                        'parameters': json.dumps(rl_params) if rl_params else None,
                                        'is_valid': True,
                                        'processed': False
                                    }
                                    
                                    SignalCRUD.create_signal(db, signal_data)
                                    strategy_signals += 1
                                    total_signals += 1
                            
                        except Exception as e:
                            logger.error(f"Error generating signals for {strategy_name} on {symbol}: {e}")
                    
                    strategy_results[strategy_name] = {
                        'signals_generated': strategy_signals,
                        'status': 'success'
                    }
                    
                except Exception as e:
                    logger.error(f"Error with strategy {strategy_name}: {e}")
                    strategy_results[strategy_name] = {
                        'signals_generated': 0,
                        'status': 'error',
                        'error': str(e)
                    }
            
            self.integration_stats['signals_generated'] += total_signals
            
            return {
                'success': True,
                'message': f'Generated {total_signals} signals across {len(enabled_strategies)} strategies',
                'total_signals': total_signals,
                'strategy_results': strategy_results,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error generating signals: {e}")
            return {
                'success': False,
                'message': f'Error: {str(e)}'
            }
    
    async def get_live_portfolio_status(self) -> Dict[str, Any]:
        """Get live portfolio status."""
        try:
            broker = get_broker()
            
            # Get positions, holdings, and balance
            positions = await broker.get_positions()
            holdings = await broker.get_holdings()
            balance = await broker.get_balance()
            
            # Calculate portfolio metrics
            total_value = balance.get('available_cash', 0)
            total_pnl = 0
            position_count = 0
            
            positions_data = []
            for pos in positions:
                if abs(pos.quantity) > 0:
                    position_count += 1
                    total_value += pos.current_price * abs(pos.quantity)
                    total_pnl += pos.pnl
                    
                    positions_data.append({
                        'symbol': pos.symbol,
                        'quantity': pos.quantity,
                        'average_price': pos.average_price,
                        'current_price': pos.current_price,
                        'pnl': pos.pnl,
                        'pnl_percent': pos.pnl_percent,
                        'side': pos.side
                    })
            
            holdings_data = []
            for holding in holdings:
                if holding.quantity > 0:
                    total_value += holding.current_price * holding.quantity
                    total_pnl += holding.pnl
                    
                    holdings_data.append({
                        'symbol': holding.symbol,
                        'quantity': holding.quantity,
                        'average_price': holding.average_price,
                        'current_price': holding.current_price,
                        'pnl': holding.pnl,
                        'pnl_percent': holding.pnl_percent
                    })
            
            # Get risk metrics
            portfolio_risk = await risk_manager.get_portfolio_risk()
            risk_violations = await risk_manager.get_risk_violations()
            
            return {
                'success': True,
                'portfolio': {
                    'total_value': total_value,
                    'available_cash': balance.get('available_cash', 0),
                    'total_pnl': total_pnl,
                    'total_pnl_percent': (total_pnl / total_value * 100) if total_value > 0 else 0,
                    'position_count': position_count,
                    'holdings_count': len(holdings_data)
                },
                'positions': positions_data,
                'holdings': holdings_data,
                'risk_metrics': {
                    'current_drawdown': portfolio_risk.current_drawdown if portfolio_risk else 0,
                    'portfolio_var': portfolio_risk.portfolio_var if portfolio_risk else 0,
                    'risk_violations': len(risk_violations)
                },
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting portfolio status: {e}")
            return {
                'success': False,
                'message': f'Error: {str(e)}'
            }
    
    async def get_trading_performance(self, days: int = 30) -> Dict[str, Any]:
        """Get trading performance metrics."""
        try:
            # Get recent orders
            all_orders = await order_manager.get_all_orders()
            completed_orders = all_orders['completed']
            
            # Filter orders by date
            cutoff_date = datetime.now() - timedelta(days=days)
            recent_orders = [
                order for order in completed_orders 
                if order.timestamp and order.timestamp >= cutoff_date
            ]
            
            # Calculate performance metrics
            total_orders = len(recent_orders)
            successful_orders = len([o for o in recent_orders if o.status.value == 'COMPLETE'])
            
            # Get portfolio performance
            portfolio_risk = await risk_manager.get_portfolio_risk()
            
            performance_data = {
                'period_days': days,
                'total_orders': total_orders,
                'successful_orders': successful_orders,
                'success_rate': (successful_orders / total_orders * 100) if total_orders > 0 else 0,
                'portfolio_value': portfolio_risk.total_value if portfolio_risk else 0,
                'total_pnl': portfolio_risk.total_pnl if portfolio_risk else 0,
                'daily_pnl': portfolio_risk.daily_pnl if portfolio_risk else 0,
                'max_drawdown': portfolio_risk.max_drawdown if portfolio_risk else 0,
                'sharpe_ratio': portfolio_risk.sharpe_ratio if portfolio_risk else 0,
                'integration_stats': self.integration_stats
            }
            
            return {
                'success': True,
                'performance': performance_data,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting trading performance: {e}")
            return {
                'success': False,
                'message': f'Error: {str(e)}'
            }
    
    async def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status."""
        try:
            # Get trading engine status
            trading_status = await trading_engine.get_trading_status()
            
            # Get broker connection status
            broker = get_broker()
            broker_connected = broker.is_connected if hasattr(broker, 'is_connected') else True
            
            # Get component statistics
            order_stats = order_manager.get_statistics()
            
            return {
                'success': True,
                'system_status': {
                    'integration_initialized': self.is_initialized,
                    'auto_trading_enabled': self.auto_trading_enabled,
                    'signal_generation_enabled': self.signal_generation_enabled,
                    'broker_connected': broker_connected,
                    'trading_engine': trading_status,
                    'order_manager': order_stats,
                    'integration_stats': self.integration_stats
                },
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting system status: {e}")
            return {
                'success': False,
                'message': f'Error: {str(e)}'
            }
    
    async def _signal_generation_loop(self):
        """Automatic signal generation loop."""
        while True:
            try:
                if self.signal_generation_enabled and self.auto_trading_enabled:
                    # Generate signals for all strategies
                    result = await self.generate_signals_for_all_strategies()
                    
                    if result['success']:
                        logger.info(f"Auto-generated {result['total_signals']} signals")
                    else:
                        logger.warning(f"Signal generation failed: {result['message']}")
                
                await asyncio.sleep(self.signal_generation_interval)
                
            except Exception as e:
                logger.error(f"Error in signal generation loop: {e}")
                await asyncio.sleep(600)  # Wait 10 minutes on error
    
    async def _performance_monitoring_loop(self):
        """Performance monitoring loop."""
        while True:
            try:
                if self.auto_trading_enabled:
                    # Update integration statistics
                    trading_status = await trading_engine.get_trading_status()
                    
                    if 'daily_stats' in trading_status:
                        daily_stats = trading_status['daily_stats']
                        self.integration_stats.update({
                            'orders_placed': daily_stats.get('orders_placed', 0),
                            'successful_trades': daily_stats.get('successful_trades', 0),
                            'failed_trades': daily_stats.get('failed_trades', 0),
                            'total_pnl': daily_stats.get('total_pnl', 0.0)
                        })
                
                await asyncio.sleep(300)  # Update every 5 minutes
                
            except Exception as e:
                logger.error(f"Error in performance monitoring loop: {e}")
                await asyncio.sleep(600)
    
    async def _on_signal_processed(self, signal: StrategySignal, order_id: str):
        """Callback for when a signal is processed."""
        try:
            self.integration_stats['signals_processed'] += 1
            logger.info(f"Signal processed: {signal.symbol} {signal.signal_type} -> Order: {order_id}")
            
        except Exception as e:
            logger.error(f"Error in signal processed callback: {e}")

# Global live trading integration instance
live_trading_integration = LiveTradingIntegration()

async def initialize_live_trading() -> bool:
    """Initialize live trading system."""
    return await live_trading_integration.initialize()

async def start_auto_trading(mode: TradingMode = TradingMode.PAPER) -> Dict[str, Any]:
    """Start automatic trading."""
    return await live_trading_integration.start_auto_trading(mode)

async def stop_auto_trading() -> Dict[str, Any]:
    """Stop automatic trading."""
    return await live_trading_integration.stop_auto_trading()

def get_live_trading_integration() -> LiveTradingIntegration:
    """Get live trading integration instance."""
    return live_trading_integration
