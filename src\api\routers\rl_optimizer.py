"""
RL Optimizer Router - Handles reinforcement learning optimization operations.
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from typing import List, Optional, Dict, Any

from ..models import (
    APIResponse, RLTrainingRequest, RLModelStatus, 
    ParameterOptimizationRequest, PaginationParams
)
from ..main import get_current_user
from ..websocket import websocket_manager
from ...rl_optimizer.rl_integration import rl_integration
from ...rl_optimizer.multi_agent_manager import multi_agent_manager
from ...data.database import db_manager
from ...data.crud import RLModelCRUD, RLTrainingHistoryCRUD, RLParameterAdaptationCRUD
from ...utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter()

@router.get("/status", response_model=APIResponse)
async def get_rl_system_status(current_user: dict = Depends(get_current_user)):
    """Get RL system status."""
    try:
        status = rl_integration.get_rl_system_status()
        
        return APIResponse(
            success=True,
            message="RL system status retrieved",
            data=status
        )
        
    except Exception as e:
        logger.error(f"Error getting RL system status: {e}")
        raise HTTPException(status_code=500, detail="Failed to get RL system status")

@router.get("/models", response_model=APIResponse)
async def get_rl_models(current_user: dict = Depends(get_current_user)):
    """Get all RL models."""
    try:
        with db_manager.get_session() as db:
            models = RLModelCRUD.get_all_active_models(db)
            
            models_data = []
            for model in models:
                model_dict = {
                    'id': model.id,
                    'strategy_name': model.strategy_name,
                    'model_type': model.model_type,
                    'is_active': model.is_active,
                    'version': model.version,
                    'training_iterations': model.training_iterations,
                    'avg_reward': float(model.avg_reward),
                    'created_at': model.created_at.isoformat(),
                    'last_trained': model.last_trained.isoformat() if model.last_trained else None,
                    'performance_metrics': model.performance_metrics
                }
                models_data.append(model_dict)
            
            return APIResponse(
                success=True,
                message="RL models retrieved",
                data={
                    "models": models_data,
                    "total": len(models_data)
                }
            )
            
    except Exception as e:
        logger.error(f"Error getting RL models: {e}")
        raise HTTPException(status_code=500, detail="Failed to get RL models")

@router.get("/models/{strategy_name}", response_model=APIResponse)
async def get_rl_model_details(
    strategy_name: str,
    current_user: dict = Depends(get_current_user)
):
    """Get RL model details for specific strategy."""
    try:
        with db_manager.get_session() as db:
            model = RLModelCRUD.get_active_model(db, strategy_name)
            
            if not model:
                raise HTTPException(status_code=404, detail="RL model not found")
            
            # Get training history
            training_history = RLTrainingHistoryCRUD.get_training_history(
                db, strategy_name, limit=20
            )
            
            # Get recent adaptations
            recent_adaptations = RLParameterAdaptationCRUD.get_recent_adaptations(
                db, strategy_name, days=7
            )
            
            model_data = {
                'id': model.id,
                'strategy_name': model.strategy_name,
                'model_type': model.model_type,
                'model_path': model.model_path,
                'parameters': model.parameters,
                'hyperparameters': model.hyperparameters,
                'performance_metrics': model.performance_metrics,
                'training_iterations': model.training_iterations,
                'avg_reward': float(model.avg_reward),
                'is_active': model.is_active,
                'version': model.version,
                'created_at': model.created_at.isoformat(),
                'last_trained': model.last_trained.isoformat() if model.last_trained else None,
                'training_history': [{
                    'training_date': th.training_date.isoformat(),
                    'training_iterations': th.training_iterations,
                    'avg_reward': float(th.avg_reward),
                    'training_time_minutes': th.training_time_minutes
                } for th in training_history],
                'recent_adaptations': [{
                    'adaptation_date': ra.adaptation_date.isoformat(),
                    'stock_id': ra.stock_id,
                    'adaptation_reason': ra.adaptation_reason,
                    'expected_improvement': float(ra.expected_improvement) if ra.expected_improvement else None
                } for ra in recent_adaptations]
            }
            
            return APIResponse(
                success=True,
                message="RL model details retrieved",
                data=model_data
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting RL model details: {e}")
        raise HTTPException(status_code=500, detail="Failed to get RL model details")

@router.post("/train", response_model=APIResponse)
async def train_rl_models(
    training_request: RLTrainingRequest,
    background_tasks: BackgroundTasks,
    current_user: dict = Depends(get_current_user)
):
    """Train RL models."""
    try:
        if not current_user.get('is_admin', False):
            raise HTTPException(status_code=403, detail="Admin access required")
        
        # Start training in background
        background_tasks.add_task(
            _run_rl_training,
            training_request,
            current_user['id']
        )
        
        return APIResponse(
            success=True,
            message="RL training initiated",
            data={
                "strategy_name": training_request.strategy_name or "all",
                "num_iterations": training_request.num_iterations,
                "episodes_per_iteration": training_request.episodes_per_iteration,
                "status": "started"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting RL training: {e}")
        raise HTTPException(status_code=500, detail="Failed to start RL training")

async def _run_rl_training(request: RLTrainingRequest, user_id: int):
    """Run RL training in background."""
    try:
        # Send start notification
        await websocket_manager.send_rl_training_update({
            'status': 'started',
            'strategy_name': request.strategy_name or 'all',
            'user_id': user_id
        })
        
        if request.strategy_name:
            # Train specific strategy
            result = await rl_integration.retrain_strategy(
                request.strategy_name,
                request.num_iterations
            )
        else:
            # Train all strategies
            await multi_agent_manager.train_all_agents(
                request.num_iterations,
                request.episodes_per_iteration
            )
            result = {"status": "completed", "message": "All strategies trained"}
        
        # Send completion notification
        await websocket_manager.send_rl_training_update({
            'status': 'completed',
            'strategy_name': request.strategy_name or 'all',
            'result': result,
            'user_id': user_id
        })
        
        logger.info(f"RL training completed for {request.strategy_name or 'all strategies'}")
        
    except Exception as e:
        logger.error(f"Error in RL training: {e}")
        await websocket_manager.send_rl_training_update({
            'status': 'failed',
            'strategy_name': request.strategy_name or 'all',
            'error': str(e),
            'user_id': user_id
        })

@router.get("/parameters/{strategy_name}/{symbol}", response_model=APIResponse)
async def get_optimized_parameters(
    strategy_name: str,
    symbol: str,
    current_user: dict = Depends(get_current_user)
):
    """Get RL-optimized parameters for strategy and symbol."""
    try:
        parameters = rl_integration.get_optimized_parameters_for_strategy(
            strategy_name, symbol
        )
        
        if not parameters:
            raise HTTPException(
                status_code=404, 
                detail="No optimized parameters found for this strategy and symbol"
            )
        
        return APIResponse(
            success=True,
            message="Optimized parameters retrieved",
            data={
                "strategy_name": strategy_name,
                "symbol": symbol,
                "parameters": parameters
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting optimized parameters: {e}")
        raise HTTPException(status_code=500, detail="Failed to get optimized parameters")

@router.post("/optimize-parameters", response_model=APIResponse)
async def optimize_parameters(
    optimization_request: ParameterOptimizationRequest,
    background_tasks: BackgroundTasks,
    current_user: dict = Depends(get_current_user)
):
    """Optimize parameters for specific strategy and symbol."""
    try:
        if not current_user.get('is_admin', False):
            raise HTTPException(status_code=403, detail="Admin access required")
        
        # Start parameter optimization in background
        background_tasks.add_task(
            _run_parameter_optimization,
            optimization_request,
            current_user['id']
        )
        
        return APIResponse(
            success=True,
            message="Parameter optimization initiated",
            data={
                "strategy_name": optimization_request.strategy_name,
                "symbol": optimization_request.symbol,
                "optimization_metric": optimization_request.optimization_metric,
                "status": "started"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting parameter optimization: {e}")
        raise HTTPException(status_code=500, detail="Failed to start parameter optimization")

async def _run_parameter_optimization(request: ParameterOptimizationRequest, user_id: int):
    """Run parameter optimization in background."""
    try:
        # Send start notification
        await websocket_manager.send_rl_training_update({
            'status': 'started',
            'type': 'parameter_optimization',
            'strategy_name': request.strategy_name,
            'symbol': request.symbol,
            'user_id': user_id
        })
        
        # Run optimization
        result = await multi_agent_manager.run_strategy_optimization(
            request.strategy_name,
            request.parameter_ranges,
            request.optimization_metric
        )
        
        # Send completion notification
        await websocket_manager.send_rl_training_update({
            'status': 'completed',
            'type': 'parameter_optimization',
            'strategy_name': request.strategy_name,
            'symbol': request.symbol,
            'result': result,
            'user_id': user_id
        })
        
        logger.info(f"Parameter optimization completed for {request.strategy_name}")
        
    except Exception as e:
        logger.error(f"Error in parameter optimization: {e}")
        await websocket_manager.send_rl_training_update({
            'status': 'failed',
            'type': 'parameter_optimization',
            'strategy_name': request.strategy_name,
            'symbol': request.symbol,
            'error': str(e),
            'user_id': user_id
        })

@router.get("/training-history", response_model=APIResponse)
async def get_training_history(
    pagination: PaginationParams = Depends(),
    strategy_name: Optional[str] = None,
    current_user: dict = Depends(get_current_user)
):
    """Get RL training history."""
    try:
        with db_manager.get_session() as db:
            if strategy_name:
                history = RLTrainingHistoryCRUD.get_training_history(
                    db, strategy_name, limit=pagination.size
                )
            else:
                history = RLTrainingHistoryCRUD.get_recent_training_performance(
                    db, days=30
                )
            
            history_data = []
            for record in history:
                record_dict = {
                    'id': record.id,
                    'strategy_name': record.strategy_name,
                    'training_date': record.training_date.isoformat(),
                    'training_iterations': record.training_iterations,
                    'avg_reward': float(record.avg_reward) if record.avg_reward else None,
                    'final_reward': float(record.final_reward) if record.final_reward else None,
                    'training_time_minutes': record.training_time_minutes,
                    'improvement_over_baseline': float(record.improvement_over_baseline) if record.improvement_over_baseline else None
                }
                history_data.append(record_dict)
            
            return APIResponse(
                success=True,
                message="Training history retrieved",
                data={
                    "history": history_data,
                    "total": len(history_data),
                    "strategy_name": strategy_name
                }
            )
            
    except Exception as e:
        logger.error(f"Error getting training history: {e}")
        raise HTTPException(status_code=500, detail="Failed to get training history")

@router.get("/adaptations", response_model=APIResponse)
async def get_parameter_adaptations(
    pagination: PaginationParams = Depends(),
    strategy_name: Optional[str] = None,
    days: int = 7,
    current_user: dict = Depends(get_current_user)
):
    """Get recent parameter adaptations."""
    try:
        with db_manager.get_session() as db:
            if strategy_name:
                adaptations = RLParameterAdaptationCRUD.get_recent_adaptations(
                    db, strategy_name, days=days
                )
            else:
                # Get adaptations for all strategies
                adaptations = []
                for strategy in multi_agent_manager.agents.keys():
                    strategy_adaptations = RLParameterAdaptationCRUD.get_recent_adaptations(
                        db, strategy, days=days
                    )
                    adaptations.extend(strategy_adaptations)
            
            adaptations_data = []
            for adaptation in adaptations:
                adaptation_dict = {
                    'id': adaptation.id,
                    'strategy_name': adaptation.strategy_name,
                    'stock_id': adaptation.stock_id,
                    'adaptation_date': adaptation.adaptation_date.isoformat(),
                    'market_conditions': adaptation.market_conditions,
                    'adapted_parameters': adaptation.adapted_parameters,
                    'adaptation_reason': adaptation.adaptation_reason,
                    'expected_improvement': float(adaptation.expected_improvement) if adaptation.expected_improvement else None,
                    'actual_performance': float(adaptation.actual_performance) if adaptation.actual_performance else None
                }
                adaptations_data.append(adaptation_dict)
            
            return APIResponse(
                success=True,
                message="Parameter adaptations retrieved",
                data={
                    "adaptations": adaptations_data,
                    "total": len(adaptations_data),
                    "days": days,
                    "strategy_name": strategy_name
                }
            )
            
    except Exception as e:
        logger.error(f"Error getting parameter adaptations: {e}")
        raise HTTPException(status_code=500, detail="Failed to get parameter adaptations")

@router.get("/performance-metrics", response_model=APIResponse)
async def get_rl_performance_metrics(
    strategy_name: Optional[str] = None,
    days: int = 30,
    current_user: dict = Depends(get_current_user)
):
    """Get RL performance metrics."""
    try:
        with db_manager.get_session() as db:
            if strategy_name:
                metrics = RLParameterAdaptationCRUD.get_adaptation_performance(
                    db, strategy_name, days
                )
                
                return APIResponse(
                    success=True,
                    message="RL performance metrics retrieved",
                    data={
                        "strategy_name": strategy_name,
                        "metrics": metrics,
                        "period_days": days
                    }
                )
            else:
                # Get metrics for all strategies
                all_metrics = {}
                for strategy in multi_agent_manager.agents.keys():
                    strategy_metrics = RLParameterAdaptationCRUD.get_adaptation_performance(
                        db, strategy, days
                    )
                    all_metrics[strategy] = strategy_metrics
                
                return APIResponse(
                    success=True,
                    message="RL performance metrics retrieved for all strategies",
                    data={
                        "metrics": all_metrics,
                        "period_days": days
                    }
                )
            
    except Exception as e:
        logger.error(f"Error getting RL performance metrics: {e}")
        raise HTTPException(status_code=500, detail="Failed to get RL performance metrics")

@router.post("/models/{model_id}/deactivate", response_model=APIResponse)
async def deactivate_rl_model(
    model_id: int,
    current_user: dict = Depends(get_current_user)
):
    """Deactivate RL model."""
    try:
        if not current_user.get('is_admin', False):
            raise HTTPException(status_code=403, detail="Admin access required")
        
        with db_manager.get_session() as db:
            success = RLModelCRUD.deactivate_model(db, model_id)
            
            if not success:
                raise HTTPException(status_code=404, detail="RL model not found")
            
            return APIResponse(
                success=True,
                message="RL model deactivated",
                data={"model_id": model_id}
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deactivating RL model: {e}")
        raise HTTPException(status_code=500, detail="Failed to deactivate RL model")

@router.post("/reset-training", response_model=APIResponse)
async def reset_rl_training(
    strategy_name: Optional[str] = None,
    current_user: dict = Depends(get_current_user)
):
    """Reset RL training (reinitialize models)."""
    try:
        if not current_user.get('is_admin', False):
            raise HTTPException(status_code=403, detail="Admin access required")
        
        if strategy_name:
            # Reset specific strategy
            agent = multi_agent_manager.agents.get(strategy_name)
            if not agent:
                raise HTTPException(status_code=404, detail="Strategy not found")
            
            # Reinitialize agent
            agent.__init__(strategy_name, agent.parameter_bounds)
            message = f"RL training reset for {strategy_name}"
        else:
            # Reset all strategies
            multi_agent_manager._initialize_agents()
            message = "RL training reset for all strategies"
        
        return APIResponse(
            success=True,
            message=message,
            data={
                "strategy_name": strategy_name or "all",
                "reset_completed": True
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error resetting RL training: {e}")
        raise HTTPException(status_code=500, detail="Failed to reset RL training")
