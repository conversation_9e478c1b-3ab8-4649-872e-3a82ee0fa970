"""
Stock AI Agent - Main orchestrator for the AI-powered stock analysis system.
Coordinates data pipeline, strategy execution, and real-time alerts.
"""

import asyncio
from datetime import datetime, date
from typing import Dict, List, Any, Optional
import schedule
import time
from concurrent.futures import ThreadPoolExecutor

from .data.data_pipeline import data_pipeline
from .data.data_monitor import alert_manager
from .data.init_db import init_database
from .strategies.strategy_registry import strategy_registry, initialize_strategies
from .rl_optimizer.rl_integration import rl_integration
from .data.crud import StockCRUD
from .data.database import db_manager
from .utils.logger import get_logger
from .utils.config import config

logger = get_logger(__name__)

class StockAIAgent:
    """Main AI agent for stock analysis and trading signal generation."""
    
    def __init__(self):
        """Initialize the Stock AI Agent."""
        self.strategies = {}
        self.is_running = False
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # Initialize components
        self._initialize_system()
    
    def _initialize_system(self):
        """Initialize all system components."""
        try:
            logger.info("Initializing Stock AI Agent system...")
            
            # Test database connection
            if not db_manager.test_connection():
                logger.error("Database connection failed")
                raise Exception("Database connection failed")
            
            # Initialize database if needed
            try:
                init_database()
                logger.info("Database initialized successfully")
            except Exception as e:
                logger.warning(f"Database initialization warning: {e}")
            
            # Initialize strategies
            self.strategies = initialize_strategies()
            logger.info(f"Initialized {len(self.strategies)} trading strategies")

            # Initialize RL system
            asyncio.run(rl_integration.initialize_rl_system())
            logger.info("RL optimization system initialized")

            # Setup scheduler
            self._setup_scheduler()
            
            logger.info("Stock AI Agent system initialized successfully")
            
        except Exception as e:
            logger.error(f"System initialization failed: {e}")
            raise
    
    def _setup_scheduler(self):
        """Setup scheduled tasks."""
        try:
            # Daily data update (after market close)
            schedule.every().day.at("16:30").do(self._scheduled_data_update)
            
            # Strategy signal generation (multiple times during market hours)
            schedule.every().hour.do(self._scheduled_signal_generation)
            
            # Data quality monitoring
            schedule.every(4).hours.do(self._scheduled_monitoring)
            
            # Weekly strategy performance review
            schedule.every().sunday.at("10:00").do(self._scheduled_performance_review)

            # RL training (weekly)
            schedule.every().sunday.at("03:00").do(self._scheduled_rl_training)
            
            logger.info("Scheduled tasks configured")
            
        except Exception as e:
            logger.error(f"Error setting up scheduler: {e}")
    
    async def update_data(self) -> Dict[str, Any]:
        """Update stock data for all Nifty 50 stocks.
        
        Returns:
            Data update results
        """
        logger.info("Starting data update process")
        
        try:
            results = await data_pipeline.run_daily_update()
            
            # Log results
            if results.get('status') == 'completed':
                nifty_results = results.get('nifty50_update', {})
                logger.info(f"Data update completed: {nifty_results.get('successful_updates', 0)} successful, "
                           f"{nifty_results.get('failed_updates', 0)} failed")
            else:
                logger.error(f"Data update failed: {results.get('error', 'Unknown error')}")
            
            return results
            
        except Exception as e:
            logger.error(f"Error in data update: {e}")
            return {'status': 'failed', 'error': str(e)}
    
    async def generate_signals(self, symbols: Optional[List[str]] = None) -> Dict[str, Any]:
        """Generate trading signals using all enabled strategies.
        
        Args:
            symbols: List of symbols to analyze (default: Nifty 50)
            
        Returns:
            Signal generation results
        """
        logger.info("Starting signal generation process")
        
        try:
            # Get symbols if not provided
            if symbols is None:
                with db_manager.get_session() as db:
                    nifty50_stocks = StockCRUD.get_nifty50_stocks(db)
                    symbols = [stock.symbol for stock in nifty50_stocks]
            
            if not symbols:
                logger.warning("No symbols found for signal generation")
                return {'status': 'failed', 'error': 'No symbols available'}
            
            # Run all strategies
            results = strategy_registry.run_all_strategies(symbols)
            
            # Count total signals generated
            total_signals = 0
            for strategy_result in results.get('results', {}).values():
                if 'total_signals' in strategy_result:
                    total_signals += strategy_result['total_signals']
            
            logger.info(f"Signal generation completed: {total_signals} total signals from {len(self.strategies)} strategies")
            
            return {
                'status': 'completed',
                'total_signals': total_signals,
                'strategies_run': len(self.strategies),
                'symbols_processed': len(symbols),
                'results': results,
                'execution_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error in signal generation: {e}")
            return {'status': 'failed', 'error': str(e)}
    
    async def run_monitoring(self) -> Dict[str, Any]:
        """Run data quality monitoring and alerting.
        
        Returns:
            Monitoring results
        """
        logger.info("Starting monitoring cycle")
        
        try:
            results = await alert_manager.run_monitoring_cycle()
            
            health_score = results.get('health_report', {}).get('overall_health_score', 'N/A')
            alerts_sent = results.get('alerts_sent', 0)
            
            logger.info(f"Monitoring completed: Health score {health_score}, {alerts_sent} alerts sent")
            
            return results
            
        except Exception as e:
            logger.error(f"Error in monitoring: {e}")
            return {'status': 'failed', 'error': str(e)}
    
    def get_latest_signals(self, strategy_name: Optional[str] = None, limit: int = 50) -> List[Dict[str, Any]]:
        """Get latest trading signals.
        
        Args:
            strategy_name: Filter by strategy name
            limit: Maximum number of signals
            
        Returns:
            List of latest signals
        """
        try:
            return strategy_registry.get_latest_signals(strategy_name, limit)
        except Exception as e:
            logger.error(f"Error getting latest signals: {e}")
            return []
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status.
        
        Returns:
            System status information
        """
        try:
            # Database status
            db_status = db_manager.test_connection()
            
            # Strategy status
            strategy_summary = strategy_registry.get_strategy_performance_summary()
            
            # Data quality status
            data_quality = data_pipeline.get_data_quality_report()

            # RL system status
            rl_status = rl_integration.get_rl_system_status()

            return {
                'system_status': 'operational' if db_status else 'degraded',
                'database_connected': db_status,
                'strategies_initialized': len(self.strategies),
                'is_running': self.is_running,
                'strategy_summary': strategy_summary,
                'data_quality': data_quality,
                'rl_system': rl_status,
                'last_updated': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting system status: {e}")
            return {
                'system_status': 'error',
                'error': str(e),
                'last_updated': datetime.now().isoformat()
            }
    
    def _scheduled_data_update(self):
        """Scheduled data update task."""
        logger.info("Running scheduled data update")
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            result = loop.run_until_complete(self.update_data())
            loop.close()
            
            if result.get('status') != 'completed':
                logger.error(f"Scheduled data update failed: {result.get('error')}")
        except Exception as e:
            logger.error(f"Error in scheduled data update: {e}")
    
    def _scheduled_signal_generation(self):
        """Scheduled signal generation task."""
        logger.info("Running scheduled signal generation")
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            result = loop.run_until_complete(self.generate_signals())
            loop.close()
            
            if result.get('status') != 'completed':
                logger.error(f"Scheduled signal generation failed: {result.get('error')}")
        except Exception as e:
            logger.error(f"Error in scheduled signal generation: {e}")
    
    def _scheduled_monitoring(self):
        """Scheduled monitoring task."""
        logger.info("Running scheduled monitoring")
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            result = loop.run_until_complete(self.run_monitoring())
            loop.close()
            
            if result.get('status') != 'completed':
                logger.error(f"Scheduled monitoring failed: {result.get('error')}")
        except Exception as e:
            logger.error(f"Error in scheduled monitoring: {e}")
    
    def _scheduled_performance_review(self):
        """Scheduled performance review task."""
        logger.info("Running scheduled performance review")
        try:
            # Get performance summary
            summary = strategy_registry.get_strategy_performance_summary()
            
            # Log performance metrics
            for strategy_name, metrics in summary.get('strategies', {}).items():
                signals_count = metrics.get('signals_generated', 0)
                logger.info(f"Strategy {strategy_name}: {signals_count} signals generated")
            
        except Exception as e:
            logger.error(f"Error in scheduled performance review: {e}")

    def _scheduled_rl_training(self):
        """Scheduled RL training task."""
        logger.info("Running scheduled RL training")
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            result = loop.run_until_complete(rl_integration.manager.train_all_agents(num_iterations=20))
            loop.close()

            logger.info("Scheduled RL training completed")
        except Exception as e:
            logger.error(f"Error in scheduled RL training: {e}")
    
    async def run(self):
        """Run the main agent loop."""
        logger.info("Starting Stock AI Agent main loop")
        self.is_running = True
        
        try:
            # Initial data update
            await self.update_data()
            
            # Initial signal generation
            await self.generate_signals()
            
            # Main loop
            while self.is_running:
                try:
                    # Run scheduled tasks
                    schedule.run_pending()
                    
                    # Sleep for a short interval
                    await asyncio.sleep(60)  # Check every minute
                    
                except KeyboardInterrupt:
                    logger.info("Received interrupt signal")
                    break
                except Exception as e:
                    logger.error(f"Error in main loop: {e}")
                    await asyncio.sleep(300)  # Wait 5 minutes before retrying
        
        except Exception as e:
            logger.error(f"Fatal error in agent main loop: {e}")
            raise
        finally:
            self.is_running = False
            self.executor.shutdown(wait=True)
            logger.info("Stock AI Agent stopped")
    
    def stop(self):
        """Stop the agent."""
        logger.info("Stopping Stock AI Agent")
        self.is_running = False

# Global agent instance
stock_ai_agent = StockAIAgent()

async def main():
    """Main entry point for the Stock AI Agent."""
    try:
        await stock_ai_agent.run()
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
    except Exception as e:
        logger.error(f"Application error: {e}")
        raise
    finally:
        stock_ai_agent.stop()

if __name__ == "__main__":
    asyncio.run(main())
