"""
Modern Portfolio Theory (MPT) Optimizer.
Implements <PERSON>owitz mean-variance optimization with various objective functions.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from scipy.optimize import minimize
from scipy.linalg import inv
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')

from ..data.database import db_manager
from ..data.crud import StockCRUD, PriceCRUD
from ..utils.logger import get_logger

logger = get_logger(__name__)

@dataclass
class OptimizationResult:
    """Portfolio optimization result."""
    weights: np.ndarray
    expected_return: float
    volatility: float
    sharpe_ratio: float
    symbols: List[str]
    optimization_method: str
    constraints_satisfied: bool
    optimization_success: bool
    additional_metrics: Dict[str, float]

@dataclass
class PortfolioConstraints:
    """Portfolio optimization constraints."""
    min_weight: float = 0.0
    max_weight: float = 1.0
    max_concentration: float = 0.15  # Maximum 15% in any single stock
    min_positions: int = 5
    max_positions: int = 20
    sector_limits: Optional[Dict[str, float]] = None
    turnover_limit: Optional[float] = None
    tracking_error_limit: Optional[float] = None

class MPTOptimizer:
    """Modern Portfolio Theory optimizer."""
    
    def __init__(self):
        """Initialize MPT optimizer."""
        self.risk_free_rate = 0.06  # 6% risk-free rate (Indian context)
        self.trading_cost = 0.001  # 0.1% trading cost
        
        logger.info("MPT Optimizer initialized")
    
    def calculate_returns_covariance(
        self, 
        symbols: List[str], 
        lookback_days: int = 252
    ) -> Tuple[np.ndarray, np.ndarray]:
        """Calculate expected returns and covariance matrix."""
        try:
            returns_data = []
            
            with db_manager.get_session() as db:
                for symbol in symbols:
                    stock = StockCRUD.get_stock_by_symbol(db, symbol)
                    if not stock:
                        continue
                    
                    # Get price history
                    prices = PriceCRUD.get_recent_prices(db, stock.id, lookback_days + 1)
                    
                    if len(prices) < lookback_days:
                        logger.warning(f"Insufficient data for {symbol}: {len(prices)} days")
                        continue
                    
                    # Calculate returns
                    price_series = [float(p.close_price) for p in reversed(prices)]
                    returns = np.diff(price_series) / price_series[:-1]
                    returns_data.append(returns)
            
            if len(returns_data) < 2:
                raise ValueError("Insufficient data for optimization")
            
            # Create returns matrix
            returns_matrix = np.array(returns_data).T
            
            # Calculate expected returns (annualized)
            expected_returns = np.mean(returns_matrix, axis=0) * 252
            
            # Calculate covariance matrix (annualized)
            covariance_matrix = np.cov(returns_matrix.T) * 252
            
            return expected_returns, covariance_matrix
            
        except Exception as e:
            logger.error(f"Error calculating returns and covariance: {e}")
            raise
    
    def optimize_max_sharpe(
        self, 
        expected_returns: np.ndarray, 
        covariance_matrix: np.ndarray,
        constraints: PortfolioConstraints
    ) -> OptimizationResult:
        """Optimize portfolio for maximum Sharpe ratio."""
        try:
            n_assets = len(expected_returns)
            
            # Objective function (negative Sharpe ratio)
            def objective(weights):
                portfolio_return = np.dot(weights, expected_returns)
                portfolio_variance = np.dot(weights.T, np.dot(covariance_matrix, weights))
                portfolio_volatility = np.sqrt(portfolio_variance)
                
                if portfolio_volatility == 0:
                    return -np.inf
                
                sharpe_ratio = (portfolio_return - self.risk_free_rate) / portfolio_volatility
                return -sharpe_ratio  # Negative because we minimize
            
            # Constraints
            constraints_list = [
                {'type': 'eq', 'fun': lambda x: np.sum(x) - 1.0}  # Weights sum to 1
            ]
            
            # Bounds
            bounds = [(constraints.min_weight, min(constraints.max_weight, constraints.max_concentration)) 
                     for _ in range(n_assets)]
            
            # Initial guess (equal weights)
            initial_guess = np.array([1.0 / n_assets] * n_assets)
            
            # Optimize
            result = minimize(
                objective,
                initial_guess,
                method='SLSQP',
                bounds=bounds,
                constraints=constraints_list,
                options={'maxiter': 1000, 'ftol': 1e-9}
            )
            
            if not result.success:
                logger.warning(f"Optimization failed: {result.message}")
            
            # Calculate portfolio metrics
            optimal_weights = result.x
            portfolio_return = np.dot(optimal_weights, expected_returns)
            portfolio_variance = np.dot(optimal_weights.T, np.dot(covariance_matrix, optimal_weights))
            portfolio_volatility = np.sqrt(portfolio_variance)
            sharpe_ratio = (portfolio_return - self.risk_free_rate) / portfolio_volatility
            
            # Additional metrics
            additional_metrics = {
                'max_weight': np.max(optimal_weights),
                'min_weight': np.min(optimal_weights),
                'effective_positions': np.sum(optimal_weights > 0.01),  # Positions > 1%
                'concentration_hhi': np.sum(optimal_weights ** 2),  # Herfindahl index
                'diversification_ratio': self._calculate_diversification_ratio(
                    optimal_weights, expected_returns, covariance_matrix
                )
            }
            
            return OptimizationResult(
                weights=optimal_weights,
                expected_return=portfolio_return,
                volatility=portfolio_volatility,
                sharpe_ratio=sharpe_ratio,
                symbols=[],  # Will be set by caller
                optimization_method="max_sharpe",
                constraints_satisfied=result.success,
                optimization_success=result.success,
                additional_metrics=additional_metrics
            )
            
        except Exception as e:
            logger.error(f"Error in max Sharpe optimization: {e}")
            raise
    
    def optimize_min_variance(
        self, 
        expected_returns: np.ndarray, 
        covariance_matrix: np.ndarray,
        constraints: PortfolioConstraints
    ) -> OptimizationResult:
        """Optimize portfolio for minimum variance."""
        try:
            n_assets = len(expected_returns)
            
            # Objective function (portfolio variance)
            def objective(weights):
                return np.dot(weights.T, np.dot(covariance_matrix, weights))
            
            # Constraints
            constraints_list = [
                {'type': 'eq', 'fun': lambda x: np.sum(x) - 1.0}  # Weights sum to 1
            ]
            
            # Bounds
            bounds = [(constraints.min_weight, min(constraints.max_weight, constraints.max_concentration)) 
                     for _ in range(n_assets)]
            
            # Initial guess
            initial_guess = np.array([1.0 / n_assets] * n_assets)
            
            # Optimize
            result = minimize(
                objective,
                initial_guess,
                method='SLSQP',
                bounds=bounds,
                constraints=constraints_list,
                options={'maxiter': 1000, 'ftol': 1e-9}
            )
            
            # Calculate portfolio metrics
            optimal_weights = result.x
            portfolio_return = np.dot(optimal_weights, expected_returns)
            portfolio_variance = np.dot(optimal_weights.T, np.dot(covariance_matrix, optimal_weights))
            portfolio_volatility = np.sqrt(portfolio_variance)
            sharpe_ratio = (portfolio_return - self.risk_free_rate) / portfolio_volatility
            
            additional_metrics = {
                'max_weight': np.max(optimal_weights),
                'min_weight': np.min(optimal_weights),
                'effective_positions': np.sum(optimal_weights > 0.01),
                'concentration_hhi': np.sum(optimal_weights ** 2),
                'diversification_ratio': self._calculate_diversification_ratio(
                    optimal_weights, expected_returns, covariance_matrix
                )
            }
            
            return OptimizationResult(
                weights=optimal_weights,
                expected_return=portfolio_return,
                volatility=portfolio_volatility,
                sharpe_ratio=sharpe_ratio,
                symbols=[],
                optimization_method="min_variance",
                constraints_satisfied=result.success,
                optimization_success=result.success,
                additional_metrics=additional_metrics
            )
            
        except Exception as e:
            logger.error(f"Error in minimum variance optimization: {e}")
            raise
    
    def optimize_target_return(
        self, 
        expected_returns: np.ndarray, 
        covariance_matrix: np.ndarray,
        target_return: float,
        constraints: PortfolioConstraints
    ) -> OptimizationResult:
        """Optimize portfolio for target return with minimum risk."""
        try:
            n_assets = len(expected_returns)
            
            # Objective function (portfolio variance)
            def objective(weights):
                return np.dot(weights.T, np.dot(covariance_matrix, weights))
            
            # Constraints
            constraints_list = [
                {'type': 'eq', 'fun': lambda x: np.sum(x) - 1.0},  # Weights sum to 1
                {'type': 'eq', 'fun': lambda x: np.dot(x, expected_returns) - target_return}  # Target return
            ]
            
            # Bounds
            bounds = [(constraints.min_weight, min(constraints.max_weight, constraints.max_concentration)) 
                     for _ in range(n_assets)]
            
            # Initial guess
            initial_guess = np.array([1.0 / n_assets] * n_assets)
            
            # Optimize
            result = minimize(
                objective,
                initial_guess,
                method='SLSQP',
                bounds=bounds,
                constraints=constraints_list,
                options={'maxiter': 1000, 'ftol': 1e-9}
            )
            
            # Calculate portfolio metrics
            optimal_weights = result.x
            portfolio_return = np.dot(optimal_weights, expected_returns)
            portfolio_variance = np.dot(optimal_weights.T, np.dot(covariance_matrix, optimal_weights))
            portfolio_volatility = np.sqrt(portfolio_variance)
            sharpe_ratio = (portfolio_return - self.risk_free_rate) / portfolio_volatility
            
            additional_metrics = {
                'target_return': target_return,
                'achieved_return': portfolio_return,
                'return_error': abs(portfolio_return - target_return),
                'max_weight': np.max(optimal_weights),
                'min_weight': np.min(optimal_weights),
                'effective_positions': np.sum(optimal_weights > 0.01),
                'concentration_hhi': np.sum(optimal_weights ** 2)
            }
            
            return OptimizationResult(
                weights=optimal_weights,
                expected_return=portfolio_return,
                volatility=portfolio_volatility,
                sharpe_ratio=sharpe_ratio,
                symbols=[],
                optimization_method="target_return",
                constraints_satisfied=result.success,
                optimization_success=result.success,
                additional_metrics=additional_metrics
            )
            
        except Exception as e:
            logger.error(f"Error in target return optimization: {e}")
            raise
    
    def generate_efficient_frontier(
        self, 
        expected_returns: np.ndarray, 
        covariance_matrix: np.ndarray,
        constraints: PortfolioConstraints,
        n_points: int = 50
    ) -> List[OptimizationResult]:
        """Generate efficient frontier."""
        try:
            # Get min and max possible returns
            min_var_result = self.optimize_min_variance(expected_returns, covariance_matrix, constraints)
            max_return = np.max(expected_returns)
            min_return = min_var_result.expected_return
            
            # Generate target returns
            target_returns = np.linspace(min_return, max_return * 0.95, n_points)
            
            efficient_portfolios = []
            
            for target_return in target_returns:
                try:
                    result = self.optimize_target_return(
                        expected_returns, covariance_matrix, target_return, constraints
                    )
                    
                    if result.optimization_success:
                        efficient_portfolios.append(result)
                        
                except Exception as e:
                    logger.debug(f"Failed to optimize for target return {target_return}: {e}")
                    continue
            
            return efficient_portfolios
            
        except Exception as e:
            logger.error(f"Error generating efficient frontier: {e}")
            return []
    
    def _calculate_diversification_ratio(
        self, 
        weights: np.ndarray, 
        expected_returns: np.ndarray, 
        covariance_matrix: np.ndarray
    ) -> float:
        """Calculate diversification ratio."""
        try:
            # Weighted average of individual volatilities
            individual_volatilities = np.sqrt(np.diag(covariance_matrix))
            weighted_avg_volatility = np.dot(weights, individual_volatilities)
            
            # Portfolio volatility
            portfolio_variance = np.dot(weights.T, np.dot(covariance_matrix, weights))
            portfolio_volatility = np.sqrt(portfolio_variance)
            
            if portfolio_volatility == 0:
                return 1.0
            
            return weighted_avg_volatility / portfolio_volatility
            
        except Exception as e:
            logger.error(f"Error calculating diversification ratio: {e}")
            return 1.0
    
    def optimize_portfolio(
        self, 
        symbols: List[str], 
        method: str = "max_sharpe",
        constraints: Optional[PortfolioConstraints] = None,
        target_return: Optional[float] = None,
        lookback_days: int = 252
    ) -> OptimizationResult:
        """Main portfolio optimization method."""
        try:
            if constraints is None:
                constraints = PortfolioConstraints()
            
            # Calculate returns and covariance
            expected_returns, covariance_matrix = self.calculate_returns_covariance(
                symbols, lookback_days
            )
            
            # Filter symbols based on available data
            valid_indices = []
            valid_symbols = []
            
            for i, symbol in enumerate(symbols):
                if i < len(expected_returns) and not np.isnan(expected_returns[i]):
                    valid_indices.append(i)
                    valid_symbols.append(symbol)
            
            if len(valid_symbols) < constraints.min_positions:
                raise ValueError(f"Insufficient valid symbols: {len(valid_symbols)}")
            
            # Filter data
            expected_returns = expected_returns[valid_indices]
            covariance_matrix = covariance_matrix[np.ix_(valid_indices, valid_indices)]
            
            # Optimize based on method
            if method == "max_sharpe":
                result = self.optimize_max_sharpe(expected_returns, covariance_matrix, constraints)
            elif method == "min_variance":
                result = self.optimize_min_variance(expected_returns, covariance_matrix, constraints)
            elif method == "target_return":
                if target_return is None:
                    raise ValueError("Target return required for target_return method")
                result = self.optimize_target_return(
                    expected_returns, covariance_matrix, target_return, constraints
                )
            else:
                raise ValueError(f"Unknown optimization method: {method}")
            
            # Set symbols
            result.symbols = valid_symbols
            
            logger.info(f"Portfolio optimization completed: {method}")
            logger.info(f"Expected return: {result.expected_return:.4f}")
            logger.info(f"Volatility: {result.volatility:.4f}")
            logger.info(f"Sharpe ratio: {result.sharpe_ratio:.4f}")
            
            return result
            
        except Exception as e:
            logger.error(f"Error in portfolio optimization: {e}")
            raise

# Global MPT optimizer instance
mpt_optimizer = MPTOptimizer()
