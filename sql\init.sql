-- Database initialization script for PostgreSQL
-- This script is run when the Docker container starts

-- Create database if it doesn't exist (handled by Docker environment)
-- CREATE DATABASE IF NOT EXISTS stock_analyzer;

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- <PERSON>reate custom types
DO $$ BEGIN
    CREATE TYPE signal_type AS ENUM ('BUY', 'SELL', 'HOLD');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE trade_type AS ENUM ('BUY', 'SELL');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE action_type AS ENUM ('SPLIT', 'DIVIDEND', 'BONUS', 'RIGHTS');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create functions for common calculations
CREATE OR REPLACE FUNCTION calculate_returns(
    start_price DECIMAL,
    end_price DECIMAL
) RETURNS DECIMAL AS $$
BEGIN
    IF start_price IS NULL OR start_price = 0 THEN
        RETURN NULL;
    END IF;
    RETURN ((end_price - start_price) / start_price) * 100;
END;
$$ LANGUAGE plpgsql;

-- Function to calculate moving average
CREATE OR REPLACE FUNCTION calculate_sma(
    stock_id_param INTEGER,
    date_param DATE,
    period INTEGER
) RETURNS DECIMAL AS $$
DECLARE
    avg_price DECIMAL;
BEGIN
    SELECT AVG(close_price) INTO avg_price
    FROM daily_prices
    WHERE stock_id = stock_id_param
      AND date <= date_param
    ORDER BY date DESC
    LIMIT period;
    
    RETURN avg_price;
END;
$$ LANGUAGE plpgsql;

-- Create indexes for better performance (these will be created by SQLAlchemy too)
-- Additional performance indexes can be added here

-- Create a view for latest stock prices
CREATE OR REPLACE VIEW latest_stock_prices AS
SELECT DISTINCT ON (dp.stock_id)
    sm.symbol,
    sm.company_name,
    dp.date,
    dp.close_price,
    dp.volume,
    dp.rsi_14,
    dp.macd,
    dp.sma_50,
    dp.sma_200
FROM daily_prices dp
JOIN stock_metadata sm ON dp.stock_id = sm.id
WHERE sm.is_active = true
ORDER BY dp.stock_id, dp.date DESC;

-- Create a view for Nifty 50 performance
CREATE OR REPLACE VIEW nifty50_performance AS
SELECT 
    sm.symbol,
    sm.company_name,
    dp.close_price,
    dp.date,
    LAG(dp.close_price) OVER (PARTITION BY sm.id ORDER BY dp.date) as prev_close,
    calculate_returns(
        LAG(dp.close_price) OVER (PARTITION BY sm.id ORDER BY dp.date),
        dp.close_price
    ) as daily_return
FROM daily_prices dp
JOIN stock_metadata sm ON dp.stock_id = sm.id
WHERE sm.is_nifty50 = true
  AND sm.is_active = true
ORDER BY dp.date DESC, sm.symbol;

-- Create materialized view for strategy performance summary
CREATE MATERIALIZED VIEW IF NOT EXISTS strategy_performance_summary AS
SELECT 
    strategy_name,
    COUNT(*) as total_signals,
    COUNT(CASE WHEN signal_type = 'BUY' THEN 1 END) as buy_signals,
    COUNT(CASE WHEN signal_type = 'SELL' THEN 1 END) as sell_signals,
    AVG(signal_strength) as avg_signal_strength,
    AVG(confidence_score) as avg_confidence,
    MAX(signal_date) as last_signal_date
FROM strategy_signals
WHERE is_valid = true
GROUP BY strategy_name;

-- Create index on materialized view
CREATE UNIQUE INDEX IF NOT EXISTS idx_strategy_performance_name 
ON strategy_performance_summary (strategy_name);

-- Grant permissions (adjust as needed for your setup)
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO stock_analyzer_user;
-- GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO stock_analyzer_user;

-- Insert some initial configuration data if needed
-- This can be expanded based on requirements

COMMIT;
