"""
Test Suite for Portfolio Optimization System.
Comprehensive tests for all optimization methods and analytics.
"""

import pytest
import numpy as np
import pandas as pd
from datetime import date, timedelta
from unittest.mock import Mock, patch

from src.portfolio_optimization.optimizer import (
    portfolio_optimizer, PortfolioOptimizationRequest, OptimizationMethod
)
from src.portfolio_optimization.mpt_optimizer import (
    mpt_optimizer, PortfolioConstraints, OptimizationResult
)
from src.portfolio_optimization.black_litterman import (
    bl_optimizer, InvestorView, BlackLittermanResult
)
from src.portfolio_optimization.risk_parity import risk_parity_optimizer
from src.portfolio_optimization.performance_analytics import (
    performance_analytics, PerformanceMetrics
)
from src.portfolio_optimization.rebalancing import (
    portfolio_rebalancer, RebalancingConfig, RebalancingTrigger
)

class TestMPTOptimizer:
    """Test Modern Portfolio Theory optimizer."""
    
    @pytest.fixture
    def sample_data(self):
        """Create sample data for testing."""
        np.random.seed(42)
        n_assets = 5
        n_days = 252
        
        # Generate correlated returns
        returns = np.random.multivariate_normal(
            mean=[0.0008] * n_assets,  # ~20% annual return
            cov=np.eye(n_assets) * 0.0004 + 0.0001,  # Some correlation
            size=n_days
        )
        
        expected_returns = np.mean(returns, axis=0) * 252
        covariance_matrix = np.cov(returns.T) * 252
        
        return expected_returns, covariance_matrix
    
    def test_max_sharpe_optimization(self, sample_data):
        """Test maximum Sharpe ratio optimization."""
        expected_returns, covariance_matrix = sample_data
        constraints = PortfolioConstraints()
        
        result = mpt_optimizer.optimize_max_sharpe(
            expected_returns, covariance_matrix, constraints
        )
        
        assert isinstance(result, OptimizationResult)
        assert result.optimization_success
        assert len(result.weights) == len(expected_returns)
        assert abs(np.sum(result.weights) - 1.0) < 1e-6  # Weights sum to 1
        assert all(w >= 0 for w in result.weights)  # Non-negative weights
        assert result.sharpe_ratio > 0
    
    def test_min_variance_optimization(self, sample_data):
        """Test minimum variance optimization."""
        expected_returns, covariance_matrix = sample_data
        constraints = PortfolioConstraints()
        
        result = mpt_optimizer.optimize_min_variance(
            covariance_matrix, constraints
        )
        
        assert isinstance(result, OptimizationResult)
        assert result.optimization_success
        assert len(result.weights) == len(expected_returns)
        assert abs(np.sum(result.weights) - 1.0) < 1e-6
        assert all(w >= 0 for w in result.weights)
        assert result.volatility > 0
    
    def test_target_return_optimization(self, sample_data):
        """Test target return optimization."""
        expected_returns, covariance_matrix = sample_data
        constraints = PortfolioConstraints()
        target_return = 0.15  # 15% target
        
        result = mpt_optimizer.optimize_target_return(
            expected_returns, covariance_matrix, target_return, constraints
        )
        
        assert isinstance(result, OptimizationResult)
        assert len(result.weights) == len(expected_returns)
        assert abs(np.sum(result.weights) - 1.0) < 1e-6
        
        # Check if target return is achieved (within tolerance)
        achieved_return = np.dot(result.weights, expected_returns)
        assert abs(achieved_return - target_return) < 0.01
    
    def test_efficient_frontier_generation(self, sample_data):
        """Test efficient frontier generation."""
        expected_returns, covariance_matrix = sample_data
        constraints = PortfolioConstraints()
        
        efficient_portfolios = mpt_optimizer.generate_efficient_frontier(
            expected_returns, covariance_matrix, constraints, n_points=10
        )
        
        assert len(efficient_portfolios) > 0
        assert all(isinstance(p, OptimizationResult) for p in efficient_portfolios)
        
        # Check that portfolios are ordered by return
        returns = [p.expected_return for p in efficient_portfolios]
        assert returns == sorted(returns)
        
        # Check that all portfolios are on the efficient frontier
        for portfolio in efficient_portfolios:
            assert portfolio.optimization_success
            assert abs(np.sum(portfolio.weights) - 1.0) < 1e-6
    
    def test_constraints_enforcement(self, sample_data):
        """Test that constraints are properly enforced."""
        expected_returns, covariance_matrix = sample_data
        
        constraints = PortfolioConstraints(
            min_weight=0.1,
            max_weight=0.3,
            max_concentration=0.25
        )
        
        result = mpt_optimizer.optimize_max_sharpe(
            expected_returns, covariance_matrix, constraints
        )
        
        assert all(w >= constraints.min_weight - 1e-6 for w in result.weights)
        assert all(w <= constraints.max_weight + 1e-6 for w in result.weights)
        assert max(result.weights) <= constraints.max_concentration + 1e-6

class TestBlackLittermanOptimizer:
    """Test Black-Litterman optimizer."""
    
    @pytest.fixture
    def sample_views(self):
        """Create sample investor views."""
        return [
            InvestorView(
                assets=['ASSET_1'],
                view_return=0.15,
                confidence=0.8,
                view_type='absolute',
                description='Bullish view on Asset 1'
            ),
            InvestorView(
                assets=['ASSET_2', 'ASSET_3'],
                view_return=0.05,
                confidence=0.6,
                view_type='relative',
                description='Asset 2 outperforms Asset 3'
            )
        ]
    
    def test_market_equilibrium_calculation(self):
        """Test market equilibrium returns calculation."""
        market_caps = np.array([1000, 800, 600, 400, 200])  # Market caps
        covariance_matrix = np.eye(5) * 0.04  # Simple covariance
        
        equilibrium_returns = bl_optimizer.calculate_market_equilibrium_returns(
            market_caps, covariance_matrix
        )
        
        assert len(equilibrium_returns) == len(market_caps)
        assert all(r > 0 for r in equilibrium_returns)  # Positive returns
    
    def test_view_matrix_creation(self, sample_views):
        """Test creation of view matrices."""
        symbols = ['ASSET_1', 'ASSET_2', 'ASSET_3', 'ASSET_4', 'ASSET_5']
        
        P, Q = bl_optimizer.create_view_matrix(sample_views, symbols)
        
        assert P.shape == (len(sample_views), len(symbols))
        assert Q.shape == (len(sample_views),)
        
        # Check absolute view
        assert P[0, 0] == 1.0  # Asset 1 view
        assert Q[0] == 0.15
        
        # Check relative view
        assert P[1, 1] == 1.0   # Asset 2
        assert P[1, 2] == -1.0  # Asset 3
        assert Q[1] == 0.05
    
    def test_view_uncertainty_calculation(self, sample_views):
        """Test view uncertainty matrix calculation."""
        symbols = ['ASSET_1', 'ASSET_2', 'ASSET_3', 'ASSET_4', 'ASSET_5']
        P, Q = bl_optimizer.create_view_matrix(sample_views, symbols)
        covariance_matrix = np.eye(5) * 0.04
        
        omega = bl_optimizer.calculate_view_uncertainty(
            sample_views, P, covariance_matrix
        )
        
        assert omega.shape == (len(sample_views), len(sample_views))
        assert np.all(np.diag(omega) > 0)  # Positive diagonal elements
        
        # Higher confidence should lead to lower uncertainty
        assert omega[0, 0] < omega[1, 1]  # View 1 has higher confidence

class TestRiskParityOptimizer:
    """Test Risk Parity optimizer."""
    
    @pytest.fixture
    def sample_covariance(self):
        """Create sample covariance matrix."""
        np.random.seed(42)
        n_assets = 5
        
        # Generate positive definite covariance matrix
        A = np.random.randn(n_assets, n_assets)
        covariance_matrix = np.dot(A, A.T) * 0.01
        
        return covariance_matrix
    
    def test_risk_contributions_calculation(self, sample_covariance):
        """Test risk contributions calculation."""
        weights = np.array([0.2, 0.2, 0.2, 0.2, 0.2])  # Equal weights
        
        risk_contributions = risk_parity_optimizer.calculate_risk_contributions(
            weights, sample_covariance
        )
        
        assert len(risk_contributions) == len(weights)
        assert abs(np.sum(risk_contributions) - 1.0) < 1e-6  # Sum to 1
        assert all(rc >= 0 for rc in risk_contributions)  # Non-negative
    
    def test_equal_risk_contribution_optimization(self, sample_covariance):
        """Test Equal Risk Contribution optimization."""
        constraints = PortfolioConstraints()
        
        weights = risk_parity_optimizer.optimize_equal_risk_contribution(
            sample_covariance, constraints
        )
        
        assert len(weights) == sample_covariance.shape[0]
        assert abs(np.sum(weights) - 1.0) < 1e-6
        assert all(w >= 0 for w in weights)
        
        # Check that risk contributions are approximately equal
        risk_contributions = risk_parity_optimizer.calculate_risk_contributions(
            weights, sample_covariance
        )
        target_risk = 1.0 / len(weights)
        max_deviation = max(abs(rc - target_risk) for rc in risk_contributions)
        assert max_deviation < 0.05  # Within 5% of target
    
    def test_hierarchical_risk_parity(self, sample_covariance):
        """Test Hierarchical Risk Parity optimization."""
        expected_returns = np.array([0.1, 0.12, 0.08, 0.15, 0.09])
        constraints = PortfolioConstraints()
        
        weights = risk_parity_optimizer.optimize_hierarchical_risk_parity(
            expected_returns, sample_covariance, constraints
        )
        
        assert len(weights) == len(expected_returns)
        assert abs(np.sum(weights) - 1.0) < 1e-6
        assert all(w >= 0 for w in weights)

class TestPerformanceAnalytics:
    """Test Performance Analytics."""
    
    @pytest.fixture
    def sample_returns(self):
        """Create sample return series."""
        np.random.seed(42)
        return np.random.normal(0.0008, 0.015, 252)  # Daily returns
    
    def test_performance_metrics_calculation(self, sample_returns):
        """Test comprehensive performance metrics calculation."""
        metrics = performance_analytics.calculate_performance_metrics(sample_returns)
        
        assert isinstance(metrics, PerformanceMetrics)
        assert metrics.total_return != 0
        assert metrics.annualized_return != 0
        assert metrics.volatility > 0
        assert -1 <= metrics.max_drawdown <= 0
        assert 0 <= metrics.win_rate <= 1
        
        # Check that Sharpe ratio is calculated
        expected_sharpe = (metrics.annualized_return - 0.06) / metrics.volatility
        assert abs(metrics.sharpe_ratio - expected_sharpe) < 0.01
    
    def test_rolling_metrics_calculation(self, sample_returns):
        """Test rolling metrics calculation."""
        rolling_metrics = performance_analytics.calculate_rolling_metrics(
            sample_returns, window=60, metrics=['return', 'volatility', 'sharpe']
        )
        
        assert 'return' in rolling_metrics
        assert 'volatility' in rolling_metrics
        assert 'sharpe' in rolling_metrics
        
        expected_length = len(sample_returns) - 60 + 1
        assert len(rolling_metrics['return']) == expected_length
        assert len(rolling_metrics['volatility']) == expected_length
        assert len(rolling_metrics['sharpe']) == expected_length
    
    def test_risk_decomposition(self):
        """Test portfolio risk decomposition."""
        weights = np.array([0.3, 0.3, 0.4])
        covariance_matrix = np.array([
            [0.04, 0.01, 0.02],
            [0.01, 0.09, 0.01],
            [0.02, 0.01, 0.16]
        ])
        symbols = ['ASSET_1', 'ASSET_2', 'ASSET_3']
        
        risk_decomp = performance_analytics.calculate_risk_decomposition(
            weights, covariance_matrix, symbols
        )
        
        assert risk_decomp.total_risk > 0
        assert len(risk_decomp.risk_contributions) == len(symbols)
        assert len(risk_decomp.marginal_risk_contributions) == len(symbols)
        
        # Risk contributions should sum to total risk
        total_risk_contrib = sum(risk_decomp.risk_contributions.values())
        assert abs(total_risk_contrib - risk_decomp.total_risk) < 1e-6

class TestRebalancingSystem:
    """Test Portfolio Rebalancing System."""
    
    @pytest.fixture
    def sample_weights(self):
        """Create sample portfolio weights."""
        current_weights = {
            'ASSET_1': 0.25,
            'ASSET_2': 0.30,
            'ASSET_3': 0.45
        }
        
        target_weights = {
            'ASSET_1': 0.20,
            'ASSET_2': 0.35,
            'ASSET_3': 0.45
        }
        
        return current_weights, target_weights
    
    def test_threshold_based_trigger(self, sample_weights):
        """Test threshold-based rebalancing trigger."""
        current_weights, target_weights = sample_weights
        
        config = RebalancingConfig(
            trigger_type=RebalancingTrigger.THRESHOLD_BASED,
            threshold_percent=0.03  # 3% threshold
        )
        
        should_rebalance, reason = portfolio_rebalancer.check_rebalancing_trigger(
            current_weights, target_weights, config
        )
        
        assert should_rebalance  # 5% deviation > 3% threshold
        assert "Threshold trigger" in reason
    
    def test_optimal_trades_calculation(self, sample_weights):
        """Test optimal trades calculation."""
        current_weights, target_weights = sample_weights
        portfolio_value = 1000000  # ₹10L
        
        config = RebalancingConfig(
            min_trade_size=0.01,
            transaction_cost=0.001,
            max_turnover=0.50
        )
        
        actions = portfolio_rebalancer.calculate_optimal_trades(
            current_weights, target_weights, portfolio_value, config
        )
        
        assert len(actions) > 0
        
        for action in actions:
            assert action.symbol in current_weights
            assert abs(action.weight_change) >= config.min_trade_size
            assert action.transaction_cost > 0
            assert action.trade_direction in ['BUY', 'SELL']
    
    def test_rebalancing_execution(self, sample_weights):
        """Test complete rebalancing execution."""
        current_weights, target_weights = sample_weights
        portfolio_value = 1000000
        
        config = RebalancingConfig(
            trigger_type=RebalancingTrigger.THRESHOLD_BASED,
            threshold_percent=0.03,
            transaction_cost=0.001
        )
        
        result = portfolio_rebalancer.execute_rebalancing(
            current_weights, target_weights, portfolio_value, config
        )
        
        assert result is not None
        assert result.rebalancing_date == date.today()
        assert len(result.actions) > 0
        assert result.total_turnover > 0
        assert result.total_transaction_cost > 0
        
        # Check that new portfolio weights are closer to target
        for symbol in target_weights:
            new_weight = result.target_portfolio[symbol]
            current_deviation = abs(current_weights[symbol] - target_weights[symbol])
            new_deviation = abs(new_weight - target_weights[symbol])
            assert new_deviation <= current_deviation

class TestPortfolioOptimizer:
    """Test main Portfolio Optimizer."""
    
    @pytest.fixture
    def sample_symbols(self):
        """Create sample symbols for testing."""
        return ['RELIANCE', 'TCS', 'HDFCBANK', 'INFY', 'ICICIBANK']
    
    @patch('src.portfolio_optimization.mpt_optimizer.mpt_optimizer.calculate_returns_covariance')
    def test_optimization_request_processing(self, mock_calc_returns, sample_symbols):
        """Test optimization request processing."""
        # Mock return data
        expected_returns = np.array([0.12, 0.15, 0.10, 0.18, 0.14])
        covariance_matrix = np.eye(5) * 0.04
        mock_calc_returns.return_value = (expected_returns, covariance_matrix)
        
        request = PortfolioOptimizationRequest(
            symbols=sample_symbols,
            method=OptimizationMethod.MAX_SHARPE,
            lookback_days=252
        )
        
        response = portfolio_optimizer.optimize_portfolio(request)
        
        assert response.optimization_result is not None
        assert response.optimization_result.optimization_success
        assert len(response.optimization_result.symbols) == len(sample_symbols)
        assert response.recommendations is not None
    
    @patch('src.portfolio_optimization.mpt_optimizer.mpt_optimizer.calculate_returns_covariance')
    def test_method_comparison(self, mock_calc_returns, sample_symbols):
        """Test optimization method comparison."""
        # Mock return data
        expected_returns = np.array([0.12, 0.15, 0.10, 0.18, 0.14])
        covariance_matrix = np.eye(5) * 0.04
        mock_calc_returns.return_value = (expected_returns, covariance_matrix)
        
        methods = [
            OptimizationMethod.MAX_SHARPE,
            OptimizationMethod.MIN_VARIANCE,
            OptimizationMethod.EQUAL_WEIGHT
        ]
        
        results = portfolio_optimizer.compare_optimization_methods(
            sample_symbols, methods, lookback_days=252
        )
        
        assert len(results) == len(methods)
        
        for method_name, response in results.items():
            assert response.optimization_result is not None
            assert response.optimization_result.optimization_success
    
    def test_request_validation(self, sample_symbols):
        """Test optimization request validation."""
        # Test empty symbols
        with pytest.raises(ValueError, match="No symbols provided"):
            request = PortfolioOptimizationRequest(
                symbols=[],
                method=OptimizationMethod.MAX_SHARPE
            )
            portfolio_optimizer._validate_request(request)
        
        # Test insufficient symbols
        with pytest.raises(ValueError, match="At least 2 symbols required"):
            request = PortfolioOptimizationRequest(
                symbols=['RELIANCE'],
                method=OptimizationMethod.MAX_SHARPE
            )
            portfolio_optimizer._validate_request(request)
        
        # Test target return method without target
        with pytest.raises(ValueError, match="Target return required"):
            request = PortfolioOptimizationRequest(
                symbols=sample_symbols,
                method=OptimizationMethod.TARGET_RETURN
            )
            portfolio_optimizer._validate_request(request)

class TestIntegration:
    """Integration tests for the complete system."""
    
    @patch('src.data.database.db_manager.get_session')
    @patch('src.data.crud.StockCRUD.get_stock_by_symbol')
    @patch('src.data.crud.PriceCRUD.get_recent_prices')
    def test_end_to_end_optimization(self, mock_prices, mock_stock, mock_session):
        """Test end-to-end optimization workflow."""
        # Mock database responses
        mock_session.return_value.__enter__.return_value = Mock()
        mock_stock.return_value = Mock(id=1, symbol='TEST', sector='Technology')
        
        # Mock price data
        mock_price_data = []
        base_price = 100.0
        for i in range(253):  # 253 days of data
            price = base_price * (1 + np.random.normal(0, 0.02))
            mock_price_data.append(Mock(close_price=price))
            base_price = price
        
        mock_prices.return_value = mock_price_data
        
        # Test optimization
        symbols = ['TEST1', 'TEST2', 'TEST3']
        request = PortfolioOptimizationRequest(
            symbols=symbols,
            method=OptimizationMethod.MAX_SHARPE,
            lookback_days=252
        )
        
        try:
            response = portfolio_optimizer.optimize_portfolio(request)
            
            # Basic validation
            assert response.optimization_result is not None
            assert len(response.optimization_result.weights) == len(symbols)
            assert abs(np.sum(response.optimization_result.weights) - 1.0) < 1e-6
            
        except Exception as e:
            # Expected to fail due to mocking, but should not crash
            assert "optimization" in str(e).lower() or "data" in str(e).lower()

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
