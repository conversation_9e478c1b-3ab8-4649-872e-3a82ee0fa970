#!/usr/bin/env python3
"""
Stock Analysis System API Server Startup Script.
"""

import uvicorn
import sys
import os
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def main():
    """Start the API server."""
    
    # Configuration
    config = {
        "app": "src.api.main:app",
        "host": "0.0.0.0",
        "port": 8000,
        "reload": True,  # Set to False in production
        "log_level": "info",
        "access_log": True,
        "workers": 1,  # Increase for production
    }
    
    print("🚀 Starting Stock Analysis System API Server...")
    print(f"📡 Server will be available at: http://localhost:{config['port']}")
    print(f"📚 API Documentation: http://localhost:{config['port']}/docs")
    print(f"🔄 ReDoc Documentation: http://localhost:{config['port']}/redoc")
    print(f"💻 Dashboard: http://localhost:{config['port']}/")
    print("=" * 60)
    
    # Start server
    uvicorn.run(**config)

if __name__ == "__main__":
    main()
