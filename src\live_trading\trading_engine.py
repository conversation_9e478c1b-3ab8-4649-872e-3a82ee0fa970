"""
Live Trading Engine - Orchestrates live trading operations.
Integrates signals, risk management, order execution, and portfolio management.
"""

import asyncio
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, time, date
from enum import Enum
from dataclasses import dataclass
import json

from .broker_integration import get_broker, initialize_broker, OrderSide, OrderType, ProductType
from .order_manager import order_manager, OrderRequest, OrderPriority, initialize_order_manager
from .risk_manager import risk_manager, initialize_risk_manager
from ..strategies.strategy_registry import strategy_registry
from ..rl_optimizer.rl_integration import rl_integration
from ..data.database import db_manager
from ..data.crud import StockCRUD, SignalCRUD
from ..data.models import StrategySignal
from ..utils.logger import get_logger
from ..utils.config import config

logger = get_logger(__name__)

class TradingMode(Enum):
    """Trading modes."""
    PAPER = "PAPER"  # Paper trading
    LIVE = "LIVE"    # Live trading
    SIMULATION = "SIMULATION"  # Simulation mode

class TradingStatus(Enum):
    """Trading engine status."""
    STOPPED = "STOPPED"
    STARTING = "STARTING"
    RUNNING = "RUNNING"
    PAUSED = "PAUSED"
    ERROR = "ERROR"

@dataclass
class TradingSession:
    """Trading session information."""
    session_id: str
    start_time: datetime
    end_time: Optional[datetime]
    mode: TradingMode
    strategies_enabled: List[str]
    total_orders: int
    successful_orders: int
    total_pnl: float
    status: TradingStatus

class LiveTradingEngine:
    """Main live trading engine."""
    
    def __init__(self):
        """Initialize trading engine."""
        self.broker = get_broker()
        self.status = TradingStatus.STOPPED
        self.mode = TradingMode.PAPER  # Default to paper trading
        
        # Configuration
        self.config = config.get_trading_config()
        self.trading_enabled = self.config.get('trading_enabled', False)
        self.market_hours = self.config.get('market_hours', {'start': '09:15', 'end': '15:30'})
        self.signal_processing_interval = self.config.get('signal_processing_interval', 30)  # seconds
        
        # Trading session
        self.current_session: Optional[TradingSession] = None
        self.session_history: List[TradingSession] = []
        
        # Signal processing
        self.processed_signals: Dict[str, datetime] = {}
        self.signal_callbacks: List[Callable] = []
        
        # Performance tracking
        self.daily_stats = {
            'orders_placed': 0,
            'orders_filled': 0,
            'total_pnl': 0.0,
            'successful_trades': 0,
            'failed_trades': 0
        }
        
        # Market data cache
        self.market_data_cache: Dict[str, Dict[str, Any]] = {}
        
        logger.info("Live Trading Engine initialized")
    
    async def initialize(self) -> bool:
        """Initialize trading engine components."""
        try:
            logger.info("Initializing live trading engine...")
            
            # Initialize broker
            broker_connected = await initialize_broker()
            if not broker_connected:
                logger.error("Failed to connect to broker")
                return False
            
            # Initialize order manager
            order_manager_ready = await initialize_order_manager()
            if not order_manager_ready:
                logger.error("Failed to initialize order manager")
                return False
            
            # Initialize risk manager
            risk_manager_ready = await initialize_risk_manager()
            if not risk_manager_ready:
                logger.error("Failed to initialize risk manager")
                return False
            
            # Register order callbacks
            order_manager.register_callback('on_order_filled', self._on_order_filled)
            order_manager.register_callback('on_order_rejected', self._on_order_rejected)
            
            logger.info("Live trading engine initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing trading engine: {e}")
            return False
    
    async def start_trading(self, mode: TradingMode = TradingMode.PAPER) -> bool:
        """Start live trading."""
        try:
            if self.status == TradingStatus.RUNNING:
                logger.warning("Trading engine is already running")
                return True
            
            if not self.trading_enabled and mode == TradingMode.LIVE:
                logger.error("Live trading is disabled in configuration")
                return False
            
            self.status = TradingStatus.STARTING
            self.mode = mode
            
            # Create new trading session
            self.current_session = TradingSession(
                session_id=f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                start_time=datetime.now(),
                end_time=None,
                mode=mode,
                strategies_enabled=list(strategy_registry.get_enabled_strategies().keys()),
                total_orders=0,
                successful_orders=0,
                total_pnl=0.0,
                status=TradingStatus.RUNNING
            )
            
            # Start trading tasks
            asyncio.create_task(self._signal_processing_loop())
            asyncio.create_task(self._market_data_update_loop())
            asyncio.create_task(self._performance_tracking_loop())
            
            self.status = TradingStatus.RUNNING
            
            logger.info(f"Live trading started in {mode.value} mode")
            return True
            
        except Exception as e:
            logger.error(f"Error starting trading: {e}")
            self.status = TradingStatus.ERROR
            return False
    
    async def stop_trading(self) -> bool:
        """Stop live trading."""
        try:
            if self.status == TradingStatus.STOPPED:
                return True
            
            self.status = TradingStatus.STOPPED
            
            # End current session
            if self.current_session:
                self.current_session.end_time = datetime.now()
                self.current_session.status = TradingStatus.STOPPED
                self.session_history.append(self.current_session)
                self.current_session = None
            
            # Cancel all pending orders
            all_orders = await order_manager.get_all_orders()
            for order in all_orders['active']:
                await order_manager.cancel_order(order.order_id)
            
            logger.info("Live trading stopped")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping trading: {e}")
            return False
    
    async def pause_trading(self) -> bool:
        """Pause live trading."""
        try:
            if self.status == TradingStatus.RUNNING:
                self.status = TradingStatus.PAUSED
                logger.info("Live trading paused")
                return True
            return False
        except Exception as e:
            logger.error(f"Error pausing trading: {e}")
            return False
    
    async def resume_trading(self) -> bool:
        """Resume live trading."""
        try:
            if self.status == TradingStatus.PAUSED:
                self.status = TradingStatus.RUNNING
                logger.info("Live trading resumed")
                return True
            return False
        except Exception as e:
            logger.error(f"Error resuming trading: {e}")
            return False
    
    async def process_signal(self, signal: StrategySignal) -> bool:
        """Process a trading signal."""
        try:
            if self.status != TradingStatus.RUNNING:
                logger.debug(f"Ignoring signal - trading not running: {signal.symbol}")
                return False
            
            # Check if signal already processed
            signal_key = f"{signal.strategy_name}_{signal.symbol}_{signal.signal_type}"
            if signal_key in self.processed_signals:
                time_diff = datetime.now() - self.processed_signals[signal_key]
                if time_diff.total_seconds() < 300:  # 5 minutes cooldown
                    logger.debug(f"Signal recently processed: {signal_key}")
                    return False
            
            # Check market hours
            if not self._is_market_open():
                logger.debug(f"Market closed - ignoring signal: {signal.symbol}")
                return False
            
            # Get RL-optimized parameters
            rl_params = rl_integration.get_optimized_parameters_for_strategy(
                signal.strategy_name, signal.symbol
            )
            
            # Calculate position size
            volatility = await self._get_symbol_volatility(signal.symbol)
            position_size = await risk_manager.calculate_position_size(
                signal.symbol, signal.signal_strength, volatility
            )
            
            # Determine order type and price
            order_type = OrderType.MARKET  # Default to market orders
            price = None
            
            if signal.signal_type == 'BUY':
                order_side = OrderSide.BUY
                # For buy orders, might use limit orders slightly above current price
                if self.mode == TradingMode.LIVE:
                    current_price = await self.broker.get_ltp(signal.symbol)
                    price = current_price * 1.001  # 0.1% above market
                    order_type = OrderType.LIMIT
            elif signal.signal_type == 'SELL':
                order_side = OrderSide.SELL
                # For sell orders, might use limit orders slightly below current price
                if self.mode == TradingMode.LIVE:
                    current_price = await self.broker.get_ltp(signal.symbol)
                    price = current_price * 0.999  # 0.1% below market
                    order_type = OrderType.LIMIT
            else:
                logger.debug(f"Ignoring HOLD signal: {signal.symbol}")
                return False
            
            # Create order request
            order_request = OrderRequest(
                symbol=signal.symbol,
                side=order_side,
                quantity=position_size,
                order_type=order_type,
                price=price,
                product_type=ProductType.CNC,  # Default to delivery
                priority=self._get_order_priority(signal.signal_strength),
                strategy_name=signal.strategy_name,
                signal_id=str(signal.id),
                metadata={
                    'signal_strength': signal.signal_strength,
                    'confidence_score': signal.confidence_score,
                    'rl_params': rl_params
                }
            )
            
            # Validate with risk manager
            risk_valid, risk_message = await risk_manager.validate_order(order_request)
            if not risk_valid:
                logger.warning(f"Order rejected by risk manager: {risk_message}")
                return False
            
            # Submit order
            order_id = await order_manager.submit_order(order_request)
            
            # Mark signal as processed
            self.processed_signals[signal_key] = datetime.now()
            
            # Update session stats
            if self.current_session:
                self.current_session.total_orders += 1
            
            self.daily_stats['orders_placed'] += 1
            
            logger.info(f"Signal processed: {signal.symbol} {signal.signal_type} - Order ID: {order_id}")
            
            # Trigger callbacks
            for callback in self.signal_callbacks:
                try:
                    await callback(signal, order_id)
                except Exception as e:
                    logger.error(f"Error in signal callback: {e}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error processing signal: {e}")
            return False
    
    async def get_trading_status(self) -> Dict[str, Any]:
        """Get comprehensive trading status."""
        try:
            # Get positions and balance
            positions = await self.broker.get_positions()
            balance = await self.broker.get_balance()
            
            # Get order statistics
            order_stats = order_manager.get_statistics()
            
            # Get risk metrics
            portfolio_risk = await risk_manager.get_portfolio_risk()
            risk_violations = await risk_manager.get_risk_violations()
            
            return {
                'status': self.status.value,
                'mode': self.mode.value,
                'trading_enabled': self.trading_enabled,
                'market_open': self._is_market_open(),
                'current_session': {
                    'session_id': self.current_session.session_id if self.current_session else None,
                    'start_time': self.current_session.start_time.isoformat() if self.current_session else None,
                    'strategies_enabled': self.current_session.strategies_enabled if self.current_session else [],
                    'total_orders': self.current_session.total_orders if self.current_session else 0
                },
                'daily_stats': self.daily_stats,
                'positions': len(positions),
                'available_cash': balance.get('available_cash', 0),
                'order_stats': order_stats,
                'portfolio_risk': {
                    'total_value': portfolio_risk.total_value if portfolio_risk else 0,
                    'daily_pnl': portfolio_risk.daily_pnl if portfolio_risk else 0,
                    'current_drawdown': portfolio_risk.current_drawdown if portfolio_risk else 0
                },
                'risk_violations': len(risk_violations),
                'last_updated': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting trading status: {e}")
            return {
                'status': 'ERROR',
                'error': str(e),
                'last_updated': datetime.now().isoformat()
            }
    
    def register_signal_callback(self, callback: Callable):
        """Register signal processing callback."""
        self.signal_callbacks.append(callback)
    
    async def _signal_processing_loop(self):
        """Main signal processing loop."""
        while self.status in [TradingStatus.RUNNING, TradingStatus.PAUSED]:
            try:
                if self.status == TradingStatus.RUNNING:
                    # Get latest signals from database
                    with db_manager.get_session() as db:
                        recent_signals = SignalCRUD.get_latest_signals(
                            db, limit=50, hours_back=1
                        )
                    
                    # Process each signal
                    for signal in recent_signals:
                        if signal.is_valid and not signal.processed:
                            await self.process_signal(signal)
                            
                            # Mark signal as processed in database
                            with db_manager.get_session() as db:
                                signal.processed = True
                                db.commit()
                
                await asyncio.sleep(self.signal_processing_interval)
                
            except Exception as e:
                logger.error(f"Error in signal processing loop: {e}")
                await asyncio.sleep(60)
    
    async def _market_data_update_loop(self):
        """Update market data cache."""
        while self.status in [TradingStatus.RUNNING, TradingStatus.PAUSED]:
            try:
                if self.status == TradingStatus.RUNNING and self._is_market_open():
                    # Get active symbols
                    positions = await self.broker.get_positions()
                    symbols = [pos.symbol for pos in positions]
                    
                    # Add Nifty 50 symbols for monitoring
                    with db_manager.get_session() as db:
                        nifty50_stocks = StockCRUD.get_nifty50_stocks(db)
                        symbols.extend([stock.symbol for stock in nifty50_stocks[:10]])
                    
                    # Update market data
                    for symbol in set(symbols):
                        try:
                            ltp = await self.broker.get_ltp(symbol)
                            self.market_data_cache[symbol] = {
                                'ltp': ltp,
                                'timestamp': datetime.now()
                            }
                        except Exception as e:
                            logger.error(f"Error updating market data for {symbol}: {e}")
                
                await asyncio.sleep(10)  # Update every 10 seconds
                
            except Exception as e:
                logger.error(f"Error in market data update loop: {e}")
                await asyncio.sleep(30)
    
    async def _performance_tracking_loop(self):
        """Track performance metrics."""
        while self.status in [TradingStatus.RUNNING, TradingStatus.PAUSED]:
            try:
                if self.status == TradingStatus.RUNNING:
                    # Update daily P&L
                    portfolio_risk = await risk_manager.get_portfolio_risk()
                    if portfolio_risk:
                        self.daily_stats['total_pnl'] = portfolio_risk.daily_pnl
                        
                        # Update session P&L
                        if self.current_session:
                            self.current_session.total_pnl = portfolio_risk.daily_pnl
                
                await asyncio.sleep(60)  # Update every minute
                
            except Exception as e:
                logger.error(f"Error in performance tracking loop: {e}")
                await asyncio.sleep(120)
    
    async def _on_order_filled(self, order):
        """Handle order fill callback."""
        try:
            self.daily_stats['orders_filled'] += 1
            self.daily_stats['successful_trades'] += 1
            
            if self.current_session:
                self.current_session.successful_orders += 1
            
            logger.info(f"Order filled: {order.symbol} {order.side.value} {order.filled_quantity}")
            
        except Exception as e:
            logger.error(f"Error handling order fill: {e}")
    
    async def _on_order_rejected(self, order):
        """Handle order rejection callback."""
        try:
            self.daily_stats['failed_trades'] += 1
            logger.warning(f"Order rejected: {order.symbol} - {order.message}")
            
        except Exception as e:
            logger.error(f"Error handling order rejection: {e}")
    
    def _is_market_open(self) -> bool:
        """Check if market is open."""
        try:
            now = datetime.now().time()
            start_time = time.fromisoformat(self.market_hours['start'])
            end_time = time.fromisoformat(self.market_hours['end'])
            
            # Check if current time is within market hours
            if start_time <= now <= end_time:
                # Check if it's a weekday
                weekday = datetime.now().weekday()
                return weekday < 5  # Monday = 0, Friday = 4
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking market hours: {e}")
            return False
    
    def _get_order_priority(self, signal_strength: float) -> OrderPriority:
        """Get order priority based on signal strength."""
        if signal_strength >= 0.8:
            return OrderPriority.HIGH
        elif signal_strength >= 0.6:
            return OrderPriority.NORMAL
        else:
            return OrderPriority.LOW
    
    async def _get_symbol_volatility(self, symbol: str) -> float:
        """Get symbol volatility (simplified)."""
        try:
            # Use risk manager's volatility calculation
            return await risk_manager._get_symbol_volatility(symbol)
        except Exception as e:
            logger.error(f"Error getting volatility for {symbol}: {e}")
            return 0.25  # Default volatility

# Global trading engine instance
trading_engine = LiveTradingEngine()

async def initialize_trading_engine() -> bool:
    """Initialize trading engine."""
    return await trading_engine.initialize()

async def start_live_trading(mode: TradingMode = TradingMode.PAPER) -> bool:
    """Start live trading."""
    return await trading_engine.start_trading(mode)

async def stop_live_trading() -> bool:
    """Stop live trading."""
    return await trading_engine.stop_trading()
