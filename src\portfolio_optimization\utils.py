"""
Portfolio Optimization Utilities.
Helper functions and utilities for portfolio optimization and analytics.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime, date, timedelta
import warnings
warnings.filterwarnings('ignore')

from ..data.database import db_manager
from ..data.crud import StockCRUD, PriceCRUD
from ..utils.logger import get_logger

logger = get_logger(__name__)

class PortfolioUtils:
    """Utility functions for portfolio optimization."""
    
    @staticmethod
    def validate_weights(weights: np.ndarray, tolerance: float = 1e-6) -> bool:
        """Validate portfolio weights."""
        try:
            # Check if weights sum to 1
            if abs(np.sum(weights) - 1.0) > tolerance:
                return False
            
            # Check for negative weights
            if np.any(weights < -tolerance):
                return False
            
            # Check for NaN or infinite values
            if np.any(~np.isfinite(weights)):
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating weights: {e}")
            return False
    
    @staticmethod
    def normalize_weights(weights: np.ndarray) -> np.ndarray:
        """Normalize weights to sum to 1."""
        try:
            weights = np.array(weights)
            
            # Handle negative weights by setting to zero
            weights = np.maximum(weights, 0)
            
            # Normalize to sum to 1
            total_weight = np.sum(weights)
            if total_weight > 0:
                weights = weights / total_weight
            else:
                # Equal weights if all are zero
                weights = np.ones(len(weights)) / len(weights)
            
            return weights
            
        except Exception as e:
            logger.error(f"Error normalizing weights: {e}")
            return np.ones(len(weights)) / len(weights)
    
    @staticmethod
    def calculate_portfolio_metrics(
        weights: np.ndarray,
        expected_returns: np.ndarray,
        covariance_matrix: np.ndarray,
        risk_free_rate: float = 0.06
    ) -> Dict[str, float]:
        """Calculate basic portfolio metrics."""
        try:
            # Portfolio return
            portfolio_return = np.dot(weights, expected_returns)
            
            # Portfolio variance and volatility
            portfolio_variance = np.dot(weights.T, np.dot(covariance_matrix, weights))
            portfolio_volatility = np.sqrt(portfolio_variance)
            
            # Sharpe ratio
            sharpe_ratio = (portfolio_return - risk_free_rate) / portfolio_volatility if portfolio_volatility > 0 else 0
            
            # Diversification ratio
            individual_volatilities = np.sqrt(np.diag(covariance_matrix))
            weighted_avg_volatility = np.dot(weights, individual_volatilities)
            diversification_ratio = weighted_avg_volatility / portfolio_volatility if portfolio_volatility > 0 else 1
            
            return {
                'expected_return': float(portfolio_return),
                'volatility': float(portfolio_volatility),
                'variance': float(portfolio_variance),
                'sharpe_ratio': float(sharpe_ratio),
                'diversification_ratio': float(diversification_ratio)
            }
            
        except Exception as e:
            logger.error(f"Error calculating portfolio metrics: {e}")
            return {}
    
    @staticmethod
    def check_covariance_matrix(covariance_matrix: np.ndarray) -> Dict[str, Any]:
        """Check covariance matrix properties."""
        try:
            n = covariance_matrix.shape[0]
            
            # Check if square
            is_square = covariance_matrix.shape[0] == covariance_matrix.shape[1]
            
            # Check if symmetric
            is_symmetric = np.allclose(covariance_matrix, covariance_matrix.T)
            
            # Check if positive semi-definite
            eigenvalues = np.linalg.eigvals(covariance_matrix)
            is_positive_semidefinite = np.all(eigenvalues >= -1e-8)
            
            # Condition number
            condition_number = np.linalg.cond(covariance_matrix)
            
            # Determinant
            determinant = np.linalg.det(covariance_matrix)
            
            return {
                'is_square': is_square,
                'is_symmetric': is_symmetric,
                'is_positive_semidefinite': is_positive_semidefinite,
                'condition_number': float(condition_number),
                'determinant': float(determinant),
                'min_eigenvalue': float(np.min(eigenvalues)),
                'max_eigenvalue': float(np.max(eigenvalues)),
                'rank': int(np.linalg.matrix_rank(covariance_matrix)),
                'size': n
            }
            
        except Exception as e:
            logger.error(f"Error checking covariance matrix: {e}")
            return {}
    
    @staticmethod
    def regularize_covariance_matrix(
        covariance_matrix: np.ndarray,
        regularization: float = 1e-8
    ) -> np.ndarray:
        """Regularize covariance matrix for numerical stability."""
        try:
            # Add regularization to diagonal
            regularized_matrix = covariance_matrix + regularization * np.eye(covariance_matrix.shape[0])
            
            # Ensure positive definiteness
            eigenvalues, eigenvectors = np.linalg.eigh(regularized_matrix)
            eigenvalues = np.maximum(eigenvalues, regularization)
            regularized_matrix = eigenvectors @ np.diag(eigenvalues) @ eigenvectors.T
            
            return regularized_matrix
            
        except Exception as e:
            logger.error(f"Error regularizing covariance matrix: {e}")
            return covariance_matrix
    
    @staticmethod
    def calculate_correlation_matrix(covariance_matrix: np.ndarray) -> np.ndarray:
        """Calculate correlation matrix from covariance matrix."""
        try:
            volatilities = np.sqrt(np.diag(covariance_matrix))
            correlation_matrix = covariance_matrix / np.outer(volatilities, volatilities)
            
            # Ensure diagonal is 1
            np.fill_diagonal(correlation_matrix, 1.0)
            
            return correlation_matrix
            
        except Exception as e:
            logger.error(f"Error calculating correlation matrix: {e}")
            return np.eye(covariance_matrix.shape[0])
    
    @staticmethod
    def get_nifty50_symbols() -> List[str]:
        """Get Nifty 50 symbols from database."""
        try:
            with db_manager.get_session() as db:
                nifty50_stocks = StockCRUD.get_nifty50_stocks(db)
                return [stock.symbol for stock in nifty50_stocks]
                
        except Exception as e:
            logger.error(f"Error getting Nifty 50 symbols: {e}")
            # Return default symbols if database fails
            return [
                'RELIANCE', 'TCS', 'HDFCBANK', 'INFY', 'HINDUNILVR',
                'ICICIBANK', 'KOTAKBANK', 'SBIN', 'BHARTIARTL', 'ITC',
                'ASIANPAINT', 'LT', 'AXISBANK', 'MARUTI', 'SUNPHARMA',
                'ULTRACEMCO', 'TITAN', 'NESTLEIND', 'BAJFINANCE', 'WIPRO'
            ]
    
    @staticmethod
    def get_sector_mapping(symbols: List[str]) -> Dict[str, str]:
        """Get sector mapping for symbols."""
        try:
            sector_mapping = {}
            
            with db_manager.get_session() as db:
                for symbol in symbols:
                    stock = StockCRUD.get_stock_by_symbol(db, symbol)
                    if stock and stock.sector:
                        sector_mapping[symbol] = stock.sector
                    else:
                        sector_mapping[symbol] = 'Unknown'
            
            return sector_mapping
            
        except Exception as e:
            logger.error(f"Error getting sector mapping: {e}")
            return {symbol: 'Unknown' for symbol in symbols}
    
    @staticmethod
    def calculate_sector_weights(
        weights: Dict[str, float],
        sector_mapping: Dict[str, str]
    ) -> Dict[str, float]:
        """Calculate sector weights from individual stock weights."""
        try:
            sector_weights = {}
            
            for symbol, weight in weights.items():
                sector = sector_mapping.get(symbol, 'Unknown')
                if sector not in sector_weights:
                    sector_weights[sector] = 0.0
                sector_weights[sector] += weight
            
            return sector_weights
            
        except Exception as e:
            logger.error(f"Error calculating sector weights: {e}")
            return {}
    
    @staticmethod
    def format_optimization_result(result: Any) -> Dict[str, Any]:
        """Format optimization result for API response."""
        try:
            if hasattr(result, 'weights') and hasattr(result, 'symbols'):
                weights_dict = {
                    symbol: float(weight) for symbol, weight in 
                    zip(result.symbols, result.weights)
                }
                
                formatted_result = {
                    'weights': weights_dict,
                    'expected_return': float(result.expected_return),
                    'volatility': float(result.volatility),
                    'sharpe_ratio': float(result.sharpe_ratio),
                    'optimization_method': result.optimization_method,
                    'optimization_success': result.optimization_success
                }
                
                if hasattr(result, 'additional_metrics'):
                    formatted_result['additional_metrics'] = result.additional_metrics
                
                return formatted_result
            
            return {}
            
        except Exception as e:
            logger.error(f"Error formatting optimization result: {e}")
            return {}
    
    @staticmethod
    def validate_symbols(symbols: List[str]) -> Tuple[List[str], List[str]]:
        """Validate symbols and return valid and invalid lists."""
        try:
            valid_symbols = []
            invalid_symbols = []
            
            with db_manager.get_session() as db:
                for symbol in symbols:
                    stock = StockCRUD.get_stock_by_symbol(db, symbol)
                    if stock and stock.is_active:
                        valid_symbols.append(symbol)
                    else:
                        invalid_symbols.append(symbol)
            
            return valid_symbols, invalid_symbols
            
        except Exception as e:
            logger.error(f"Error validating symbols: {e}")
            return symbols, []  # Assume all valid if validation fails
    
    @staticmethod
    def calculate_turnover(
        current_weights: Dict[str, float],
        target_weights: Dict[str, float]
    ) -> float:
        """Calculate portfolio turnover."""
        try:
            turnover = 0.0
            all_symbols = set(current_weights.keys()) | set(target_weights.keys())
            
            for symbol in all_symbols:
                current_weight = current_weights.get(symbol, 0.0)
                target_weight = target_weights.get(symbol, 0.0)
                turnover += abs(target_weight - current_weight)
            
            return turnover / 2.0  # Divide by 2 as turnover is half of total weight changes
            
        except Exception as e:
            logger.error(f"Error calculating turnover: {e}")
            return 0.0
    
    @staticmethod
    def generate_random_portfolio(
        n_assets: int,
        method: str = "uniform"
    ) -> np.ndarray:
        """Generate random portfolio weights."""
        try:
            if method == "uniform":
                # Uniform random weights
                weights = np.random.uniform(0, 1, n_assets)
            elif method == "dirichlet":
                # Dirichlet distribution (more realistic)
                weights = np.random.dirichlet(np.ones(n_assets))
            elif method == "exponential":
                # Exponential distribution (concentrated)
                weights = np.random.exponential(1, n_assets)
            else:
                # Default to uniform
                weights = np.random.uniform(0, 1, n_assets)
            
            # Normalize to sum to 1
            weights = weights / np.sum(weights)
            
            return weights
            
        except Exception as e:
            logger.error(f"Error generating random portfolio: {e}")
            return np.ones(n_assets) / n_assets
    
    @staticmethod
    def calculate_tracking_error(
        portfolio_returns: np.ndarray,
        benchmark_returns: np.ndarray
    ) -> float:
        """Calculate tracking error."""
        try:
            if len(portfolio_returns) != len(benchmark_returns):
                raise ValueError("Portfolio and benchmark returns must have same length")
            
            active_returns = portfolio_returns - benchmark_returns
            tracking_error = np.std(active_returns) * np.sqrt(252)  # Annualized
            
            return float(tracking_error)
            
        except Exception as e:
            logger.error(f"Error calculating tracking error: {e}")
            return 0.0
    
    @staticmethod
    def calculate_information_ratio(
        portfolio_returns: np.ndarray,
        benchmark_returns: np.ndarray
    ) -> float:
        """Calculate information ratio."""
        try:
            if len(portfolio_returns) != len(benchmark_returns):
                raise ValueError("Portfolio and benchmark returns must have same length")
            
            active_returns = portfolio_returns - benchmark_returns
            
            if np.std(active_returns) == 0:
                return 0.0
            
            information_ratio = np.mean(active_returns) / np.std(active_returns) * np.sqrt(252)
            
            return float(information_ratio)
            
        except Exception as e:
            logger.error(f"Error calculating information ratio: {e}")
            return 0.0
    
    @staticmethod
    def create_benchmark_portfolio(
        symbols: List[str],
        method: str = "equal_weight"
    ) -> Dict[str, float]:
        """Create benchmark portfolio."""
        try:
            if method == "equal_weight":
                weight = 1.0 / len(symbols)
                return {symbol: weight for symbol in symbols}
            
            elif method == "market_cap":
                # Market cap weighted (simplified)
                with db_manager.get_session() as db:
                    market_caps = []
                    valid_symbols = []
                    
                    for symbol in symbols:
                        stock = StockCRUD.get_stock_by_symbol(db, symbol)
                        if stock and stock.market_cap:
                            market_caps.append(float(stock.market_cap))
                            valid_symbols.append(symbol)
                    
                    if market_caps:
                        total_market_cap = sum(market_caps)
                        return {
                            symbol: market_cap / total_market_cap 
                            for symbol, market_cap in zip(valid_symbols, market_caps)
                        }
            
            # Default to equal weight
            weight = 1.0 / len(symbols)
            return {symbol: weight for symbol in symbols}
            
        except Exception as e:
            logger.error(f"Error creating benchmark portfolio: {e}")
            weight = 1.0 / len(symbols)
            return {symbol: weight for symbol in symbols}

# Global utility instance
portfolio_utils = PortfolioUtils()
