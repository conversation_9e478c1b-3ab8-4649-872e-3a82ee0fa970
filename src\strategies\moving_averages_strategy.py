"""
Dynamic Moving Averages Strategy Implementation.
Features Golden/Death Cross detection and ADX-based market condition analysis.
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

from .base_strategy import BaseStrategy, TradingSignal, SignalType, SignalStrength
from .strategy_utils import TechnicalAnalysis, SignalFilters
from ..utils.logger import get_logger
from ..utils.config import config

logger = get_logger(__name__)

class DynamicMovingAveragesStrategy(BaseStrategy):
    """
    Dynamic Moving Averages Strategy with adaptive weighting.
    
    Features:
    - Golden Cross (50 DMA > 200 DMA) and Death Cross detection
    - Exponential weighting in trending markets vs. simple MA in ranging markets
    - ADX indicator to determine market condition
    - Multiple timeframe confirmation
    - Volume and momentum confirmation
    - Dynamic position sizing based on trend strength
    """
    
    def __init__(self, parameters: Optional[Dict[str, Any]] = None):
        """Initialize Dynamic Moving Averages Strategy.
        
        Args:
            parameters: Strategy parameters override
        """
        default_parameters = {
            'short_period': 50,
            'long_period': 200,
            'adx_period': 14,
            'adx_threshold': 25,
            'trending_threshold': 30,
            'ranging_threshold': 20,
            'volume_confirmation': True,
            'momentum_confirmation': True,
            'multiple_timeframes': True,
            'min_signal_strength': 0.6,
            'use_exponential_in_trends': True,
            'cross_confirmation_bars': 2
        }
        
        if parameters:
            default_parameters.update(parameters)
        
        super().__init__("dynamic_moving_averages", default_parameters)
        
        # Strategy-specific attributes
        self.short_period = self.parameters['short_period']
        self.long_period = self.parameters['long_period']
        self.adx_period = self.parameters['adx_period']
        self.adx_threshold = self.parameters['adx_threshold']
        self.trending_threshold = self.parameters['trending_threshold']
        self.ranging_threshold = self.parameters['ranging_threshold']
        self.volume_confirmation = self.parameters['volume_confirmation']
        self.momentum_confirmation = self.parameters['momentum_confirmation']
        self.multiple_timeframes = self.parameters['multiple_timeframes']
        self.use_exponential_in_trends = self.parameters['use_exponential_in_trends']
        self.cross_confirmation_bars = self.parameters['cross_confirmation_bars']
    
    def get_required_indicators(self) -> List[str]:
        """Get required technical indicators."""
        return [
            'sma_50', 'sma_200', 'ema_12', 'ema_26',
            'volume_ratio', 'rsi_14', 'adx'
        ]
    
    def calculate_dynamic_ma(
        self, 
        prices: pd.Series, 
        period: int, 
        adx_values: pd.Series,
        use_exponential_in_trends: bool = True
    ) -> pd.Series:
        """Calculate dynamic moving average based on market condition.
        
        Args:
            prices: Price series
            period: MA period
            adx_values: ADX values for trend detection
            use_exponential_in_trends: Whether to use EMA in trending markets
            
        Returns:
            Dynamic moving average series
        """
        if not use_exponential_in_trends:
            return prices.rolling(window=period).mean()
        
        # Initialize result series
        dynamic_ma = pd.Series(index=prices.index, dtype=float)
        
        # Calculate both SMA and EMA
        sma = prices.rolling(window=period).mean()
        ema = prices.ewm(span=period, adjust=False).mean()
        
        for i in range(len(prices)):
            if i < period or pd.isna(adx_values.iloc[i]):
                dynamic_ma.iloc[i] = sma.iloc[i]
            else:
                adx_value = adx_values.iloc[i]
                
                if adx_value >= self.trending_threshold:
                    # Strong trend: use EMA (more responsive)
                    dynamic_ma.iloc[i] = ema.iloc[i]
                elif adx_value <= self.ranging_threshold:
                    # Ranging market: use SMA (less noise)
                    dynamic_ma.iloc[i] = sma.iloc[i]
                else:
                    # Moderate trend: blend EMA and SMA
                    blend_ratio = (adx_value - self.ranging_threshold) / (self.trending_threshold - self.ranging_threshold)
                    dynamic_ma.iloc[i] = (blend_ratio * ema.iloc[i]) + ((1 - blend_ratio) * sma.iloc[i])
        
        return dynamic_ma
    
    def detect_ma_cross(
        self, 
        short_ma: pd.Series, 
        long_ma: pd.Series, 
        index: int
    ) -> Optional[str]:
        """Detect moving average crossover.
        
        Args:
            short_ma: Short period moving average
            long_ma: Long period moving average
            index: Current index
            
        Returns:
            'golden' for golden cross, 'death' for death cross, None otherwise
        """
        if index < 1:
            return None
        
        prev_short = short_ma.iloc[index-1]
        prev_long = long_ma.iloc[index-1]
        curr_short = short_ma.iloc[index]
        curr_long = long_ma.iloc[index]
        
        # Golden Cross: short MA crosses above long MA
        if prev_short <= prev_long and curr_short > curr_long:
            return 'golden'
        
        # Death Cross: short MA crosses below long MA
        elif prev_short >= prev_long and curr_short < curr_long:
            return 'death'
        
        return None
    
    def confirm_cross_with_bars(
        self, 
        short_ma: pd.Series, 
        long_ma: pd.Series, 
        index: int, 
        cross_type: str
    ) -> bool:
        """Confirm crossover with multiple bars.
        
        Args:
            short_ma: Short period moving average
            long_ma: Long period moving average
            index: Current index
            cross_type: 'golden' or 'death'
            
        Returns:
            True if crossover is confirmed
        """
        if index < self.cross_confirmation_bars:
            return False
        
        confirmation_count = 0
        
        for i in range(self.cross_confirmation_bars):
            bar_index = index - i
            short_val = short_ma.iloc[bar_index]
            long_val = long_ma.iloc[bar_index]
            
            if cross_type == 'golden' and short_val > long_val:
                confirmation_count += 1
            elif cross_type == 'death' and short_val < long_val:
                confirmation_count += 1
        
        return confirmation_count >= self.cross_confirmation_bars
    
    def calculate_trend_strength(
        self, 
        short_ma: pd.Series, 
        long_ma: pd.Series, 
        adx: pd.Series, 
        index: int
    ) -> float:
        """Calculate trend strength score.
        
        Args:
            short_ma: Short period moving average
            long_ma: Long period moving average
            adx: ADX values
            index: Current index
            
        Returns:
            Trend strength score (0.0 to 1.0)
        """
        if index < 10 or pd.isna(adx.iloc[index]):
            return 0.0
        
        # ADX component (0-1 scale)
        adx_strength = min(adx.iloc[index] / 50, 1.0)  # Normalize to 50 max
        
        # MA separation component
        ma_separation = abs(short_ma.iloc[index] - long_ma.iloc[index]) / long_ma.iloc[index]
        ma_strength = min(ma_separation / 0.05, 1.0)  # Normalize to 5% max separation
        
        # MA slope component (trend consistency)
        short_slope = (short_ma.iloc[index] - short_ma.iloc[index-5]) / short_ma.iloc[index-5] if index >= 5 else 0
        long_slope = (long_ma.iloc[index] - long_ma.iloc[index-10]) / long_ma.iloc[index-10] if index >= 10 else 0
        
        slope_strength = min(abs(short_slope) + abs(long_slope), 0.1) / 0.1  # Normalize to 10% max
        
        # Combine components
        trend_strength = (adx_strength * 0.5) + (ma_strength * 0.3) + (slope_strength * 0.2)
        
        return min(1.0, trend_strength)
    
    def analyze_multiple_timeframes(
        self, 
        data: pd.DataFrame, 
        index: int
    ) -> Dict[str, bool]:
        """Analyze moving averages across multiple timeframes.
        
        Args:
            data: DataFrame with price and MA data
            index: Current index
            
        Returns:
            Dictionary with timeframe analysis
        """
        if not self.multiple_timeframes or index < 50:
            return {
                'short_term_bullish': False,
                'medium_term_bullish': False,
                'long_term_bullish': False
            }
        
        current_price = data['close_price'].iloc[index]
        
        # Short-term: 20-day MA
        short_term_ma = data['close_price'].iloc[max(0, index-19):index+1].mean()
        short_term_bullish = current_price > short_term_ma
        
        # Medium-term: 50-day MA
        medium_term_bullish = current_price > data['sma_50'].iloc[index] if not pd.isna(data['sma_50'].iloc[index]) else False
        
        # Long-term: 200-day MA
        long_term_bullish = current_price > data['sma_200'].iloc[index] if not pd.isna(data['sma_200'].iloc[index]) else False
        
        return {
            'short_term_bullish': short_term_bullish,
            'medium_term_bullish': medium_term_bullish,
            'long_term_bullish': long_term_bullish
        }
    
    def calculate_momentum_score(self, data: pd.DataFrame, index: int) -> float:
        """Calculate momentum confirmation score.
        
        Args:
            data: DataFrame with price and indicator data
            index: Current index
            
        Returns:
            Momentum score (0.0 to 1.0)
        """
        if index < 20:
            return 0.5
        
        momentum_score = 0.5
        
        # RSI momentum
        if 'rsi_14' in data.columns and not pd.isna(data['rsi_14'].iloc[index]):
            rsi = data['rsi_14'].iloc[index]
            if 40 <= rsi <= 60:
                momentum_score += 0.1  # Neutral RSI is good
            elif rsi > 60:
                momentum_score += 0.2  # Bullish momentum
            elif rsi < 40:
                momentum_score -= 0.1  # Bearish momentum
        
        # Price momentum
        current_price = data['close_price'].iloc[index]
        price_5_ago = data['close_price'].iloc[index-5] if index >= 5 else current_price
        price_10_ago = data['close_price'].iloc[index-10] if index >= 10 else current_price
        
        momentum_5 = (current_price - price_5_ago) / price_5_ago
        momentum_10 = (current_price - price_10_ago) / price_10_ago
        
        # Positive momentum adds to score
        momentum_score += min(momentum_5 * 2, 0.2)  # Cap at 0.2
        momentum_score += min(momentum_10 * 1, 0.1)  # Cap at 0.1
        
        return max(0.0, min(1.0, momentum_score))
    
    def calculate_signals(self, data: pd.DataFrame) -> List[TradingSignal]:
        """Calculate moving average trading signals.
        
        Args:
            data: DataFrame with OHLCV and technical indicator data
            
        Returns:
            List of trading signals
        """
        signals = []
        
        if len(data) < self.long_period + 20:
            logger.warning("Insufficient data for moving averages strategy")
            return signals
        
        # Calculate dynamic moving averages if ADX is available
        if 'adx' in data.columns:
            short_ma = self.calculate_dynamic_ma(
                data['close_price'], 
                self.short_period, 
                data['adx'],
                self.use_exponential_in_trends
            )
            long_ma = self.calculate_dynamic_ma(
                data['close_price'], 
                self.long_period, 
                data['adx'],
                self.use_exponential_in_trends
            )
        else:
            # Fallback to simple moving averages
            short_ma = data['sma_50'] if 'sma_50' in data.columns else data['close_price'].rolling(self.short_period).mean()
            long_ma = data['sma_200'] if 'sma_200' in data.columns else data['close_price'].rolling(self.long_period).mean()
        
        # Detect overall trend
        trend_direction = TechnicalAnalysis.detect_trend(data['close_price'])
        
        # Iterate through data to find signals
        for i in range(self.long_period, len(data)):
            current_date = data['date'].iloc[i]
            current_price = data['close_price'].iloc[i]
            
            if pd.isna(current_price) or pd.isna(short_ma.iloc[i]) or pd.isna(long_ma.iloc[i]):
                continue
            
            # Detect MA crossover
            cross_type = self.detect_ma_cross(short_ma, long_ma, i)
            
            if cross_type is None:
                continue
            
            # Confirm crossover with multiple bars
            cross_confirmed = self.confirm_cross_with_bars(short_ma, long_ma, i, cross_type)
            
            if not cross_confirmed:
                continue
            
            # Calculate trend strength
            adx_series = data.get('adx', pd.Series([25] * len(data)))  # Default ADX if not available
            trend_strength = self.calculate_trend_strength(short_ma, long_ma, adx_series, i)
            
            # Volume confirmation
            volume_confirmed = True
            if self.volume_confirmation:
                current_volume = data['volume'].iloc[i]
                avg_volume = data['volume'].iloc[max(0, i-20):i].mean()
                volume_confirmed = SignalFilters.volume_confirmation(
                    current_price, current_volume, avg_volume, 1.3
                )
            
            # Momentum confirmation
            momentum_score = 0.5
            if self.momentum_confirmation:
                momentum_score = self.calculate_momentum_score(data, i)
            
            # Multiple timeframe analysis
            timeframe_analysis = self.analyze_multiple_timeframes(data, i)
            
            # Create signals based on cross type
            if cross_type == 'golden':
                # Golden Cross - BUY signal
                
                # Multiple timeframe confirmation
                mtf_bullish_count = sum([
                    timeframe_analysis['short_term_bullish'],
                    timeframe_analysis['medium_term_bullish'],
                    timeframe_analysis['long_term_bullish']
                ])
                
                # Calculate signal strength
                base_strength = 0.6  # Base strength for golden cross
                base_strength += trend_strength * 0.2
                base_strength += (momentum_score - 0.5) * 0.2  # Momentum bonus/penalty
                base_strength += (mtf_bullish_count / 3) * 0.1  # MTF bonus
                
                if volume_confirmed:
                    base_strength += 0.1
                
                signal_strength = min(1.0, base_strength)
                
                if signal_strength >= self.parameters['min_signal_strength']:
                    # Calculate stop loss and target
                    atr = current_price * 0.02  # 2% fallback ATR
                    if 'atr' in data.columns and not pd.isna(data['atr'].iloc[i]):
                        atr = data['atr'].iloc[i]
                    
                    stop_loss = long_ma.iloc[i] - atr  # Stop below long MA
                    target_price = current_price + (3 * atr)  # 3:1 risk-reward
                    
                    signal = TradingSignal(
                        symbol="",  # Will be set by base class
                        signal_type=SignalType.BUY,
                        signal_strength=signal_strength,
                        price=current_price,
                        target_price=target_price,
                        stop_loss=stop_loss,
                        confidence_score=trend_strength,
                        parameters={
                            'cross_type': cross_type,
                            'short_ma': short_ma.iloc[i],
                            'long_ma': long_ma.iloc[i],
                            'trend_strength': trend_strength,
                            'momentum_score': momentum_score,
                            'volume_confirmed': volume_confirmed,
                            'mtf_bullish_count': mtf_bullish_count,
                            'adx_value': adx_series.iloc[i] if not pd.isna(adx_series.iloc[i]) else None
                        },
                        timestamp=datetime.combine(current_date, datetime.min.time())
                    )
                    
                    signals.append(signal)
            
            elif cross_type == 'death':
                # Death Cross - SELL signal
                
                # Multiple timeframe confirmation (bearish)
                mtf_bearish_count = sum([
                    not timeframe_analysis['short_term_bullish'],
                    not timeframe_analysis['medium_term_bullish'],
                    not timeframe_analysis['long_term_bullish']
                ])
                
                # Calculate signal strength
                base_strength = 0.6  # Base strength for death cross
                base_strength += trend_strength * 0.2
                base_strength += (0.5 - momentum_score) * 0.2  # Bearish momentum bonus
                base_strength += (mtf_bearish_count / 3) * 0.1  # MTF bonus
                
                if volume_confirmed:
                    base_strength += 0.1
                
                signal_strength = min(1.0, base_strength)
                
                if signal_strength >= self.parameters['min_signal_strength']:
                    # Calculate stop loss and target
                    atr = current_price * 0.02  # 2% fallback ATR
                    if 'atr' in data.columns and not pd.isna(data['atr'].iloc[i]):
                        atr = data['atr'].iloc[i]
                    
                    stop_loss = long_ma.iloc[i] + atr  # Stop above long MA
                    target_price = current_price - (3 * atr)  # 3:1 risk-reward
                    
                    signal = TradingSignal(
                        symbol="",  # Will be set by base class
                        signal_type=SignalType.SELL,
                        signal_strength=signal_strength,
                        price=current_price,
                        target_price=target_price,
                        stop_loss=stop_loss,
                        confidence_score=trend_strength,
                        parameters={
                            'cross_type': cross_type,
                            'short_ma': short_ma.iloc[i],
                            'long_ma': long_ma.iloc[i],
                            'trend_strength': trend_strength,
                            'momentum_score': momentum_score,
                            'volume_confirmed': volume_confirmed,
                            'mtf_bearish_count': mtf_bearish_count,
                            'adx_value': adx_series.iloc[i] if not pd.isna(adx_series.iloc[i]) else None
                        },
                        timestamp=datetime.combine(current_date, datetime.min.time())
                    )
                    
                    signals.append(signal)
        
        logger.info(f"Generated {len(signals)} moving averages signals")
        return signals
