version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:13
    container_name: stock_analyzer_db
    environment:
      POSTGRES_DB: stock_analyzer
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${DB_PASSWORD:-postgres}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - stock_analyzer_network

  # Redis for caching and task queue
  redis:
    image: redis:7-alpine
    container_name: stock_analyzer_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - stock_analyzer_network

  # Main Application
  stock_analyzer:
    build: .
    container_name: stock_analyzer_app
    environment:
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=stock_analyzer
      - DB_USER=postgres
      - DB_PASSWORD=${DB_PASSWORD:-postgres}
      - REDIS_URL=redis://redis:6379/0
      - ENVIRONMENT=production
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    networks:
      - stock_analyzer_network
    restart: unless-stopped

  # Celery Worker for background tasks
  celery_worker:
    build: .
    container_name: stock_analyzer_worker
    command: celery -A src.utils.celery_app worker --loglevel=info
    environment:
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=stock_analyzer
      - DB_USER=postgres
      - DB_PASSWORD=${DB_PASSWORD:-postgres}
      - REDIS_URL=redis://redis:6379/0
      - ENVIRONMENT=production
    depends_on:
      - postgres
      - redis
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    networks:
      - stock_analyzer_network
    restart: unless-stopped

  # Celery Beat for scheduled tasks
  celery_beat:
    build: .
    container_name: stock_analyzer_scheduler
    command: celery -A src.utils.celery_app beat --loglevel=info
    environment:
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=stock_analyzer
      - DB_USER=postgres
      - DB_PASSWORD=${DB_PASSWORD:-postgres}
      - REDIS_URL=redis://redis:6379/0
      - ENVIRONMENT=production
    depends_on:
      - postgres
      - redis
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    networks:
      - stock_analyzer_network
    restart: unless-stopped

  # Prometheus for monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: stock_analyzer_prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - stock_analyzer_network

volumes:
  postgres_data:
  redis_data:
  prometheus_data:

networks:
  stock_analyzer_network:
    driver: bridge
