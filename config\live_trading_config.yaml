# Live Trading Configuration
# This file contains configuration for live trading system

# Broker Configuration
broker:
  broker_name: "mock"  # Options: "zerodha", "mock"
  api_key: ""  # Your broker API key
  api_secret: ""  # Your broker API secret
  access_token: ""  # Your broker access token
  sandbox_mode: true  # Use sandbox/demo mode

# Trading Configuration
trading:
  trading_enabled: false  # Set to true to enable live trading
  default_mode: "PAPER"  # Options: "PAPER", "LIVE", "SIMULATION"
  signal_processing_interval: 30  # seconds
  signal_generation_interval: 300  # seconds (5 minutes)
  market_hours:
    start: "09:15"  # Market opening time
    end: "15:30"    # Market closing time
  max_order_value: 1000000  # ₹10L maximum order value
  max_position_size: 500000  # ₹5L maximum position size
  order_timeout_minutes: 60  # Order timeout in minutes

# Risk Management Configuration
risk:
  max_position_size: 500000  # ₹5L per position
  max_portfolio_concentration: 0.15  # 15% per stock
  max_sector_concentration: 0.30  # 30% per sector
  daily_loss_limit: 50000  # ₹50K daily loss limit
  max_drawdown_limit: 0.10  # 10% maximum drawdown
  max_portfolio_var: 100000  # ₹1L VaR limit
  max_position_volatility: 0.30  # 30% volatility limit
  max_correlation: 0.80  # 80% correlation limit

# Strategy Configuration for Live Trading
strategies:
  adaptive_rsi:
    enabled: true
    live_trading_enabled: true
    position_sizing: "dynamic"  # "fixed", "dynamic", "risk_based"
    max_positions: 3
    
  enhanced_pivot:
    enabled: true
    live_trading_enabled: true
    position_sizing: "dynamic"
    max_positions: 2
    
  ml_enhanced_macd:
    enabled: true
    live_trading_enabled: true
    position_sizing: "risk_based"
    max_positions: 2
    
  dynamic_moving_averages:
    enabled: true
    live_trading_enabled: true
    position_sizing: "dynamic"
    max_positions: 3
    
  volume_confirmed_breakout:
    enabled: true
    live_trading_enabled: true
    position_sizing: "risk_based"
    max_positions: 2

# RL Configuration for Live Trading
rl:
  training_enabled: true
  real_time_adaptation: true
  adaptation_frequency: 240  # minutes (4 hours)
  min_confidence_threshold: 0.6
  parameter_bounds:
    adaptive_rsi:
      rsi_period: [10, 21]
      oversold_threshold: [20, 35]
      overbought_threshold: [65, 80]
    enhanced_pivot:
      volume_multiplier: [1.2, 2.5]
      breakout_confirmation_bars: [1, 5]
    ml_enhanced_macd:
      fast_period: [8, 16]
      slow_period: [20, 30]
      signal_period: [7, 12]
    dynamic_moving_averages:
      short_ma_period: [40, 80]
      long_ma_period: [160, 220]
      adx_threshold: [15, 35]
    volume_confirmed_breakout:
      lookback_period: [15, 25]
      volume_threshold: [1.5, 3.0]

# Logging Configuration for Live Trading
logging:
  level: "INFO"
  live_trading_log_file: "logs/live_trading.log"
  order_log_file: "logs/orders.log"
  risk_log_file: "logs/risk_management.log"
  max_file_size: "100MB"
  backup_count: 10

# Notification Configuration
notifications:
  enabled: true
  channels:
    email:
      enabled: false
      smtp_server: ""
      smtp_port: 587
      username: ""
      password: ""
      recipients: []
    
    webhook:
      enabled: false
      url: ""
      headers: {}
    
    telegram:
      enabled: false
      bot_token: ""
      chat_id: ""

# Performance Monitoring
monitoring:
  enabled: true
  metrics_collection_interval: 60  # seconds
  performance_alert_thresholds:
    daily_loss_percent: 5.0  # Alert if daily loss > 5%
    drawdown_percent: 8.0    # Alert if drawdown > 8%
    win_rate_threshold: 40.0  # Alert if win rate < 40%
  
# Database Configuration for Live Trading
database:
  live_trading_tables:
    - "live_orders"
    - "live_positions"
    - "live_trades"
    - "risk_violations"
    - "trading_sessions"
  
  backup:
    enabled: true
    frequency: "daily"
    retention_days: 30

# Security Configuration
security:
  api_rate_limiting:
    enabled: true
    requests_per_minute: 100
  
  order_validation:
    double_confirmation: true  # Require confirmation for large orders
    large_order_threshold: 100000  # ₹1L
  
  access_control:
    live_trading_roles: ["admin", "trader"]
    emergency_stop_roles: ["admin"]

# Development and Testing
development:
  paper_trading_enabled: true
  simulation_mode_enabled: true
  mock_broker_enabled: true
  test_data_symbols: ["RELIANCE", "TCS", "INFY", "HDFCBANK", "ICICIBANK"]
