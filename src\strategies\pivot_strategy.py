"""
Enhanced Pivot Points Strategy Implementation.
Features volume confirmation and intraday support/resistance levels.
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

from .base_strategy import BaseStrategy, TradingSignal, SignalType, SignalStrength
from .strategy_utils import TechnicalAnalysis, SignalFilters
from ..utils.logger import get_logger
from ..utils.config import config

logger = get_logger(__name__)

class EnhancedPivotStrategy(BaseStrategy):
    """
    Enhanced Pivot Points Strategy with volume confirmation.
    
    Features:
    - Classic pivot points calculation (P, R1, R2, S1, S2)
    - Fibonacci pivot points for additional levels
    - Volume confirmation for breakout signals
    - Support/resistance strength analysis
    - Multiple pivot timeframes
    - Breakout failure detection
    """
    
    def __init__(self, parameters: Optional[Dict[str, Any]] = None):
        """Initialize Enhanced Pivot Points Strategy.
        
        Args:
            parameters: Strategy parameters override
        """
        default_parameters = {
            'volume_multiplier': 1.5,
            'min_breakout_volume': 2.0,
            'pivot_strength_threshold': 0.6,
            'use_fibonacci_pivots': True,
            'breakout_confirmation_bars': 2,
            'support_resistance_levels': 3,
            'min_signal_strength': 0.5,
            'trend_alignment': True,
            'use_multiple_timeframes': False
        }
        
        if parameters:
            default_parameters.update(parameters)
        
        super().__init__("enhanced_pivot", default_parameters)
        
        # Strategy-specific attributes
        self.volume_multiplier = self.parameters['volume_multiplier']
        self.min_breakout_volume = self.parameters['min_breakout_volume']
        self.pivot_strength_threshold = self.parameters['pivot_strength_threshold']
        self.use_fibonacci_pivots = self.parameters['use_fibonacci_pivots']
        self.breakout_confirmation_bars = self.parameters['breakout_confirmation_bars']
        self.support_resistance_levels = self.parameters['support_resistance_levels']
        self.trend_alignment = self.parameters['trend_alignment']
        self.use_multiple_timeframes = self.parameters['use_multiple_timeframes']
    
    def get_required_indicators(self) -> List[str]:
        """Get required technical indicators."""
        return [
            'pivot_point', 'resistance_1', 'resistance_2', 'support_1', 'support_2',
            'volume_ratio', 'sma_20', 'sma_50', 'atr'
        ]
    
    def calculate_classic_pivots(
        self, 
        high: float, 
        low: float, 
        close: float
    ) -> Dict[str, float]:
        """Calculate classic pivot points.
        
        Args:
            high: Previous day's high
            low: Previous day's low
            close: Previous day's close
            
        Returns:
            Dictionary with pivot levels
        """
        pivot = (high + low + close) / 3
        
        r1 = (2 * pivot) - low
        r2 = pivot + (high - low)
        r3 = high + 2 * (pivot - low)
        
        s1 = (2 * pivot) - high
        s2 = pivot - (high - low)
        s3 = low - 2 * (high - pivot)
        
        return {
            'pivot': pivot,
            'r1': r1, 'r2': r2, 'r3': r3,
            's1': s1, 's2': s2, 's3': s3
        }
    
    def calculate_fibonacci_pivots(
        self, 
        high: float, 
        low: float, 
        close: float
    ) -> Dict[str, float]:
        """Calculate Fibonacci pivot points.
        
        Args:
            high: Previous day's high
            low: Previous day's low
            close: Previous day's close
            
        Returns:
            Dictionary with Fibonacci pivot levels
        """
        pivot = (high + low + close) / 3
        range_hl = high - low
        
        # Fibonacci ratios
        fib_ratios = [0.236, 0.382, 0.618, 0.786]
        
        fib_pivots = {'pivot': pivot}
        
        for i, ratio in enumerate(fib_ratios, 1):
            fib_pivots[f'fr{i}'] = pivot + (range_hl * ratio)
            fib_pivots[f'fs{i}'] = pivot - (range_hl * ratio)
        
        return fib_pivots
    
    def calculate_pivot_strength(
        self, 
        data: pd.DataFrame, 
        pivot_level: float, 
        lookback: int = 10
    ) -> float:
        """Calculate the strength of a pivot level based on historical touches.
        
        Args:
            data: DataFrame with price data
            pivot_level: Pivot level to analyze
            lookback: Number of periods to look back
            
        Returns:
            Strength score (0.0 to 1.0)
        """
        if len(data) < lookback:
            return 0.0
        
        recent_data = data.tail(lookback)
        tolerance = pivot_level * 0.005  # 0.5% tolerance
        
        # Count touches (price came within tolerance)
        touches = 0
        bounces = 0
        
        for i in range(len(recent_data)):
            high = recent_data['high_price'].iloc[i]
            low = recent_data['low_price'].iloc[i]
            close = recent_data['close_price'].iloc[i]
            
            # Check if price touched the level
            if low <= pivot_level + tolerance and high >= pivot_level - tolerance:
                touches += 1
                
                # Check if it bounced (closed away from level)
                if abs(close - pivot_level) > tolerance:
                    bounces += 1
        
        if touches == 0:
            return 0.0
        
        # Strength based on bounce rate and number of touches
        bounce_rate = bounces / touches
        touch_strength = min(touches / 5, 1.0)  # Normalize to max 5 touches
        
        return (bounce_rate * 0.7) + (touch_strength * 0.3)
    
    def detect_breakout(
        self, 
        data: pd.DataFrame, 
        index: int, 
        pivot_level: float,
        direction: str
    ) -> Dict[str, Any]:
        """Detect breakout from pivot level.
        
        Args:
            data: DataFrame with price data
            index: Current index
            pivot_level: Pivot level
            direction: 'above' or 'below'
            
        Returns:
            Breakout information
        """
        if index < self.breakout_confirmation_bars:
            return {'is_breakout': False}
        
        current_price = data['close_price'].iloc[index]
        current_volume = data['volume'].iloc[index]
        avg_volume = data['volume'].iloc[max(0, index-20):index].mean()
        
        # Check price breakout
        if direction == 'above':
            price_breakout = current_price > pivot_level
        else:
            price_breakout = current_price < pivot_level
        
        if not price_breakout:
            return {'is_breakout': False}
        
        # Volume confirmation
        volume_confirmed = SignalFilters.volume_confirmation(
            current_price, current_volume, avg_volume, self.volume_multiplier
        )
        
        # Check for confirmation bars
        confirmation_count = 0
        for i in range(1, min(self.breakout_confirmation_bars + 1, index + 1)):
            past_price = data['close_price'].iloc[index - i]
            if direction == 'above' and past_price > pivot_level:
                confirmation_count += 1
            elif direction == 'below' and past_price < pivot_level:
                confirmation_count += 1
        
        confirmed_breakout = confirmation_count >= self.breakout_confirmation_bars
        
        return {
            'is_breakout': True,
            'volume_confirmed': volume_confirmed,
            'confirmed_breakout': confirmed_breakout,
            'volume_ratio': current_volume / avg_volume if avg_volume > 0 else 1.0,
            'breakout_strength': min(1.0, (confirmation_count / self.breakout_confirmation_bars) * 
                                   (current_volume / avg_volume if avg_volume > 0 else 1.0))
        }
    
    def calculate_signal_strength(
        self, 
        breakout_info: Dict[str, Any],
        pivot_strength: float,
        trend_aligned: bool = False
    ) -> float:
        """Calculate signal strength for pivot breakout.
        
        Args:
            breakout_info: Breakout detection results
            pivot_strength: Strength of the pivot level
            trend_aligned: Whether signal aligns with trend
            
        Returns:
            Signal strength (0.0 to 1.0)
        """
        base_strength = 0.4
        
        # Breakout strength
        if breakout_info.get('is_breakout', False):
            base_strength += breakout_info.get('breakout_strength', 0) * 0.3
        
        # Volume confirmation
        if breakout_info.get('volume_confirmed', False):
            base_strength += 0.2
        
        # Pivot level strength
        base_strength += pivot_strength * 0.2
        
        # Trend alignment
        if trend_aligned:
            base_strength += 0.1
        
        return min(1.0, base_strength)
    
    def identify_key_levels(self, data: pd.DataFrame, index: int) -> Dict[str, List[float]]:
        """Identify key support and resistance levels.
        
        Args:
            data: DataFrame with price data
            index: Current index
            
        Returns:
            Dictionary with support and resistance levels
        """
        if index < 20:
            return {'support': [], 'resistance': []}
        
        # Get recent data
        recent_data = data.iloc[max(0, index-50):index+1]
        
        # Use built-in pivot points if available
        pivot_levels = []
        if 'pivot_point' in data.columns and not pd.isna(data['pivot_point'].iloc[index]):
            pivot_levels.append(data['pivot_point'].iloc[index])
        
        if 'resistance_1' in data.columns and not pd.isna(data['resistance_1'].iloc[index]):
            pivot_levels.append(data['resistance_1'].iloc[index])
        
        if 'resistance_2' in data.columns and not pd.isna(data['resistance_2'].iloc[index]):
            pivot_levels.append(data['resistance_2'].iloc[index])
        
        if 'support_1' in data.columns and not pd.isna(data['support_1'].iloc[index]):
            pivot_levels.append(data['support_1'].iloc[index])
        
        if 'support_2' in data.columns and not pd.isna(data['support_2'].iloc[index]):
            pivot_levels.append(data['support_2'].iloc[index])
        
        # Detect additional support/resistance using price action
        sr_levels = TechnicalAnalysis.detect_support_resistance(
            recent_data['high_price'],
            recent_data['low_price'],
            window=10
        )
        
        current_price = data['close_price'].iloc[index]
        
        # Classify levels as support or resistance
        support_levels = []
        resistance_levels = []
        
        all_levels = pivot_levels + sr_levels['support'] + sr_levels['resistance']
        
        for level in all_levels:
            if level < current_price:
                support_levels.append(level)
            else:
                resistance_levels.append(level)
        
        # Sort and limit levels
        support_levels = sorted(list(set(support_levels)), reverse=True)[:self.support_resistance_levels]
        resistance_levels = sorted(list(set(resistance_levels)))[:self.support_resistance_levels]
        
        return {
            'support': support_levels,
            'resistance': resistance_levels
        }
    
    def calculate_signals(self, data: pd.DataFrame) -> List[TradingSignal]:
        """Calculate pivot point trading signals.
        
        Args:
            data: DataFrame with OHLCV and technical indicator data
            
        Returns:
            List of trading signals
        """
        signals = []
        
        if len(data) < 50:
            logger.warning("Insufficient data for pivot strategy")
            return signals
        
        # Detect trend
        trend_direction = TechnicalAnalysis.detect_trend(data['close_price'])
        
        # Iterate through data to find signals
        for i in range(20, len(data)):
            current_date = data['date'].iloc[i]
            current_price = data['close_price'].iloc[i]
            
            if pd.isna(current_price):
                continue
            
            # Identify key levels
            key_levels = self.identify_key_levels(data, i)
            
            # Check for breakouts from resistance levels (BUY signals)
            for resistance_level in key_levels['resistance']:
                if abs(resistance_level - current_price) / current_price < 0.001:  # Very close to level
                    continue
                
                breakout_info = self.detect_breakout(data, i, resistance_level, 'above')
                
                if breakout_info['is_breakout'] and breakout_info.get('volume_confirmed', False):
                    # Calculate pivot strength
                    pivot_strength = self.calculate_pivot_strength(data.iloc[:i+1], resistance_level)
                    
                    if pivot_strength >= self.pivot_strength_threshold:
                        # Trend alignment check
                        trend_aligned = SignalFilters.trend_alignment(
                            'BUY', trend_direction, self.trend_alignment
                        )
                        
                        # Calculate signal strength
                        signal_strength = self.calculate_signal_strength(
                            breakout_info, pivot_strength, trend_aligned
                        )
                        
                        if signal_strength >= self.parameters['min_signal_strength']:
                            # Calculate stop loss and target
                            atr = data.get('atr', pd.Series([current_price * 0.02] * len(data))).iloc[i]
                            if pd.isna(atr):
                                atr = current_price * 0.02
                            
                            stop_loss = resistance_level - (0.5 * atr)  # Just below breakout level
                            target_price = current_price + (2 * atr)  # 4:1 risk-reward
                            
                            signal = TradingSignal(
                                symbol="",  # Will be set by base class
                                signal_type=SignalType.BUY,
                                signal_strength=signal_strength,
                                price=current_price,
                                target_price=target_price,
                                stop_loss=stop_loss,
                                confidence_score=signal_strength,
                                parameters={
                                    'pivot_level': resistance_level,
                                    'pivot_strength': pivot_strength,
                                    'breakout_info': breakout_info,
                                    'trend_aligned': trend_aligned,
                                    'signal_type': 'resistance_breakout'
                                },
                                timestamp=datetime.combine(current_date, datetime.min.time())
                            )
                            
                            signals.append(signal)
            
            # Check for breakouts from support levels (SELL signals)
            for support_level in key_levels['support']:
                if abs(support_level - current_price) / current_price < 0.001:  # Very close to level
                    continue
                
                breakout_info = self.detect_breakout(data, i, support_level, 'below')
                
                if breakout_info['is_breakout'] and breakout_info.get('volume_confirmed', False):
                    # Calculate pivot strength
                    pivot_strength = self.calculate_pivot_strength(data.iloc[:i+1], support_level)
                    
                    if pivot_strength >= self.pivot_strength_threshold:
                        # Trend alignment check
                        trend_aligned = SignalFilters.trend_alignment(
                            'SELL', trend_direction, self.trend_alignment
                        )
                        
                        # Calculate signal strength
                        signal_strength = self.calculate_signal_strength(
                            breakout_info, pivot_strength, trend_aligned
                        )
                        
                        if signal_strength >= self.parameters['min_signal_strength']:
                            # Calculate stop loss and target
                            atr = data.get('atr', pd.Series([current_price * 0.02] * len(data))).iloc[i]
                            if pd.isna(atr):
                                atr = current_price * 0.02
                            
                            stop_loss = support_level + (0.5 * atr)  # Just above breakout level
                            target_price = current_price - (2 * atr)  # 4:1 risk-reward
                            
                            signal = TradingSignal(
                                symbol="",  # Will be set by base class
                                signal_type=SignalType.SELL,
                                signal_strength=signal_strength,
                                price=current_price,
                                target_price=target_price,
                                stop_loss=stop_loss,
                                confidence_score=signal_strength,
                                parameters={
                                    'pivot_level': support_level,
                                    'pivot_strength': pivot_strength,
                                    'breakout_info': breakout_info,
                                    'trend_aligned': trend_aligned,
                                    'signal_type': 'support_breakdown'
                                },
                                timestamp=datetime.combine(current_date, datetime.min.time())
                            )
                            
                            signals.append(signal)
        
        logger.info(f"Generated {len(signals)} pivot point signals")
        return signals
