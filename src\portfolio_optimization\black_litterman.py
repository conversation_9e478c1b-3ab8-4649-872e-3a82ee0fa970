"""
Black-Litterman Model Implementation.
Incorporates investor views and market equilibrium to improve portfolio optimization.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from scipy.linalg import inv

from .mpt_optimizer import MPTOptimizer, OptimizationResult, PortfolioConstraints
from ..data.database import db_manager
from ..data.crud import StockCRUD
from ..utils.logger import get_logger

logger = get_logger(__name__)

@dataclass
class InvestorView:
    """Investor view for Black-Litterman model."""
    assets: List[str]  # Assets involved in the view
    view_return: float  # Expected return for the view
    confidence: float  # Confidence level (0-1)
    view_type: str  # 'absolute' or 'relative'
    description: str  # Human-readable description

@dataclass
class BlackLittermanResult:
    """Black-Litterman optimization result."""
    bl_returns: np.ndarray  # Black-Litterman expected returns
    bl_covariance: np.ndarray  # Black-Litterman covariance matrix
    prior_returns: np.ndarray  # Market equilibrium returns
    posterior_returns: np.ndarray  # Posterior expected returns
    optimization_result: OptimizationResult  # Final portfolio optimization
    views_incorporated: List[InvestorView]  # Views used in optimization
    tau: float  # Scaling factor used
    omega: np.ndarray  # View uncertainty matrix

class BlackLittermanOptimizer:
    """Black-Litterman portfolio optimizer."""
    
    def __init__(self):
        """Initialize Black-Litterman optimizer."""
        self.mpt_optimizer = MPTOptimizer()
        self.risk_aversion = 3.0  # Default risk aversion parameter
        self.tau = 0.025  # Default scaling factor (1/40 for monthly data)
        
        logger.info("Black-Litterman Optimizer initialized")
    
    def calculate_market_equilibrium_returns(
        self, 
        market_caps: np.ndarray, 
        covariance_matrix: np.ndarray
    ) -> np.ndarray:
        """Calculate implied equilibrium returns from market capitalization."""
        try:
            # Market capitalization weights
            market_weights = market_caps / np.sum(market_caps)
            
            # Implied equilibrium returns: μ = λ * Σ * w_market
            # where λ is risk aversion parameter
            equilibrium_returns = self.risk_aversion * np.dot(covariance_matrix, market_weights)
            
            return equilibrium_returns
            
        except Exception as e:
            logger.error(f"Error calculating market equilibrium returns: {e}")
            raise
    
    def create_view_matrix(
        self, 
        views: List[InvestorView], 
        symbols: List[str]
    ) -> Tuple[np.ndarray, np.ndarray]:
        """Create picking matrix P and view vector Q."""
        try:
            n_assets = len(symbols)
            n_views = len(views)
            
            P = np.zeros((n_views, n_assets))  # Picking matrix
            Q = np.zeros(n_views)  # View returns vector
            
            symbol_to_index = {symbol: i for i, symbol in enumerate(symbols)}
            
            for i, view in enumerate(views):
                Q[i] = view.view_return
                
                if view.view_type == 'absolute':
                    # Absolute view: asset will return X%
                    for asset in view.assets:
                        if asset in symbol_to_index:
                            P[i, symbol_to_index[asset]] = 1.0
                            
                elif view.view_type == 'relative':
                    # Relative view: asset A will outperform asset B by X%
                    if len(view.assets) == 2:
                        asset_a, asset_b = view.assets
                        if asset_a in symbol_to_index and asset_b in symbol_to_index:
                            P[i, symbol_to_index[asset_a]] = 1.0
                            P[i, symbol_to_index[asset_b]] = -1.0
            
            return P, Q
            
        except Exception as e:
            logger.error(f"Error creating view matrix: {e}")
            raise
    
    def calculate_view_uncertainty(
        self, 
        views: List[InvestorView], 
        P: np.ndarray, 
        covariance_matrix: np.ndarray
    ) -> np.ndarray:
        """Calculate view uncertainty matrix Omega."""
        try:
            n_views = len(views)
            omega = np.zeros((n_views, n_views))
            
            for i, view in enumerate(views):
                # View uncertainty based on confidence and view variance
                view_variance = np.dot(P[i, :], np.dot(covariance_matrix, P[i, :].T))
                
                # Lower confidence = higher uncertainty
                uncertainty_multiplier = (1.0 - view.confidence) * 2.0 + 0.1
                omega[i, i] = view_variance * uncertainty_multiplier
            
            return omega
            
        except Exception as e:
            logger.error(f"Error calculating view uncertainty: {e}")
            raise
    
    def calculate_bl_returns_covariance(
        self, 
        prior_returns: np.ndarray,
        prior_covariance: np.ndarray,
        P: np.ndarray,
        Q: np.ndarray,
        omega: np.ndarray,
        tau: float
    ) -> Tuple[np.ndarray, np.ndarray]:
        """Calculate Black-Litterman posterior returns and covariance."""
        try:
            # Scaled prior covariance
            tau_sigma = tau * prior_covariance
            
            # Calculate posterior covariance matrix
            # Σ_BL = [(τΣ)^-1 + P'Ω^-1P]^-1
            tau_sigma_inv = inv(tau_sigma)
            omega_inv = inv(omega)
            
            posterior_covariance_inv = tau_sigma_inv + np.dot(P.T, np.dot(omega_inv, P))
            posterior_covariance = inv(posterior_covariance_inv)
            
            # Calculate posterior expected returns
            # μ_BL = Σ_BL * [(τΣ)^-1 * μ + P'Ω^-1 * Q]
            term1 = np.dot(tau_sigma_inv, prior_returns)
            term2 = np.dot(P.T, np.dot(omega_inv, Q))
            
            posterior_returns = np.dot(posterior_covariance, term1 + term2)
            
            return posterior_returns, posterior_covariance
            
        except Exception as e:
            logger.error(f"Error calculating BL returns and covariance: {e}")
            raise
    
    def get_market_caps(self, symbols: List[str]) -> np.ndarray:
        """Get market capitalizations for symbols."""
        try:
            market_caps = []
            
            with db_manager.get_session() as db:
                for symbol in symbols:
                    stock = StockCRUD.get_stock_by_symbol(db, symbol)
                    if stock and stock.market_cap:
                        market_caps.append(float(stock.market_cap))
                    else:
                        # Default market cap if not available
                        market_caps.append(100000.0)  # 1000 Cr default
            
            return np.array(market_caps)
            
        except Exception as e:
            logger.error(f"Error getting market caps: {e}")
            # Return equal weights if market caps not available
            return np.ones(len(symbols))
    
    def optimize_with_views(
        self,
        symbols: List[str],
        views: List[InvestorView],
        constraints: Optional[PortfolioConstraints] = None,
        tau: Optional[float] = None,
        risk_aversion: Optional[float] = None,
        lookback_days: int = 252
    ) -> BlackLittermanResult:
        """Optimize portfolio using Black-Litterman model with investor views."""
        try:
            if constraints is None:
                constraints = PortfolioConstraints()
            
            if tau is not None:
                self.tau = tau
            
            if risk_aversion is not None:
                self.risk_aversion = risk_aversion
            
            # Get historical returns and covariance
            expected_returns, covariance_matrix = self.mpt_optimizer.calculate_returns_covariance(
                symbols, lookback_days
            )
            
            # Get market capitalizations
            market_caps = self.get_market_caps(symbols)
            
            # Calculate market equilibrium returns
            prior_returns = self.calculate_market_equilibrium_returns(market_caps, covariance_matrix)
            
            # Create view matrices
            P, Q = self.create_view_matrix(views, symbols)
            
            # Calculate view uncertainty
            omega = self.calculate_view_uncertainty(views, P, covariance_matrix)
            
            # Calculate Black-Litterman returns and covariance
            bl_returns, bl_covariance = self.calculate_bl_returns_covariance(
                prior_returns, covariance_matrix, P, Q, omega, self.tau
            )
            
            # Optimize portfolio using BL inputs
            n_assets = len(symbols)
            
            # Use max Sharpe optimization with BL inputs
            def objective(weights):
                portfolio_return = np.dot(weights, bl_returns)
                portfolio_variance = np.dot(weights.T, np.dot(bl_covariance, weights))
                portfolio_volatility = np.sqrt(portfolio_variance)
                
                if portfolio_volatility == 0:
                    return -np.inf
                
                sharpe_ratio = (portfolio_return - self.mpt_optimizer.risk_free_rate) / portfolio_volatility
                return -sharpe_ratio
            
            # Constraints and bounds
            from scipy.optimize import minimize
            
            constraints_list = [
                {'type': 'eq', 'fun': lambda x: np.sum(x) - 1.0}
            ]
            
            bounds = [(constraints.min_weight, min(constraints.max_weight, constraints.max_concentration)) 
                     for _ in range(n_assets)]
            
            initial_guess = np.array([1.0 / n_assets] * n_assets)
            
            # Optimize
            result = minimize(
                objective,
                initial_guess,
                method='SLSQP',
                bounds=bounds,
                constraints=constraints_list,
                options={'maxiter': 1000, 'ftol': 1e-9}
            )
            
            # Calculate final portfolio metrics
            optimal_weights = result.x
            portfolio_return = np.dot(optimal_weights, bl_returns)
            portfolio_variance = np.dot(optimal_weights.T, np.dot(bl_covariance, optimal_weights))
            portfolio_volatility = np.sqrt(portfolio_variance)
            sharpe_ratio = (portfolio_return - self.mpt_optimizer.risk_free_rate) / portfolio_volatility
            
            # Additional metrics
            additional_metrics = {
                'max_weight': np.max(optimal_weights),
                'min_weight': np.min(optimal_weights),
                'effective_positions': np.sum(optimal_weights > 0.01),
                'concentration_hhi': np.sum(optimal_weights ** 2),
                'views_count': len(views),
                'tau_used': self.tau,
                'risk_aversion': self.risk_aversion
            }
            
            # Create optimization result
            optimization_result = OptimizationResult(
                weights=optimal_weights,
                expected_return=portfolio_return,
                volatility=portfolio_volatility,
                sharpe_ratio=sharpe_ratio,
                symbols=symbols,
                optimization_method="black_litterman",
                constraints_satisfied=result.success,
                optimization_success=result.success,
                additional_metrics=additional_metrics
            )
            
            # Create Black-Litterman result
            bl_result = BlackLittermanResult(
                bl_returns=bl_returns,
                bl_covariance=bl_covariance,
                prior_returns=prior_returns,
                posterior_returns=bl_returns,
                optimization_result=optimization_result,
                views_incorporated=views,
                tau=self.tau,
                omega=omega
            )
            
            logger.info(f"Black-Litterman optimization completed with {len(views)} views")
            logger.info(f"Expected return: {portfolio_return:.4f}")
            logger.info(f"Volatility: {portfolio_volatility:.4f}")
            logger.info(f"Sharpe ratio: {sharpe_ratio:.4f}")
            
            return bl_result
            
        except Exception as e:
            logger.error(f"Error in Black-Litterman optimization: {e}")
            raise
    
    def create_strategy_views(self, strategy_signals: Dict[str, float]) -> List[InvestorView]:
        """Create investor views from strategy signals."""
        try:
            views = []
            
            for symbol, signal_strength in strategy_signals.items():
                if abs(signal_strength) > 0.5:  # Only strong signals
                    # Convert signal strength to expected return
                    expected_return = signal_strength * 0.20  # Max 20% expected return
                    
                    # Confidence based on signal strength
                    confidence = min(abs(signal_strength), 0.9)
                    
                    view = InvestorView(
                        assets=[symbol],
                        view_return=expected_return,
                        confidence=confidence,
                        view_type='absolute',
                        description=f"Strategy signal for {symbol}: {signal_strength:.2f}"
                    )
                    
                    views.append(view)
            
            return views
            
        except Exception as e:
            logger.error(f"Error creating strategy views: {e}")
            return []
    
    def create_sector_views(self, sector_outlook: Dict[str, float]) -> List[InvestorView]:
        """Create sector-based relative views."""
        try:
            views = []
            
            # Create relative views between sectors
            sectors = list(sector_outlook.keys())
            
            for i, sector_a in enumerate(sectors):
                for sector_b in sectors[i+1:]:
                    return_diff = sector_outlook[sector_a] - sector_outlook[sector_b]
                    
                    if abs(return_diff) > 0.02:  # 2% minimum difference
                        # Get representative stocks for each sector
                        with db_manager.get_session() as db:
                            stocks_a = StockCRUD.get_stocks_by_sector(db, sector_a, limit=1)
                            stocks_b = StockCRUD.get_stocks_by_sector(db, sector_b, limit=1)
                            
                            if stocks_a and stocks_b:
                                view = InvestorView(
                                    assets=[stocks_a[0].symbol, stocks_b[0].symbol],
                                    view_return=return_diff,
                                    confidence=0.6,  # Moderate confidence for sector views
                                    view_type='relative',
                                    description=f"{sector_a} vs {sector_b} sector view"
                                )
                                
                                views.append(view)
            
            return views

        except Exception as e:
            logger.error(f"Error creating sector views: {e}")
            return []

# Global Black-Litterman optimizer instance
bl_optimizer = BlackLittermanOptimizer()
