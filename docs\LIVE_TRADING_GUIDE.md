# Live Trading System Guide

## Overview

The Live Trading System provides comprehensive automated trading capabilities with robust risk management, order execution, and portfolio monitoring. It integrates seamlessly with the existing strategy framework and RL optimizer to provide intelligent, adaptive trading.

## Architecture

### Core Components

1. **Broker Integration** (`broker_integration.py`)
   - Unified interface for multiple brokers (Zerodha, Mock)
   - Order placement, modification, and cancellation
   - Portfolio and position management
   - Real-time market data

2. **Order Manager** (`order_manager.py`)
   - Order lifecycle management
   - Queue-based order processing
   - Partial fill handling
   - Order timeout and retry logic

3. **Risk Manager** (`risk_manager.py`)
   - Position sizing and concentration limits
   - Drawdown and loss limits
   - Volatility and correlation monitoring
   - Emergency stop functionality

4. **Trading Engine** (`trading_engine.py`)
   - Signal processing and execution
   - Market hours monitoring
   - Performance tracking
   - Session management

5. **Integration Layer** (`integration.py`)
   - Main system integration
   - Automatic signal generation
   - Performance monitoring
   - API endpoints

## Getting Started

### 1. Configuration

Copy and modify the configuration file:
```bash
cp config/live_trading_config.yaml config/config.yaml
```

Key configuration sections:
- `broker`: Broker API credentials and settings
- `trading`: Trading parameters and market hours
- `risk`: Risk management limits
- `strategies`: Strategy-specific settings

### 2. Broker Setup

#### Mock Broker (Testing)
```yaml
broker:
  broker_name: "mock"
  sandbox_mode: true
```

#### Zerodha Kite (Live Trading)
```yaml
broker:
  broker_name: "zerodha"
  api_key: "your_api_key"
  api_secret: "your_api_secret"
  access_token: "your_access_token"
  sandbox_mode: false
```

### 3. Initialize System

```python
from src.live_trading.integration import initialize_live_trading

# Initialize live trading system
success = await initialize_live_trading()
if success:
    print("Live trading system initialized successfully")
```

### 4. Start Trading

```python
from src.live_trading.integration import start_auto_trading
from src.live_trading.trading_engine import TradingMode

# Start paper trading
result = await start_auto_trading(TradingMode.PAPER)

# Start live trading (requires proper configuration)
result = await start_auto_trading(TradingMode.LIVE)
```

## API Endpoints

### System Control

- `GET /api/v1/live-trading/status` - Get system status
- `POST /api/v1/live-trading/initialize` - Initialize system
- `POST /api/v1/live-trading/start` - Start trading
- `POST /api/v1/live-trading/stop` - Stop trading

### Portfolio Management

- `GET /api/v1/live-trading/portfolio` - Get portfolio status
- `GET /api/v1/live-trading/positions` - Get current positions
- `GET /api/v1/live-trading/orders` - Get order history

### Order Management

- `POST /api/v1/live-trading/orders` - Place manual order
- `DELETE /api/v1/live-trading/orders/{order_id}` - Cancel order

### Risk Management

- `GET /api/v1/live-trading/risk` - Get risk status
- `POST /api/v1/live-trading/emergency-stop` - Emergency stop
- `POST /api/v1/live-trading/emergency-stop/deactivate` - Deactivate emergency stop

### Signal Generation

- `POST /api/v1/live-trading/signals/generate` - Generate signals
- `GET /api/v1/live-trading/performance` - Get performance metrics

## Risk Management

### Position Limits
- Maximum position size: ₹5L per stock
- Portfolio concentration: 15% per stock
- Sector concentration: 30% per sector

### Loss Limits
- Daily loss limit: ₹50K
- Maximum drawdown: 10%
- Portfolio VaR: ₹1L

### Volatility Controls
- Maximum position volatility: 30%
- Correlation limit: 80%

### Emergency Controls
- Emergency stop: Immediately halt all trading
- Trading halt: Pause trading due to risk violations
- Order timeout: Cancel orders after 60 minutes

## Trading Modes

### Paper Trading
- Simulated trading with real market data
- No actual money involved
- Full system testing

### Live Trading
- Real money trading
- Requires broker integration
- Full risk management active

### Simulation
- Historical data simulation
- Strategy testing and validation
- Performance analysis

## Signal Processing

### Automatic Signal Generation
- Runs every 5 minutes during market hours
- Uses RL-optimized parameters
- Processes all enabled strategies

### Signal Validation
- Risk management validation
- Position size calculation
- Market hours verification

### Order Execution
- Intelligent order routing
- Partial fill handling
- Price improvement logic

## Monitoring and Alerts

### Real-time Monitoring
- Portfolio P&L tracking
- Position monitoring
- Risk violation alerts

### Performance Metrics
- Win rate and success rate
- Sharpe ratio and drawdown
- Strategy performance comparison

### Alert System
- Risk violation notifications
- Order execution alerts
- System status updates

## Best Practices

### 1. Start with Paper Trading
Always test strategies in paper trading mode before going live.

### 2. Monitor Risk Metrics
Regularly check risk violations and portfolio concentration.

### 3. Use Stop Losses
Implement proper stop-loss levels for all positions.

### 4. Diversification
Maintain proper diversification across stocks and sectors.

### 5. Regular Review
Review and adjust risk parameters based on market conditions.

## Troubleshooting

### Common Issues

1. **Broker Connection Failed**
   - Check API credentials
   - Verify network connectivity
   - Check broker API status

2. **Orders Not Executing**
   - Verify market hours
   - Check risk limits
   - Review order parameters

3. **Risk Violations**
   - Review position sizes
   - Check portfolio concentration
   - Adjust risk parameters

4. **System Performance**
   - Monitor CPU and memory usage
   - Check database connections
   - Review log files

### Log Files
- `logs/live_trading.log` - General trading logs
- `logs/orders.log` - Order execution logs
- `logs/risk_management.log` - Risk management logs

## Security Considerations

### API Security
- Store credentials securely
- Use environment variables
- Enable API rate limiting

### Access Control
- Restrict live trading access
- Implement role-based permissions
- Log all trading activities

### Data Protection
- Encrypt sensitive data
- Secure database connections
- Regular security audits

## Performance Optimization

### System Resources
- Monitor CPU and memory usage
- Optimize database queries
- Use connection pooling

### Trading Performance
- Minimize latency
- Optimize order routing
- Use efficient data structures

### Scalability
- Horizontal scaling support
- Load balancing
- Distributed processing

## Support and Maintenance

### Regular Maintenance
- Update broker APIs
- Review risk parameters
- Monitor system performance

### Backup and Recovery
- Regular database backups
- Configuration backups
- Disaster recovery plan

### Updates and Upgrades
- Test in paper trading first
- Gradual rollout
- Monitor after deployment

## Contact and Support

For technical support or questions:
- Check documentation and logs
- Review configuration settings
- Contact system administrator

---

**⚠️ Important Disclaimer:**
Live trading involves real financial risk. Always test thoroughly in paper trading mode before deploying with real money. The system is provided as-is without warranty. Users are responsible for their own trading decisions and risk management.
