"""
PPO-based Reinforcement Learning Optimizer for Trading Strategies.
Optimizes strategy parameters dynamically based on market conditions using PPO.
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.distributions import Normal
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, date, timedelta
import json
from collections import deque
import pickle

from ..backtesting.backtest_engine import BacktestEngine
from ..backtesting.backtest_runner import backtest_runner
from ..strategies.strategy_registry import strategy_registry
from ..data.database import db_manager
from ..data.models import RLModel, RLTrainingHistory
from ..data.crud import RLModelCRUD
from ..utils.logger import get_logger
from ..utils.config import config

logger = get_logger(__name__)

class PPOActor(nn.Module):
    """PPO Actor network for parameter optimization."""
    
    def __init__(self, state_dim: int, action_dim: int, hidden_dim: int = 256):
        """Initialize PPO Actor.
        
        Args:
            state_dim: Dimension of state space (market conditions)
            action_dim: Dimension of action space (strategy parameters)
            hidden_dim: Hidden layer dimension
        """
        super(PPOActor, self).__init__()
        
        self.fc1 = nn.Linear(state_dim, hidden_dim)
        self.fc2 = nn.Linear(hidden_dim, hidden_dim)
        self.fc3 = nn.Linear(hidden_dim, hidden_dim)
        
        # Mean and std for continuous actions
        self.mean_head = nn.Linear(hidden_dim, action_dim)
        self.std_head = nn.Linear(hidden_dim, action_dim)
        
        # Initialize weights
        self.apply(self._init_weights)
    
    def _init_weights(self, m):
        """Initialize network weights."""
        if isinstance(m, nn.Linear):
            nn.init.orthogonal_(m.weight, 0.01)
            nn.init.constant_(m.bias, 0)
    
    def forward(self, state: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """Forward pass.
        
        Args:
            state: Input state tensor
            
        Returns:
            Tuple of (mean, std) for action distribution
        """
        x = F.relu(self.fc1(state))
        x = F.relu(self.fc2(x))
        x = F.relu(self.fc3(x))
        
        mean = torch.tanh(self.mean_head(x))  # Bounded actions
        std = F.softplus(self.std_head(x)) + 1e-5  # Ensure positive std
        
        return mean, std
    
    def get_action(self, state: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """Get action from current policy.
        
        Args:
            state: Current state
            
        Returns:
            Tuple of (action, log_prob)
        """
        mean, std = self.forward(state)
        dist = Normal(mean, std)
        action = dist.sample()
        log_prob = dist.log_prob(action).sum(dim=-1)
        
        return action, log_prob
    
    def evaluate_action(self, state: torch.Tensor, action: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """Evaluate action under current policy.
        
        Args:
            state: State tensor
            action: Action tensor
            
        Returns:
            Tuple of (log_prob, entropy)
        """
        mean, std = self.forward(state)
        dist = Normal(mean, std)
        
        log_prob = dist.log_prob(action).sum(dim=-1)
        entropy = dist.entropy().sum(dim=-1)
        
        return log_prob, entropy

class PPOCritic(nn.Module):
    """PPO Critic network for value estimation."""
    
    def __init__(self, state_dim: int, hidden_dim: int = 256):
        """Initialize PPO Critic.
        
        Args:
            state_dim: Dimension of state space
            hidden_dim: Hidden layer dimension
        """
        super(PPOCritic, self).__init__()
        
        self.fc1 = nn.Linear(state_dim, hidden_dim)
        self.fc2 = nn.Linear(hidden_dim, hidden_dim)
        self.fc3 = nn.Linear(hidden_dim, hidden_dim)
        self.value_head = nn.Linear(hidden_dim, 1)
        
        # Initialize weights
        self.apply(self._init_weights)
    
    def _init_weights(self, m):
        """Initialize network weights."""
        if isinstance(m, nn.Linear):
            nn.init.orthogonal_(m.weight, 1.0)
            nn.init.constant_(m.bias, 0)
    
    def forward(self, state: torch.Tensor) -> torch.Tensor:
        """Forward pass.
        
        Args:
            state: Input state tensor
            
        Returns:
            Value estimate
        """
        x = F.relu(self.fc1(state))
        x = F.relu(self.fc2(x))
        x = F.relu(self.fc3(x))
        value = self.value_head(x)
        
        return value

class MarketStateExtractor:
    """Extracts market state features for RL agent."""
    
    def __init__(self):
        """Initialize market state extractor."""
        self.feature_names = [
            'volatility', 'trend_strength', 'volume_ratio', 'rsi_level',
            'market_regime', 'correlation', 'momentum', 'mean_reversion',
            'sector_performance', 'market_breadth'
        ]
        self.state_dim = len(self.feature_names)
    
    def extract_state(self, symbol: str, lookback_days: int = 30) -> np.ndarray:
        """Extract market state features for a symbol.
        
        Args:
            symbol: Stock symbol
            lookback_days: Number of days to look back
            
        Returns:
            State feature vector
        """
        try:
            with db_manager.get_session() as db:
                from ..data.crud import StockCRUD, PriceCRUD
                
                # Get stock and recent price data
                stock = StockCRUD.get_stock_by_symbol(db, symbol)
                if not stock:
                    return np.zeros(self.state_dim)
                
                end_date = date.today()
                start_date = end_date - timedelta(days=lookback_days + 50)  # Extra for calculations
                
                price_data = PriceCRUD.get_price_history(db, stock.id, start_date, end_date)
                
                if len(price_data) < lookback_days:
                    return np.zeros(self.state_dim)
                
                # Convert to arrays for calculations
                prices = np.array([float(p.close_price) for p in price_data])
                volumes = np.array([p.volume for p in price_data])
                
                # Calculate features
                features = np.zeros(self.state_dim)
                
                # 1. Volatility (20-day rolling std of returns)
                returns = np.diff(prices) / prices[:-1]
                if len(returns) >= 20:
                    features[0] = np.std(returns[-20:]) * np.sqrt(252)  # Annualized
                
                # 2. Trend strength (linear regression slope)
                if len(prices) >= 20:
                    x = np.arange(20)
                    y = prices[-20:]
                    slope = np.polyfit(x, y, 1)[0]
                    features[1] = slope / np.mean(y)  # Normalized slope
                
                # 3. Volume ratio (current vs average)
                if len(volumes) >= 20:
                    current_volume = volumes[-1]
                    avg_volume = np.mean(volumes[-20:])
                    features[2] = current_volume / avg_volume if avg_volume > 0 else 1.0
                
                # 4. RSI level (from database if available)
                if hasattr(price_data[-1], 'rsi_14') and price_data[-1].rsi_14:
                    features[3] = float(price_data[-1].rsi_14) / 100.0  # Normalize to 0-1
                else:
                    features[3] = 0.5  # Neutral
                
                # 5. Market regime (trending vs ranging)
                if len(prices) >= 50:
                    # Calculate ADX-like measure
                    high_low_range = np.std(prices[-20:]) / np.mean(prices[-20:])
                    features[4] = min(high_low_range * 10, 1.0)  # Normalize
                
                # 6. Correlation with market (simplified)
                features[5] = 0.7  # Placeholder - would calculate with Nifty 50
                
                # 7. Momentum (20-day price change)
                if len(prices) >= 20:
                    momentum = (prices[-1] - prices[-20]) / prices[-20]
                    features[6] = np.tanh(momentum * 5)  # Bounded momentum
                
                # 8. Mean reversion indicator
                if len(prices) >= 20:
                    current_price = prices[-1]
                    sma_20 = np.mean(prices[-20:])
                    deviation = (current_price - sma_20) / sma_20
                    features[7] = np.tanh(deviation * 3)  # Bounded deviation
                
                # 9. Sector performance (placeholder)
                features[8] = 0.0  # Would calculate sector relative performance
                
                # 10. Market breadth (placeholder)
                features[9] = 0.5  # Would calculate advance/decline ratio
                
                # Ensure all features are finite and bounded
                features = np.nan_to_num(features, nan=0.0, posinf=1.0, neginf=-1.0)
                features = np.clip(features, -3.0, 3.0)  # Clip extreme values
                
                return features
                
        except Exception as e:
            logger.error(f"Error extracting market state for {symbol}: {e}")
            return np.zeros(self.state_dim)

class RewardFunction:
    """Reward function for RL optimization."""
    
    def __init__(self):
        """Initialize reward function."""
        self.weights = {
            'return': 0.4,
            'sharpe': 0.3,
            'drawdown': 0.2,
            'stability': 0.1
        }
    
    def calculate_reward(self, backtest_result: Dict[str, Any]) -> float:
        """Calculate reward from backtest results.
        
        Args:
            backtest_result: Backtest results dictionary
            
        Returns:
            Reward value
        """
        try:
            if 'error' in backtest_result:
                return -1.0  # Penalty for failed backtests
            
            # Extract metrics
            total_return = backtest_result.get('total_return', 0) / 100  # Convert to decimal
            sharpe_ratio = backtest_result.get('sharpe_ratio', 0)
            max_drawdown = backtest_result.get('max_drawdown', 100) / 100  # Convert to decimal
            win_rate = backtest_result.get('win_rate', 0) / 100  # Convert to decimal
            
            # Calculate individual reward components
            
            # 1. Return component (normalized)
            return_reward = np.tanh(total_return * 2)  # Bounded return reward
            
            # 2. Sharpe ratio component
            sharpe_reward = np.tanh(sharpe_ratio / 2)  # Normalize Sharpe
            
            # 3. Drawdown penalty (negative reward for high drawdown)
            drawdown_penalty = -np.tanh(max_drawdown * 3)  # Penalty for drawdown
            
            # 4. Stability reward (based on win rate and trade count)
            total_trades = backtest_result.get('total_trades', 0)
            stability_reward = 0
            if total_trades > 10:  # Minimum trades for stability
                stability_reward = np.tanh((win_rate - 0.5) * 2)  # Reward for >50% win rate
            
            # Combine rewards
            total_reward = (
                self.weights['return'] * return_reward +
                self.weights['sharpe'] * sharpe_reward +
                self.weights['drawdown'] * drawdown_penalty +
                self.weights['stability'] * stability_reward
            )
            
            # Bonus for exceptional performance
            if sharpe_ratio > 2.0 and max_drawdown < 0.1:
                total_reward += 0.5
            
            # Penalty for poor performance
            if sharpe_ratio < 0 or max_drawdown > 0.3:
                total_reward -= 0.5
            
            return np.clip(total_reward, -2.0, 2.0)  # Bounded reward
            
        except Exception as e:
            logger.error(f"Error calculating reward: {e}")
            return -1.0

class PPOOptimizer:
    """PPO-based optimizer for trading strategy parameters."""
    
    def __init__(self, strategy_name: str, parameter_bounds: Dict[str, Tuple[float, float]]):
        """Initialize PPO optimizer.
        
        Args:
            strategy_name: Name of strategy to optimize
            parameter_bounds: Dictionary of parameter bounds
        """
        self.strategy_name = strategy_name
        self.parameter_bounds = parameter_bounds
        self.parameter_names = list(parameter_bounds.keys())
        
        # RL components
        self.state_extractor = MarketStateExtractor()
        self.reward_function = RewardFunction()
        
        # Network dimensions
        self.state_dim = self.state_extractor.state_dim
        self.action_dim = len(self.parameter_names)
        
        # PPO hyperparameters
        self.lr_actor = 3e-4
        self.lr_critic = 1e-3
        self.gamma = 0.99
        self.eps_clip = 0.2
        self.k_epochs = 4
        self.entropy_coef = 0.01
        self.value_coef = 0.5
        
        # Initialize networks
        self.actor = PPOActor(self.state_dim, self.action_dim)
        self.critic = PPOCritic(self.state_dim)
        self.actor_old = PPOActor(self.state_dim, self.action_dim)
        
        # Copy actor to old actor
        self.actor_old.load_state_dict(self.actor.state_dict())
        
        # Optimizers
        self.actor_optimizer = optim.Adam(self.actor.parameters(), lr=self.lr_actor)
        self.critic_optimizer = optim.Adam(self.critic.parameters(), lr=self.lr_critic)
        
        # Experience buffer
        self.buffer = {
            'states': [],
            'actions': [],
            'rewards': [],
            'log_probs': [],
            'values': [],
            'dones': []
        }
        
        # Training history
        self.training_history = []
        
        logger.info(f"Initialized PPO optimizer for {strategy_name}")
    
    def action_to_parameters(self, action: np.ndarray) -> Dict[str, Any]:
        """Convert action vector to strategy parameters.
        
        Args:
            action: Action vector from RL agent
            
        Returns:
            Dictionary of strategy parameters
        """
        parameters = {}
        
        for i, param_name in enumerate(self.parameter_names):
            # Convert from [-1, 1] to parameter bounds
            min_val, max_val = self.parameter_bounds[param_name]
            
            # Normalize action from [-1, 1] to [0, 1]
            normalized = (action[i] + 1) / 2
            
            # Scale to parameter bounds
            param_value = min_val + normalized * (max_val - min_val)
            
            # Handle integer parameters
            if param_name in ['rsi_period', 'lookback_period', 'confirmation_bars']:
                param_value = int(round(param_value))
            
            parameters[param_name] = param_value
        
        return parameters
    
    def parameters_to_action(self, parameters: Dict[str, Any]) -> np.ndarray:
        """Convert strategy parameters to action vector.
        
        Args:
            parameters: Dictionary of strategy parameters
            
        Returns:
            Action vector for RL agent
        """
        action = np.zeros(self.action_dim)
        
        for i, param_name in enumerate(self.parameter_names):
            if param_name in parameters:
                min_val, max_val = self.parameter_bounds[param_name]
                param_value = parameters[param_name]
                
                # Normalize to [0, 1]
                normalized = (param_value - min_val) / (max_val - min_val)
                
                # Convert to [-1, 1]
                action[i] = normalized * 2 - 1
            
        return action

    def get_action(self, state: np.ndarray) -> Tuple[np.ndarray, float]:
        """Get action from current policy.

        Args:
            state: Current market state

        Returns:
            Tuple of (action, log_prob)
        """
        state_tensor = torch.FloatTensor(state).unsqueeze(0)

        with torch.no_grad():
            action, log_prob = self.actor_old.get_action(state_tensor)

        return action.squeeze(0).numpy(), log_prob.item()

    def evaluate_backtest(self, symbol: str, parameters: Dict[str, Any]) -> float:
        """Evaluate parameters using backtesting.

        Args:
            symbol: Stock symbol to test on
            parameters: Strategy parameters

        Returns:
            Reward from backtest
        """
        try:
            # Run backtest with given parameters
            start_date = date.today() - timedelta(days=365)  # 1 year backtest
            end_date = date.today() - timedelta(days=1)

            result = backtest_runner.engine.run_single_strategy_backtest(
                self.strategy_name, symbol, start_date, end_date, parameters
            )

            # Calculate reward
            reward = self.reward_function.calculate_reward(result)

            return reward

        except Exception as e:
            logger.error(f"Error evaluating backtest: {e}")
            return -1.0

    def collect_experience(self, symbols: List[str], num_episodes: int = 10):
        """Collect experience for training.

        Args:
            symbols: List of symbols to train on
            num_episodes: Number of episodes per symbol
        """
        logger.info(f"Collecting experience for {self.strategy_name}")

        for symbol in symbols:
            logger.info(f"Training on {symbol}")

            for episode in range(num_episodes):
                # Get current market state
                state = self.state_extractor.extract_state(symbol)

                # Get action from policy
                action, log_prob = self.get_action(state)

                # Convert action to parameters
                parameters = self.action_to_parameters(action)

                # Evaluate parameters
                reward = self.evaluate_backtest(symbol, parameters)

                # Get value estimate
                state_tensor = torch.FloatTensor(state).unsqueeze(0)
                value = self.critic(state_tensor).item()

                # Store experience
                self.buffer['states'].append(state)
                self.buffer['actions'].append(action)
                self.buffer['rewards'].append(reward)
                self.buffer['log_probs'].append(log_prob)
                self.buffer['values'].append(value)
                self.buffer['dones'].append(True)  # Each episode is independent

                logger.debug(f"Episode {episode+1}/{num_episodes}: Reward = {reward:.3f}")

    def compute_advantages(self) -> Tuple[np.ndarray, np.ndarray]:
        """Compute advantages using GAE.

        Returns:
            Tuple of (advantages, returns)
        """
        rewards = np.array(self.buffer['rewards'])
        values = np.array(self.buffer['values'])
        dones = np.array(self.buffer['dones'])

        advantages = np.zeros_like(rewards)
        returns = np.zeros_like(rewards)

        # Since each episode is independent, we can compute advantages directly
        for i in range(len(rewards)):
            returns[i] = rewards[i]  # No future rewards in single-step episodes
            advantages[i] = rewards[i] - values[i]

        # Normalize advantages
        advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)

        return advantages, returns

    def update_policy(self):
        """Update policy using PPO."""
        if len(self.buffer['states']) == 0:
            return

        logger.info("Updating policy")

        # Convert buffer to tensors
        states = torch.FloatTensor(np.array(self.buffer['states']))
        actions = torch.FloatTensor(np.array(self.buffer['actions']))
        old_log_probs = torch.FloatTensor(self.buffer['log_probs'])

        # Compute advantages and returns
        advantages, returns = self.compute_advantages()
        advantages = torch.FloatTensor(advantages)
        returns = torch.FloatTensor(returns)

        # PPO update
        for _ in range(self.k_epochs):
            # Evaluate actions under current policy
            log_probs, entropy = self.actor.evaluate_action(states, actions)
            values = self.critic(states).squeeze()

            # Compute ratio
            ratio = torch.exp(log_probs - old_log_probs)

            # Compute surrogate losses
            surr1 = ratio * advantages
            surr2 = torch.clamp(ratio, 1 - self.eps_clip, 1 + self.eps_clip) * advantages

            # Actor loss
            actor_loss = -torch.min(surr1, surr2).mean()

            # Critic loss
            critic_loss = F.mse_loss(values, returns)

            # Entropy bonus
            entropy_loss = -entropy.mean()

            # Total loss
            total_loss = actor_loss + self.value_coef * critic_loss + self.entropy_coef * entropy_loss

            # Update actor
            self.actor_optimizer.zero_grad()
            actor_loss.backward(retain_graph=True)
            torch.nn.utils.clip_grad_norm_(self.actor.parameters(), 0.5)
            self.actor_optimizer.step()

            # Update critic
            self.critic_optimizer.zero_grad()
            critic_loss.backward()
            torch.nn.utils.clip_grad_norm_(self.critic.parameters(), 0.5)
            self.critic_optimizer.step()

        # Update old policy
        self.actor_old.load_state_dict(self.actor.state_dict())

        # Log training metrics
        avg_reward = np.mean(self.buffer['rewards'])
        self.training_history.append({
            'timestamp': datetime.now().isoformat(),
            'avg_reward': avg_reward,
            'actor_loss': actor_loss.item(),
            'critic_loss': critic_loss.item(),
            'entropy': entropy.mean().item()
        })

        logger.info(f"Policy updated. Avg reward: {avg_reward:.3f}")

        # Clear buffer
        self.clear_buffer()

    def clear_buffer(self):
        """Clear experience buffer."""
        for key in self.buffer:
            self.buffer[key] = []

    def train(self, symbols: List[str], num_iterations: int = 100, episodes_per_iteration: int = 5):
        """Train the PPO optimizer.

        Args:
            symbols: List of symbols to train on
            num_iterations: Number of training iterations
            episodes_per_iteration: Episodes per iteration
        """
        logger.info(f"Starting PPO training for {self.strategy_name}")
        logger.info(f"Training on {len(symbols)} symbols for {num_iterations} iterations")

        for iteration in range(num_iterations):
            logger.info(f"Training iteration {iteration + 1}/{num_iterations}")

            # Collect experience
            self.collect_experience(symbols, episodes_per_iteration)

            # Update policy
            self.update_policy()

            # Save model periodically
            if (iteration + 1) % 10 == 0:
                self.save_model()

            # Log progress
            if self.training_history:
                latest_metrics = self.training_history[-1]
                logger.info(f"Iteration {iteration + 1} - Avg Reward: {latest_metrics['avg_reward']:.3f}")

        # Final model save
        self.save_model()
        logger.info("PPO training completed")

    def get_optimal_parameters(self, symbol: str) -> Dict[str, Any]:
        """Get optimal parameters for a symbol.

        Args:
            symbol: Stock symbol

        Returns:
            Optimal parameters dictionary
        """
        try:
            # Extract current market state
            state = self.state_extractor.extract_state(symbol)

            # Get action from trained policy
            action, _ = self.get_action(state)

            # Convert to parameters
            parameters = self.action_to_parameters(action)

            logger.info(f"Optimal parameters for {symbol}: {parameters}")
            return parameters

        except Exception as e:
            logger.error(f"Error getting optimal parameters for {symbol}: {e}")
            return {}

    def save_model(self):
        """Save model to database and file."""
        try:
            # Save to file
            model_data = {
                'actor_state_dict': self.actor.state_dict(),
                'critic_state_dict': self.critic.state_dict(),
                'actor_old_state_dict': self.actor_old.state_dict(),
                'parameter_bounds': self.parameter_bounds,
                'training_history': self.training_history
            }

            model_path = f"models/rl/{self.strategy_name}_ppo_model.pkl"
            import os
            os.makedirs(os.path.dirname(model_path), exist_ok=True)

            with open(model_path, 'wb') as f:
                pickle.dump(model_data, f)

            # Save to database
            with db_manager.get_session() as db:
                model_db_data = {
                    'strategy_name': self.strategy_name,
                    'model_type': 'PPO',
                    'model_path': model_path,
                    'parameters': json.dumps(self.parameter_bounds),
                    'performance_metrics': json.dumps({
                        'avg_reward': np.mean([h['avg_reward'] for h in self.training_history[-10:]]) if self.training_history else 0,
                        'training_iterations': len(self.training_history)
                    }),
                    'is_active': True
                }

                RLModelCRUD.create_or_update_model(db, model_db_data)

            logger.info(f"Model saved for {self.strategy_name}")

        except Exception as e:
            logger.error(f"Error saving model: {e}")

    def load_model(self, model_path: Optional[str] = None):
        """Load model from file.

        Args:
            model_path: Path to model file (optional)
        """
        try:
            if model_path is None:
                model_path = f"models/rl/{self.strategy_name}_ppo_model.pkl"

            import os
            if not os.path.exists(model_path):
                logger.warning(f"Model file not found: {model_path}")
                return False

            with open(model_path, 'rb') as f:
                model_data = pickle.load(f)

            # Load network states
            self.actor.load_state_dict(model_data['actor_state_dict'])
            self.critic.load_state_dict(model_data['critic_state_dict'])
            self.actor_old.load_state_dict(model_data['actor_old_state_dict'])

            # Load training history
            self.training_history = model_data.get('training_history', [])

            logger.info(f"Model loaded for {self.strategy_name}")
            return True

        except Exception as e:
            logger.error(f"Error loading model: {e}")
            return False

    def get_optimal_parameters(self, symbol: str) -> Dict[str, Any]:
        """Get optimal parameters for a symbol.

        Args:
            symbol: Stock symbol

        Returns:
            Optimal parameters dictionary
        """
        try:
            # Extract current market state
            state = self.state_extractor.extract_state(symbol)

            # Get action from trained policy
            action, _ = self.get_action(state)

            # Convert to parameters
            parameters = self.action_to_parameters(action)

            logger.info(f"Optimal parameters for {symbol}: {parameters}")
            return parameters

        except Exception as e:
            logger.error(f"Error getting optimal parameters for {symbol}: {e}")
            return {}

    def save_model(self):
        """Save model to database and file."""
        try:
            # Save to file
            model_data = {
                'actor_state_dict': self.actor.state_dict(),
                'critic_state_dict': self.critic.state_dict(),
                'actor_old_state_dict': self.actor_old.state_dict(),
                'parameter_bounds': self.parameter_bounds,
                'training_history': self.training_history
            }

            model_path = f"models/rl/{self.strategy_name}_ppo_model.pkl"
            import os
            os.makedirs(os.path.dirname(model_path), exist_ok=True)

            with open(model_path, 'wb') as f:
                pickle.dump(model_data, f)

            # Save to database
            with db_manager.get_session() as db:
                model_db_data = {
                    'strategy_name': self.strategy_name,
                    'model_type': 'PPO',
                    'model_path': model_path,
                    'parameters': json.dumps(self.parameter_bounds),
                    'performance_metrics': json.dumps({
                        'avg_reward': np.mean([h['avg_reward'] for h in self.training_history[-10:]]) if self.training_history else 0,
                        'training_iterations': len(self.training_history)
                    }),
                    'is_active': True
                }

                RLModelCRUD.create_or_update_model(db, model_db_data)

            logger.info(f"Model saved for {self.strategy_name}")

        except Exception as e:
            logger.error(f"Error saving model: {e}")

    def load_model(self, model_path: Optional[str] = None):
        """Load model from file.

        Args:
            model_path: Path to model file (optional)
        """
        try:
            if model_path is None:
                model_path = f"models/rl/{self.strategy_name}_ppo_model.pkl"

            import os
            if not os.path.exists(model_path):
                logger.warning(f"Model file not found: {model_path}")
                return False

            with open(model_path, 'rb') as f:
                model_data = pickle.load(f)

            # Load network states
            self.actor.load_state_dict(model_data['actor_state_dict'])
            self.critic.load_state_dict(model_data['critic_state_dict'])
            self.actor_old.load_state_dict(model_data['actor_old_state_dict'])

            # Load training history
            self.training_history = model_data.get('training_history', [])

            logger.info(f"Model loaded for {self.strategy_name}")
            return True

        except Exception as e:
            logger.error(f"Error loading model: {e}")
            return False
