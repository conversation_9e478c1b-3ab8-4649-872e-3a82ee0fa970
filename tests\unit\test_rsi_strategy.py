"""
Unit tests for Adaptive RSI Strategy.
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
from unittest.mock import Mock, patch

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from src.strategies.rsi_strategy import AdaptiveRSIStrategy
from src.strategies.base_strategy import SignalType

class TestAdaptiveRSIStrategy:
    """Test cases for Adaptive RSI Strategy."""
    
    @pytest.fixture
    def sample_data(self):
        """Create sample data for testing."""
        dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
        np.random.seed(42)
        
        # Generate realistic price data
        prices = []
        current_price = 100.0
        
        for _ in range(len(dates)):
            change = np.random.normal(0, 0.02)  # 2% daily volatility
            current_price *= (1 + change)
            prices.append(current_price)
        
        data = pd.DataFrame({
            'date': dates[:len(prices)],
            'open_price': prices,
            'high_price': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
            'low_price': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
            'close_price': prices,
            'volume': np.random.randint(100000, 1000000, len(prices)),
        })
        
        # Add technical indicators
        data['rsi_14'] = self._calculate_rsi(pd.Series(prices), 14)
        data['sma_20'] = pd.Series(prices).rolling(20).mean()
        data['sma_50'] = pd.Series(prices).rolling(50).mean()
        data['sma_200'] = pd.Series(prices).rolling(200).mean()
        data['volume_ratio'] = np.random.uniform(0.5, 3.0, len(prices))
        data['bb_upper'] = data['sma_20'] * 1.02
        data['bb_lower'] = data['sma_20'] * 0.98
        
        return data
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate RSI for test data."""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    @pytest.fixture
    def strategy(self):
        """Create strategy instance for testing."""
        return AdaptiveRSIStrategy()
    
    def test_strategy_initialization(self, strategy):
        """Test strategy initialization."""
        assert strategy.name == "adaptive_rsi"
        assert strategy.rsi_period == 14
        assert strategy.base_oversold == 30
        assert strategy.base_overbought == 70
        assert len(strategy.get_required_indicators()) > 0
    
    def test_required_indicators(self, strategy):
        """Test required indicators list."""
        indicators = strategy.get_required_indicators()
        expected_indicators = ['rsi_14', 'sma_20', 'sma_50', 'sma_200', 'volume_ratio', 'bb_upper', 'bb_lower']
        
        for indicator in expected_indicators:
            assert indicator in indicators
    
    def test_data_validation(self, strategy, sample_data):
        """Test data validation."""
        # Valid data should pass
        assert strategy.validate_data(sample_data) == True
        
        # Missing required column should fail
        invalid_data = sample_data.drop(columns=['rsi_14'])
        assert strategy.validate_data(invalid_data) == False
        
        # Insufficient data should fail
        short_data = sample_data.head(10)
        assert strategy.validate_data(short_data) == False
    
    def test_dynamic_thresholds_calculation(self, strategy, sample_data):
        """Test dynamic threshold calculation."""
        oversold, overbought = strategy.calculate_dynamic_thresholds(sample_data)
        
        # Check that thresholds are series
        assert isinstance(oversold, pd.Series)
        assert isinstance(overbought, pd.Series)
        
        # Check that thresholds are within bounds
        assert oversold.min() >= 20
        assert oversold.max() <= 35
        assert overbought.min() >= 65
        assert overbought.max() <= 80
        
        # Check that oversold < overbought
        assert (oversold < overbought).all()
    
    def test_rsi_divergence_detection(self, strategy, sample_data):
        """Test RSI divergence detection."""
        divergences = strategy.detect_rsi_divergence(sample_data)
        
        # Check return structure
        assert 'bullish' in divergences
        assert 'bearish' in divergences
        assert isinstance(divergences['bullish'], list)
        assert isinstance(divergences['bearish'], list)
    
    def test_signal_strength_calculation(self, strategy):
        """Test signal strength calculation."""
        # Test BUY signal strength
        strength = strategy.calculate_signal_strength(
            rsi_value=25,
            threshold=30,
            signal_type=SignalType.BUY,
            has_divergence=True,
            volume_confirmed=True,
            trend_aligned=True
        )
        
        assert 0.0 <= strength <= 1.0
        assert strength > 0.5  # Should be strong signal
        
        # Test SELL signal strength
        strength = strategy.calculate_signal_strength(
            rsi_value=75,
            threshold=70,
            signal_type=SignalType.SELL,
            has_divergence=False,
            volume_confirmed=False,
            trend_aligned=False
        )
        
        assert 0.0 <= strength <= 1.0
    
    def test_multiple_timeframe_analysis(self, strategy, sample_data):
        """Test multiple timeframe analysis."""
        # Test with sufficient data
        analysis = strategy.analyze_multiple_timeframes(sample_data, 100)
        
        expected_keys = [
            'short_term_bullish', 'medium_term_bullish', 'long_term_bullish',
            'short_term_bearish', 'medium_term_bearish', 'long_term_bearish'
        ]
        
        for key in expected_keys:
            assert key in analysis
            assert isinstance(analysis[key], bool)
        
        # Test with insufficient data
        analysis = strategy.analyze_multiple_timeframes(sample_data, 10)
        assert all(not value for value in analysis.values())
    
    def test_signal_generation(self, strategy, sample_data):
        """Test signal generation."""
        signals = strategy.calculate_signals(sample_data)
        
        # Check that signals are generated
        assert isinstance(signals, list)
        
        # Check signal properties if any signals generated
        for signal in signals:
            assert signal.signal_type in [SignalType.BUY, SignalType.SELL]
            assert 0.0 <= signal.signal_strength <= 1.0
            assert signal.price > 0
            assert signal.stop_loss is not None
            assert signal.target_price is not None
            assert signal.parameters is not None
    
    def test_signal_filtering(self, strategy):
        """Test signal filtering."""
        from src.strategies.base_strategy import TradingSignal
        
        # Create test signals
        strong_signal = TradingSignal(
            symbol="TEST",
            signal_type=SignalType.BUY,
            signal_strength=0.8,
            price=100.0,
            confidence_score=0.9
        )
        
        weak_signal = TradingSignal(
            symbol="TEST",
            signal_type=SignalType.BUY,
            signal_strength=0.3,
            price=100.0,
            confidence_score=0.4
        )
        
        signals = [strong_signal, weak_signal]
        filtered = strategy.filter_signals(signals)
        
        # Only strong signal should pass
        assert len(filtered) == 1
        assert filtered[0].signal_strength == 0.8
    
    def test_custom_parameters(self):
        """Test strategy with custom parameters."""
        custom_params = {
            'rsi_period': 21,
            'base_oversold': 25,
            'base_overbought': 75,
            'min_signal_strength': 0.7
        }
        
        strategy = AdaptiveRSIStrategy(custom_params)
        
        assert strategy.rsi_period == 21
        assert strategy.base_oversold == 25
        assert strategy.base_overbought == 75
        assert strategy.parameters['min_signal_strength'] == 0.7
    
    @patch('src.strategies.rsi_strategy.db_manager')
    def test_generate_signals_for_stock(self, mock_db_manager, strategy):
        """Test signal generation for a specific stock."""
        # Mock database responses
        mock_session = Mock()
        mock_db_manager.get_session.return_value.__enter__.return_value = mock_session
        
        # Mock stock data
        mock_stock = Mock()
        mock_stock.id = 1
        mock_session.query.return_value.filter.return_value.first.return_value = mock_stock
        
        # Mock price data
        mock_price_data = []
        for i in range(100):
            mock_price = Mock()
            mock_price.date = date.today() - timedelta(days=100-i)
            mock_price.open_price = 100 + i * 0.1
            mock_price.high_price = 101 + i * 0.1
            mock_price.low_price = 99 + i * 0.1
            mock_price.close_price = 100.5 + i * 0.1
            mock_price.volume = 100000
            mock_price.rsi_14 = 50 + (i % 40) - 20  # Oscillating RSI
            mock_price.sma_20 = 100 + i * 0.05
            mock_price.sma_50 = 100 + i * 0.03
            mock_price.sma_200 = 100 + i * 0.01
            mock_price.volume_ratio = 1.5
            mock_price.bb_upper = 102 + i * 0.1
            mock_price.bb_lower = 98 + i * 0.1
            mock_price_data.append(mock_price)
        
        mock_session.query.return_value.filter.return_value.all.return_value = mock_price_data
        
        # Test signal generation
        signals = strategy.generate_signals_for_stock("TEST")
        
        # Should return list of signals
        assert isinstance(signals, list)

if __name__ == "__main__":
    pytest.main([__file__])
