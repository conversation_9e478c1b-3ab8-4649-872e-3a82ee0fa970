"""
Portfolio Optimization Router - API endpoints for portfolio optimization.
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from typing import List, Optional, Dict, Any
from datetime import datetime, date
from pydantic import BaseModel

from ..models import APIResponse, PaginationParams
from ..main import get_current_user
from ...portfolio_optimization.optimizer import (
    portfolio_optimizer, PortfolioOptimizationRequest, OptimizationMethod
)
from ...portfolio_optimization.mpt_optimizer import PortfolioConstraints
from ...portfolio_optimization.black_litterman import InvestorView
from ...portfolio_optimization.rebalancing import RebalancingConfig, RebalancingTrigger
from ...utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter()

# Pydantic models for API
class OptimizationRequestModel(BaseModel):
    symbols: List[str]
    method: str
    constraints: Optional[Dict[str, Any]] = None
    target_return: Optional[float] = None
    investor_views: Optional[List[Dict[str, Any]]] = None
    lookback_days: int = 252
    benchmark_symbol: Optional[str] = None

class InvestorViewModel(BaseModel):
    assets: List[str]
    view_return: float
    confidence: float
    view_type: str
    description: str

class ConstraintsModel(BaseModel):
    min_weight: float = 0.0
    max_weight: float = 1.0
    max_concentration: float = 0.15
    min_positions: int = 5
    max_positions: int = 20

class RebalancingConfigModel(BaseModel):
    trigger_type: str
    frequency_days: Optional[int] = None
    threshold_percent: Optional[float] = None
    volatility_threshold: Optional[float] = None
    performance_threshold: Optional[float] = None
    min_trade_size: float = 0.01
    transaction_cost: float = 0.001
    max_turnover: float = 0.5

@router.post("/optimize", response_model=APIResponse)
async def optimize_portfolio(
    request: OptimizationRequestModel,
    current_user: dict = Depends(get_current_user)
):
    """Optimize portfolio using specified method."""
    try:
        # Validate optimization method
        try:
            optimization_method = OptimizationMethod(request.method.lower())
        except ValueError:
            raise HTTPException(status_code=400, detail=f"Invalid optimization method: {request.method}")
        
        # Create constraints
        constraints = None
        if request.constraints:
            constraints = PortfolioConstraints(
                min_weight=request.constraints.get('min_weight', 0.0),
                max_weight=request.constraints.get('max_weight', 1.0),
                max_concentration=request.constraints.get('max_concentration', 0.15),
                min_positions=request.constraints.get('min_positions', 5),
                max_positions=request.constraints.get('max_positions', 20)
            )
        
        # Create investor views for Black-Litterman
        investor_views = None
        if request.investor_views:
            investor_views = []
            for view_data in request.investor_views:
                view = InvestorView(
                    assets=view_data['assets'],
                    view_return=view_data['view_return'],
                    confidence=view_data['confidence'],
                    view_type=view_data['view_type'],
                    description=view_data['description']
                )
                investor_views.append(view)
        
        # Create optimization request
        opt_request = PortfolioOptimizationRequest(
            symbols=request.symbols,
            method=optimization_method,
            constraints=constraints,
            target_return=request.target_return,
            investor_views=investor_views,
            lookback_days=request.lookback_days,
            benchmark_symbol=request.benchmark_symbol
        )
        
        # Execute optimization
        response = portfolio_optimizer.optimize_portfolio(opt_request)
        
        # Format response
        result_data = {
            'optimization_result': {
                'weights': {symbol: float(weight) for symbol, weight in 
                           zip(response.optimization_result.symbols, response.optimization_result.weights)},
                'expected_return': float(response.optimization_result.expected_return),
                'volatility': float(response.optimization_result.volatility),
                'sharpe_ratio': float(response.optimization_result.sharpe_ratio),
                'optimization_method': response.optimization_result.optimization_method,
                'optimization_success': response.optimization_result.optimization_success,
                'additional_metrics': response.optimization_result.additional_metrics
            },
            'performance_metrics': {
                'total_return': float(response.performance_metrics.total_return) if response.performance_metrics else None,
                'annualized_return': float(response.performance_metrics.annualized_return) if response.performance_metrics else None,
                'volatility': float(response.performance_metrics.volatility) if response.performance_metrics else None,
                'sharpe_ratio': float(response.performance_metrics.sharpe_ratio) if response.performance_metrics else None,
                'max_drawdown': float(response.performance_metrics.max_drawdown) if response.performance_metrics else None,
                'var_95': float(response.performance_metrics.var_95) if response.performance_metrics else None
            } if response.performance_metrics else None,
            'risk_analysis': response.risk_analysis,
            'rebalancing_analysis': response.rebalancing_analysis,
            'recommendations': response.recommendations
        }
        
        return APIResponse(
            success=True,
            message="Portfolio optimization completed successfully",
            data=result_data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in portfolio optimization: {e}")
        raise HTTPException(status_code=500, detail="Portfolio optimization failed")

@router.post("/compare-methods", response_model=APIResponse)
async def compare_optimization_methods(
    symbols: List[str],
    methods: List[str],
    constraints: Optional[ConstraintsModel] = None,
    lookback_days: int = 252,
    current_user: dict = Depends(get_current_user)
):
    """Compare multiple optimization methods."""
    try:
        # Validate methods
        optimization_methods = []
        for method in methods:
            try:
                optimization_methods.append(OptimizationMethod(method.lower()))
            except ValueError:
                raise HTTPException(status_code=400, detail=f"Invalid optimization method: {method}")
        
        # Create constraints
        portfolio_constraints = None
        if constraints:
            portfolio_constraints = PortfolioConstraints(
                min_weight=constraints.min_weight,
                max_weight=constraints.max_weight,
                max_concentration=constraints.max_concentration,
                min_positions=constraints.min_positions,
                max_positions=constraints.max_positions
            )
        
        # Compare methods
        results = portfolio_optimizer.compare_optimization_methods(
            symbols, optimization_methods, portfolio_constraints, lookback_days
        )
        
        # Format results
        comparison_data = {}
        for method, response in results.items():
            comparison_data[method] = {
                'weights': {symbol: float(weight) for symbol, weight in 
                           zip(response.optimization_result.symbols, response.optimization_result.weights)},
                'expected_return': float(response.optimization_result.expected_return),
                'volatility': float(response.optimization_result.volatility),
                'sharpe_ratio': float(response.optimization_result.sharpe_ratio),
                'optimization_success': response.optimization_result.optimization_success,
                'performance_metrics': {
                    'annualized_return': float(response.performance_metrics.annualized_return) if response.performance_metrics else None,
                    'volatility': float(response.performance_metrics.volatility) if response.performance_metrics else None,
                    'sharpe_ratio': float(response.performance_metrics.sharpe_ratio) if response.performance_metrics else None,
                    'max_drawdown': float(response.performance_metrics.max_drawdown) if response.performance_metrics else None
                } if response.performance_metrics else None
            }
        
        return APIResponse(
            success=True,
            message=f"Compared {len(results)} optimization methods",
            data={
                'comparison_results': comparison_data,
                'summary': {
                    'methods_compared': len(results),
                    'symbols_count': len(symbols),
                    'lookback_days': lookback_days
                }
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error comparing optimization methods: {e}")
        raise HTTPException(status_code=500, detail="Method comparison failed")

@router.get("/efficient-frontier", response_model=APIResponse)
async def generate_efficient_frontier(
    symbols: List[str] = Query(...),
    n_points: int = Query(50, ge=10, le=100),
    lookback_days: int = Query(252, ge=30, le=1000),
    current_user: dict = Depends(get_current_user)
):
    """Generate efficient frontier for given symbols."""
    try:
        from ...portfolio_optimization.mpt_optimizer import mpt_optimizer
        
        # Calculate returns and covariance
        expected_returns, covariance_matrix = mpt_optimizer.calculate_returns_covariance(
            symbols, lookback_days
        )
        
        # Generate efficient frontier
        constraints = PortfolioConstraints()
        efficient_portfolios = mpt_optimizer.generate_efficient_frontier(
            expected_returns, covariance_matrix, constraints, n_points
        )
        
        # Format results
        frontier_data = []
        for portfolio in efficient_portfolios:
            frontier_data.append({
                'expected_return': float(portfolio.expected_return),
                'volatility': float(portfolio.volatility),
                'sharpe_ratio': float(portfolio.sharpe_ratio),
                'weights': {symbol: float(weight) for symbol, weight in 
                           zip(symbols, portfolio.weights)}
            })
        
        return APIResponse(
            success=True,
            message=f"Generated efficient frontier with {len(frontier_data)} points",
            data={
                'efficient_frontier': frontier_data,
                'symbols': symbols,
                'lookback_days': lookback_days,
                'n_points': len(frontier_data)
            }
        )
        
    except Exception as e:
        logger.error(f"Error generating efficient frontier: {e}")
        raise HTTPException(status_code=500, detail="Efficient frontier generation failed")

@router.post("/rebalancing/analyze", response_model=APIResponse)
async def analyze_rebalancing(
    current_weights: Dict[str, float],
    target_weights: Dict[str, float],
    portfolio_value: float,
    config: Optional[RebalancingConfigModel] = None,
    current_user: dict = Depends(get_current_user)
):
    """Analyze portfolio rebalancing requirements."""
    try:
        from ...portfolio_optimization.rebalancing import portfolio_rebalancer
        
        # Create rebalancing config
        rebalancing_config = None
        if config:
            rebalancing_config = RebalancingConfig(
                trigger_type=RebalancingTrigger(config.trigger_type.lower()),
                frequency_days=config.frequency_days,
                threshold_percent=config.threshold_percent,
                volatility_threshold=config.volatility_threshold,
                performance_threshold=config.performance_threshold,
                min_trade_size=config.min_trade_size,
                transaction_cost=config.transaction_cost,
                max_turnover=config.max_turnover
            )
        
        # Execute rebalancing analysis
        rebalancing_result = portfolio_rebalancer.execute_rebalancing(
            current_weights,
            target_weights,
            portfolio_value,
            rebalancing_config
        )
        
        if rebalancing_result:
            # Format actions
            actions_data = []
            for action in rebalancing_result.actions:
                actions_data.append({
                    'symbol': action.symbol,
                    'current_weight': float(action.current_weight),
                    'target_weight': float(action.target_weight),
                    'weight_change': float(action.weight_change),
                    'trade_amount': float(action.trade_amount),
                    'trade_direction': action.trade_direction,
                    'transaction_cost': float(action.transaction_cost)
                })
            
            result_data = {
                'rebalancing_needed': True,
                'rebalancing_date': rebalancing_result.rebalancing_date.isoformat(),
                'trigger_reason': rebalancing_result.trigger_reason,
                'actions': actions_data,
                'total_turnover': float(rebalancing_result.total_turnover),
                'total_transaction_cost': float(rebalancing_result.total_transaction_cost),
                'expected_improvement': float(rebalancing_result.expected_improvement),
                'current_portfolio': rebalancing_result.current_portfolio,
                'target_portfolio': rebalancing_result.target_portfolio
            }
        else:
            result_data = {
                'rebalancing_needed': False,
                'message': 'No rebalancing required'
            }
        
        return APIResponse(
            success=True,
            message="Rebalancing analysis completed",
            data=result_data
        )
        
    except Exception as e:
        logger.error(f"Error in rebalancing analysis: {e}")
        raise HTTPException(status_code=500, detail="Rebalancing analysis failed")

@router.get("/performance/analyze", response_model=APIResponse)
async def analyze_portfolio_performance(
    symbols: List[str] = Query(...),
    weights: List[float] = Query(...),
    lookback_days: int = Query(252, ge=30, le=1000),
    benchmark_symbol: Optional[str] = Query(None),
    current_user: dict = Depends(get_current_user)
):
    """Analyze portfolio performance metrics."""
    try:
        if len(symbols) != len(weights):
            raise HTTPException(status_code=400, detail="Symbols and weights must have same length")
        
        if abs(sum(weights) - 1.0) > 0.01:
            raise HTTPException(status_code=400, detail="Weights must sum to 1.0")
        
        from ...portfolio_optimization.performance_analytics import performance_analytics
        from ...data.database import db_manager
        from ...data.crud import StockCRUD, PriceCRUD
        
        # Get historical returns
        returns_data = []
        
        with db_manager.get_session() as db:
            for symbol in symbols:
                stock = StockCRUD.get_stock_by_symbol(db, symbol)
                if not stock:
                    continue
                
                prices = PriceCRUD.get_recent_prices(db, stock.id, lookback_days + 1)
                
                if len(prices) < lookback_days:
                    continue
                
                price_series = [float(p.close_price) for p in reversed(prices)]
                returns = performance_analytics.calculate_returns(np.array(price_series))
                returns_data.append(returns)
        
        if len(returns_data) < len(symbols):
            raise HTTPException(status_code=400, detail="Insufficient data for some symbols")
        
        # Calculate portfolio returns
        import numpy as np
        returns_matrix = np.array(returns_data).T
        weights_array = np.array(weights)
        portfolio_returns = performance_analytics.calculate_portfolio_returns(
            weights_array, returns_matrix
        )
        
        # Get benchmark returns if specified
        benchmark_returns = None
        if benchmark_symbol:
            with db_manager.get_session() as db:
                benchmark_stock = StockCRUD.get_stock_by_symbol(db, benchmark_symbol)
                if benchmark_stock:
                    benchmark_prices = PriceCRUD.get_recent_prices(
                        db, benchmark_stock.id, lookback_days + 1
                    )
                    
                    if len(benchmark_prices) >= lookback_days:
                        benchmark_price_series = [float(p.close_price) for p in reversed(benchmark_prices)]
                        benchmark_returns = performance_analytics.calculate_returns(
                            np.array(benchmark_price_series)
                        )
        
        # Calculate performance metrics
        performance_metrics = performance_analytics.calculate_performance_metrics(
            portfolio_returns, benchmark_returns
        )
        
        # Calculate rolling metrics
        rolling_metrics = performance_analytics.calculate_rolling_metrics(portfolio_returns)
        
        # Format response
        result_data = {
            'performance_metrics': {
                'total_return': float(performance_metrics.total_return),
                'annualized_return': float(performance_metrics.annualized_return),
                'volatility': float(performance_metrics.volatility),
                'sharpe_ratio': float(performance_metrics.sharpe_ratio),
                'sortino_ratio': float(performance_metrics.sortino_ratio),
                'max_drawdown': float(performance_metrics.max_drawdown),
                'calmar_ratio': float(performance_metrics.calmar_ratio),
                'var_95': float(performance_metrics.var_95),
                'cvar_95': float(performance_metrics.cvar_95),
                'skewness': float(performance_metrics.skewness),
                'kurtosis': float(performance_metrics.kurtosis),
                'beta': float(performance_metrics.beta),
                'alpha': float(performance_metrics.alpha),
                'information_ratio': float(performance_metrics.information_ratio),
                'tracking_error': float(performance_metrics.tracking_error),
                'win_rate': float(performance_metrics.win_rate),
                'profit_factor': float(performance_metrics.profit_factor)
            },
            'rolling_metrics': {
                metric: [float(x) for x in values] for metric, values in rolling_metrics.items()
            },
            'portfolio_composition': {
                symbol: float(weight) for symbol, weight in zip(symbols, weights)
            },
            'analysis_period': {
                'lookback_days': lookback_days,
                'total_observations': len(portfolio_returns)
            }
        }
        
        return APIResponse(
            success=True,
            message="Portfolio performance analysis completed",
            data=result_data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in performance analysis: {e}")
        raise HTTPException(status_code=500, detail="Performance analysis failed")

@router.get("/methods", response_model=APIResponse)
async def get_optimization_methods(current_user: dict = Depends(get_current_user)):
    """Get available optimization methods."""
    try:
        methods = [
            {
                'name': 'max_sharpe',
                'display_name': 'Maximum Sharpe Ratio',
                'description': 'Optimize for maximum risk-adjusted returns',
                'requires_target_return': False,
                'requires_views': False
            },
            {
                'name': 'min_variance',
                'display_name': 'Minimum Variance',
                'description': 'Minimize portfolio volatility',
                'requires_target_return': False,
                'requires_views': False
            },
            {
                'name': 'target_return',
                'display_name': 'Target Return',
                'description': 'Achieve target return with minimum risk',
                'requires_target_return': True,
                'requires_views': False
            },
            {
                'name': 'risk_parity_erc',
                'display_name': 'Equal Risk Contribution',
                'description': 'Equal risk contribution from all assets',
                'requires_target_return': False,
                'requires_views': False
            },
            {
                'name': 'risk_parity_hrp',
                'display_name': 'Hierarchical Risk Parity',
                'description': 'Hierarchical clustering-based risk parity',
                'requires_target_return': False,
                'requires_views': False
            },
            {
                'name': 'black_litterman',
                'display_name': 'Black-Litterman',
                'description': 'Incorporate investor views with market equilibrium',
                'requires_target_return': False,
                'requires_views': True
            },
            {
                'name': 'equal_weight',
                'display_name': 'Equal Weight',
                'description': 'Equal allocation to all assets',
                'requires_target_return': False,
                'requires_views': False
            }
        ]
        
        return APIResponse(
            success=True,
            message="Available optimization methods retrieved",
            data={
                'methods': methods,
                'total_methods': len(methods)
            }
        )
        
    except Exception as e:
        logger.error(f"Error getting optimization methods: {e}")
        raise HTTPException(status_code=500, detail="Failed to get optimization methods")
