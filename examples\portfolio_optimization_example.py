"""
Portfolio Optimization Examples.
Demonstrates various portfolio optimization techniques and analytics.
"""

import asyncio
import numpy as np
from datetime import date, timedelta
from typing import List, Dict

# Import portfolio optimization components
from src.portfolio_optimization.optimizer import (
    portfolio_optimizer, PortfolioOptimizationRequest, OptimizationMethod
)
from src.portfolio_optimization.mpt_optimizer import PortfolioConstraints
from src.portfolio_optimization.black_litterman import InvestorView
from src.portfolio_optimization.rebalancing import RebalancingConfig, RebalancingTrigger
from src.portfolio_optimization.performance_analytics import performance_analytics

async def example_basic_optimization():
    """Example: Basic portfolio optimization using different methods."""
    print("=== Basic Portfolio Optimization Example ===")
    
    # Define universe of stocks (Nifty 50 sample)
    symbols = [
        'RELIANCE', 'TCS', 'HDFCBANK', 'INFY', 'HINDUNILVR',
        'ICICIBANK', 'KOTAKBANK', 'SBIN', 'BHARTIARTL', 'ITC'
    ]
    
    # Define constraints
    constraints = PortfolioConstraints(
        min_weight=0.02,  # Minimum 2% allocation
        max_weight=0.25,  # Maximum 25% allocation
        max_concentration=0.20,  # Maximum 20% in any single stock
        min_positions=5,
        max_positions=10
    )
    
    # Test different optimization methods
    methods = [
        OptimizationMethod.MAX_SHARPE,
        OptimizationMethod.MIN_VARIANCE,
        OptimizationMethod.RISK_PARITY_ERC,
        OptimizationMethod.EQUAL_WEIGHT
    ]
    
    results = {}
    
    for method in methods:
        try:
            print(f"\nOptimizing with method: {method.value}")
            
            request = PortfolioOptimizationRequest(
                symbols=symbols,
                method=method,
                constraints=constraints,
                lookback_days=252
            )
            
            response = portfolio_optimizer.optimize_portfolio(request)
            results[method.value] = response
            
            # Print results
            opt_result = response.optimization_result
            print(f"Expected Return: {opt_result.expected_return:.4f}")
            print(f"Volatility: {opt_result.volatility:.4f}")
            print(f"Sharpe Ratio: {opt_result.sharpe_ratio:.4f}")
            
            # Print top 5 holdings
            weights_dict = {symbol: weight for symbol, weight in 
                           zip(opt_result.symbols, opt_result.weights)}
            sorted_weights = sorted(weights_dict.items(), key=lambda x: x[1], reverse=True)
            
            print("Top 5 Holdings:")
            for symbol, weight in sorted_weights[:5]:
                print(f"  {symbol}: {weight:.3f} ({weight*100:.1f}%)")
            
        except Exception as e:
            print(f"Error with method {method.value}: {e}")
    
    return results

async def example_black_litterman_optimization():
    """Example: Black-Litterman optimization with investor views."""
    print("\n=== Black-Litterman Optimization Example ===")
    
    symbols = ['RELIANCE', 'TCS', 'HDFCBANK', 'INFY', 'ICICIBANK']
    
    # Create investor views
    investor_views = [
        InvestorView(
            assets=['RELIANCE'],
            view_return=0.15,  # Expect 15% return
            confidence=0.8,    # 80% confidence
            view_type='absolute',
            description='Bullish on Reliance due to new energy ventures'
        ),
        InvestorView(
            assets=['TCS', 'INFY'],
            view_return=0.05,  # TCS will outperform INFY by 5%
            confidence=0.6,    # 60% confidence
            view_type='relative',
            description='TCS expected to outperform INFY'
        ),
        InvestorView(
            assets=['HDFCBANK'],
            view_return=0.12,  # Expect 12% return
            confidence=0.7,    # 70% confidence
            view_type='absolute',
            description='Banking sector recovery expected'
        )
    ]
    
    try:
        request = PortfolioOptimizationRequest(
            symbols=symbols,
            method=OptimizationMethod.BLACK_LITTERMAN,
            investor_views=investor_views,
            lookback_days=252
        )
        
        response = portfolio_optimizer.optimize_portfolio(request)
        opt_result = response.optimization_result
        
        print(f"Expected Return: {opt_result.expected_return:.4f}")
        print(f"Volatility: {opt_result.volatility:.4f}")
        print(f"Sharpe Ratio: {opt_result.sharpe_ratio:.4f}")
        
        print("\nPortfolio Weights:")
        for symbol, weight in zip(opt_result.symbols, opt_result.weights):
            print(f"  {symbol}: {weight:.3f} ({weight*100:.1f}%)")
        
        print(f"\nViews Incorporated: {len(investor_views)}")
        for i, view in enumerate(investor_views):
            print(f"  View {i+1}: {view.description}")
        
        return response
        
    except Exception as e:
        print(f"Error in Black-Litterman optimization: {e}")
        return None

async def example_efficient_frontier():
    """Example: Generate and analyze efficient frontier."""
    print("\n=== Efficient Frontier Example ===")
    
    symbols = ['RELIANCE', 'TCS', 'HDFCBANK', 'INFY', 'ICICIBANK']
    
    try:
        from src.portfolio_optimization.mpt_optimizer import mpt_optimizer
        
        # Calculate returns and covariance
        expected_returns, covariance_matrix = mpt_optimizer.calculate_returns_covariance(
            symbols, 252
        )
        
        # Generate efficient frontier
        constraints = PortfolioConstraints()
        efficient_portfolios = mpt_optimizer.generate_efficient_frontier(
            expected_returns, covariance_matrix, constraints, n_points=20
        )
        
        print(f"Generated {len(efficient_portfolios)} efficient portfolios")
        print("\nEfficient Frontier Points:")
        print("Return\tVolatility\tSharpe")
        print("-" * 30)
        
        for portfolio in efficient_portfolios[:10]:  # Show first 10 points
            print(f"{portfolio.expected_return:.4f}\t{portfolio.volatility:.4f}\t\t{portfolio.sharpe_ratio:.4f}")
        
        # Find maximum Sharpe ratio portfolio
        max_sharpe_portfolio = max(efficient_portfolios, key=lambda p: p.sharpe_ratio)
        print(f"\nMaximum Sharpe Ratio Portfolio:")
        print(f"Return: {max_sharpe_portfolio.expected_return:.4f}")
        print(f"Volatility: {max_sharpe_portfolio.volatility:.4f}")
        print(f"Sharpe Ratio: {max_sharpe_portfolio.sharpe_ratio:.4f}")
        
        return efficient_portfolios
        
    except Exception as e:
        print(f"Error generating efficient frontier: {e}")
        return []

async def example_rebalancing_analysis():
    """Example: Portfolio rebalancing analysis."""
    print("\n=== Rebalancing Analysis Example ===")
    
    # Current portfolio weights
    current_weights = {
        'RELIANCE': 0.25,
        'TCS': 0.20,
        'HDFCBANK': 0.20,
        'INFY': 0.15,
        'ICICIBANK': 0.20
    }
    
    # Target portfolio weights (from optimization)
    target_weights = {
        'RELIANCE': 0.18,
        'TCS': 0.22,
        'HDFCBANK': 0.25,
        'INFY': 0.15,
        'ICICIBANK': 0.20
    }
    
    portfolio_value = 1000000  # ₹10L portfolio
    
    try:
        from src.portfolio_optimization.rebalancing import portfolio_rebalancer
        
        # Configure rebalancing
        config = RebalancingConfig(
            trigger_type=RebalancingTrigger.THRESHOLD_BASED,
            threshold_percent=0.05,  # 5% threshold
            transaction_cost=0.001,  # 0.1% transaction cost
            min_trade_size=0.02      # Minimum 2% trade
        )
        
        # Analyze rebalancing
        result = portfolio_rebalancer.execute_rebalancing(
            current_weights,
            target_weights,
            portfolio_value,
            config
        )
        
        if result:
            print("Rebalancing Required!")
            print(f"Trigger: {result.trigger_reason}")
            print(f"Total Turnover: {result.total_turnover:.3f}")
            print(f"Transaction Cost: ₹{result.total_transaction_cost:,.0f}")
            print(f"Expected Improvement: {result.expected_improvement:.4f}")
            
            print("\nRebalancing Actions:")
            for action in result.actions:
                print(f"  {action.symbol}: {action.current_weight:.3f} → {action.target_weight:.3f} "
                      f"({action.trade_direction} ₹{abs(action.trade_amount):,.0f})")
        else:
            print("No rebalancing required")
        
        return result
        
    except Exception as e:
        print(f"Error in rebalancing analysis: {e}")
        return None

async def example_performance_analytics():
    """Example: Portfolio performance analytics."""
    print("\n=== Performance Analytics Example ===")
    
    # Simulate portfolio returns (normally would come from actual data)
    np.random.seed(42)
    n_days = 252
    daily_returns = np.random.normal(0.0008, 0.015, n_days)  # ~20% annual return, 15% volatility
    
    try:
        # Calculate performance metrics
        performance_metrics = performance_analytics.calculate_performance_metrics(daily_returns)
        
        print("Performance Metrics:")
        print(f"Total Return: {performance_metrics.total_return:.4f} ({performance_metrics.total_return*100:.2f}%)")
        print(f"Annualized Return: {performance_metrics.annualized_return:.4f} ({performance_metrics.annualized_return*100:.2f}%)")
        print(f"Volatility: {performance_metrics.volatility:.4f} ({performance_metrics.volatility*100:.2f}%)")
        print(f"Sharpe Ratio: {performance_metrics.sharpe_ratio:.4f}")
        print(f"Sortino Ratio: {performance_metrics.sortino_ratio:.4f}")
        print(f"Maximum Drawdown: {performance_metrics.max_drawdown:.4f} ({performance_metrics.max_drawdown*100:.2f}%)")
        print(f"Calmar Ratio: {performance_metrics.calmar_ratio:.4f}")
        print(f"VaR (95%): {performance_metrics.var_95:.4f}")
        print(f"CVaR (95%): {performance_metrics.cvar_95:.4f}")
        print(f"Win Rate: {performance_metrics.win_rate:.4f} ({performance_metrics.win_rate*100:.1f}%)")
        print(f"Profit Factor: {performance_metrics.profit_factor:.4f}")
        
        # Calculate rolling metrics
        rolling_metrics = performance_analytics.calculate_rolling_metrics(
            daily_returns, window=60, metrics=['return', 'volatility', 'sharpe']
        )
        
        print(f"\nRolling Metrics (60-day window):")
        print(f"Average Rolling Return: {np.mean(rolling_metrics['return']):.4f}")
        print(f"Average Rolling Volatility: {np.mean(rolling_metrics['volatility']):.4f}")
        print(f"Average Rolling Sharpe: {np.mean(rolling_metrics['sharpe']):.4f}")
        
        return performance_metrics
        
    except Exception as e:
        print(f"Error in performance analytics: {e}")
        return None

async def example_method_comparison():
    """Example: Compare different optimization methods."""
    print("\n=== Method Comparison Example ===")
    
    symbols = ['RELIANCE', 'TCS', 'HDFCBANK', 'INFY', 'ICICIBANK']
    methods = [
        OptimizationMethod.MAX_SHARPE,
        OptimizationMethod.MIN_VARIANCE,
        OptimizationMethod.RISK_PARITY_ERC,
        OptimizationMethod.EQUAL_WEIGHT
    ]
    
    try:
        results = portfolio_optimizer.compare_optimization_methods(
            symbols, methods, lookback_days=252
        )
        
        print("Method Comparison Results:")
        print("Method\t\t\tReturn\tVolatility\tSharpe\tMax Weight")
        print("-" * 65)
        
        for method, response in results.items():
            opt_result = response.optimization_result
            max_weight = np.max(opt_result.weights)
            
            print(f"{method:<20}\t{opt_result.expected_return:.4f}\t{opt_result.volatility:.4f}\t\t{opt_result.sharpe_ratio:.4f}\t{max_weight:.3f}")
        
        # Find best method by Sharpe ratio
        best_method = max(results.items(), key=lambda x: x[1].optimization_result.sharpe_ratio)
        print(f"\nBest Method by Sharpe Ratio: {best_method[0]}")
        print(f"Sharpe Ratio: {best_method[1].optimization_result.sharpe_ratio:.4f}")
        
        return results
        
    except Exception as e:
        print(f"Error in method comparison: {e}")
        return {}

async def main():
    """Run all portfolio optimization examples."""
    print("Portfolio Optimization Examples")
    print("=" * 50)
    
    try:
        # Run examples
        await example_basic_optimization()
        await example_black_litterman_optimization()
        await example_efficient_frontier()
        await example_rebalancing_analysis()
        await example_performance_analytics()
        await example_method_comparison()
        
        print("\n" + "=" * 50)
        print("All examples completed successfully!")
        
    except Exception as e:
        print(f"Error running examples: {e}")

if __name__ == "__main__":
    asyncio.run(main())
