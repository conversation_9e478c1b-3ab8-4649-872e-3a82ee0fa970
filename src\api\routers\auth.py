"""
Authentication Router - Handles user authentication and management.
"""

from fastapi import APIRouter, HTTPException, Depends, status
from typing import List

from ..models import (
    APIResponse, LoginRequest, LoginResponse, UserCreate, UserResponse
)
from ..auth import auth_manager
from ..main import get_current_user
from ...utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter()

@router.post("/login", response_model=LoginResponse)
async def login(login_request: LoginRequest):
    """User login endpoint."""
    try:
        result = await auth_manager.login(login_request.username, login_request.password)
        return LoginResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )

@router.post("/register", response_model=APIResponse)
async def register(user_data: UserCreate, current_user: dict = Depends(get_current_user)):
    """Register new user (admin only)."""
    try:
        if not current_user.get('is_admin', False):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Admin access required"
            )
        
        user = await auth_manager.create_user(user_data.dict())
        
        return APIResponse(
            success=True,
            message="User created successfully",
            data=user
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Registration error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed"
        )

@router.get("/me", response_model=APIResponse)
async def get_current_user_info(current_user: dict = Depends(get_current_user)):
    """Get current user information."""
    return APIResponse(
        success=True,
        message="User information retrieved",
        data=current_user
    )

@router.get("/users", response_model=APIResponse)
async def get_all_users(current_user: dict = Depends(get_current_user)):
    """Get all users (admin only)."""
    try:
        if not current_user.get('is_admin', False):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Admin access required"
            )
        
        users = await auth_manager.get_all_users()
        
        return APIResponse(
            success=True,
            message="Users retrieved successfully",
            data={"users": users}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting users: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get users"
        )

@router.get("/users/{user_id}", response_model=APIResponse)
async def get_user(user_id: int, current_user: dict = Depends(get_current_user)):
    """Get user by ID."""
    try:
        # Users can only access their own info unless they're admin
        if current_user['id'] != user_id and not current_user.get('is_admin', False):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        user = await auth_manager.get_user_by_id(user_id)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        return APIResponse(
            success=True,
            message="User retrieved successfully",
            data=user
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting user: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user"
        )

@router.put("/users/{user_id}", response_model=APIResponse)
async def update_user(
    user_id: int, 
    update_data: dict, 
    current_user: dict = Depends(get_current_user)
):
    """Update user information."""
    try:
        # Users can only update their own info unless they're admin
        if current_user['id'] != user_id and not current_user.get('is_admin', False):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        # Non-admin users cannot change admin status
        if not current_user.get('is_admin', False) and 'is_admin' in update_data:
            del update_data['is_admin']
        
        user = await auth_manager.update_user(user_id, update_data)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        return APIResponse(
            success=True,
            message="User updated successfully",
            data=user
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating user: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update user"
        )

@router.delete("/users/{user_id}", response_model=APIResponse)
async def delete_user(user_id: int, current_user: dict = Depends(get_current_user)):
    """Delete user (admin only)."""
    try:
        if not current_user.get('is_admin', False):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Admin access required"
            )
        
        # Prevent self-deletion
        if current_user['id'] == user_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot delete your own account"
            )
        
        success = await auth_manager.delete_user(user_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        return APIResponse(
            success=True,
            message="User deleted successfully",
            data={"user_id": user_id}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting user: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete user"
        )

@router.post("/change-password", response_model=APIResponse)
async def change_password(
    password_data: dict, 
    current_user: dict = Depends(get_current_user)
):
    """Change user password."""
    try:
        current_password = password_data.get('current_password')
        new_password = password_data.get('new_password')
        
        if not current_password or not new_password:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Current password and new password are required"
            )
        
        # Verify current password
        user = await auth_manager.authenticate_user(current_user['username'], current_password)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Current password is incorrect"
            )
        
        # Update password
        await auth_manager.update_user(current_user['id'], {'password': new_password})
        
        return APIResponse(
            success=True,
            message="Password changed successfully",
            data={}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error changing password: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to change password"
        )
