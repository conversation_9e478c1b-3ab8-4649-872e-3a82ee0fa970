"""
Data monitoring and alerting system for the stock data pipeline.
Monitors data quality, freshness, and anomalies.
"""

from datetime import datetime, date, timedelta
from typing import Dict, List, Any, Optional
import pandas as pd
from sqlalchemy import func, and_

from .database import db_manager
from .models import StockMetadata, DailyPrice, StrategySignal
from .crud import StockCRUD, PriceCRUD
from ..utils.logger import get_logger
from ..utils.config import config

logger = get_logger(__name__)

class DataQualityMonitor:
    """Monitors data quality and generates alerts."""
    
    def __init__(self):
        self.quality_thresholds = {
            'missing_data_threshold': 0.05,  # 5% missing data is acceptable
            'stale_data_hours': 24,  # Data older than 24 hours is stale
            'price_change_threshold': 0.20,  # 20% price change is suspicious
            'volume_spike_threshold': 10.0,  # 10x volume spike is suspicious
        }
    
    def check_data_freshness(self) -> Dict[str, Any]:
        """Check if data is up to date."""
        try:
            with db_manager.get_session() as db:
                # Get latest data dates for all active stocks
                latest_dates = db.query(
                    StockMetadata.symbol,
                    func.max(DailyPrice.date).label('latest_date')
                ).join(DailyPrice).filter(
                    StockMetadata.is_active == True
                ).group_by(StockMetadata.symbol).all()
                
                today = date.today()
                stale_stocks = []
                
                for symbol, latest_date in latest_dates:
                    if latest_date:
                        days_old = (today - latest_date).days
                        if days_old > 1:  # More than 1 day old
                            stale_stocks.append({
                                'symbol': symbol,
                                'latest_date': latest_date.isoformat(),
                                'days_old': days_old
                            })
                
                return {
                    'total_stocks_checked': len(latest_dates),
                    'stale_stocks_count': len(stale_stocks),
                    'stale_stocks': stale_stocks,
                    'freshness_percentage': ((len(latest_dates) - len(stale_stocks)) / len(latest_dates) * 100) if latest_dates else 0
                }
                
        except Exception as e:
            logger.error(f"Error checking data freshness: {e}")
            return {'error': str(e)}
    
    def check_missing_data(self, days_back: int = 30) -> Dict[str, Any]:
        """Check for missing data in recent days."""
        try:
            with db_manager.get_session() as db:
                end_date = date.today()
                start_date = end_date - timedelta(days=days_back)
                
                # Get all active Nifty 50 stocks
                nifty50_stocks = StockCRUD.get_nifty50_stocks(db)
                
                missing_data_report = []
                
                for stock in nifty50_stocks:
                    missing_dates = PriceCRUD.get_missing_dates(
                        db, stock.id, start_date, end_date
                    )
                    
                    if missing_dates:
                        missing_data_report.append({
                            'symbol': stock.symbol,
                            'missing_dates_count': len(missing_dates),
                            'missing_dates': [d.isoformat() for d in missing_dates[:5]],  # Show first 5
                            'missing_percentage': len(missing_dates) / days_back * 100
                        })
                
                return {
                    'period_days': days_back,
                    'stocks_with_missing_data': len(missing_data_report),
                    'missing_data_details': missing_data_report
                }
                
        except Exception as e:
            logger.error(f"Error checking missing data: {e}")
            return {'error': str(e)}
    
    def check_price_anomalies(self, days_back: int = 7) -> Dict[str, Any]:
        """Check for price anomalies and suspicious movements."""
        try:
            with db_manager.get_session() as db:
                end_date = date.today()
                start_date = end_date - timedelta(days=days_back)
                
                # Query for recent price data with large movements
                anomalies = db.query(
                    StockMetadata.symbol,
                    DailyPrice.date,
                    DailyPrice.open_price,
                    DailyPrice.close_price,
                    DailyPrice.volume,
                    DailyPrice.volume_ratio
                ).join(StockMetadata).filter(
                    and_(
                        DailyPrice.date >= start_date,
                        StockMetadata.is_active == True
                    )
                ).all()
                
                suspicious_movements = []
                
                for symbol, date_val, open_price, close_price, volume, volume_ratio in anomalies:
                    # Check for large price movements
                    if open_price and close_price:
                        price_change = abs(close_price - open_price) / open_price
                        
                        if price_change > self.quality_thresholds['price_change_threshold']:
                            suspicious_movements.append({
                                'symbol': symbol,
                                'date': date_val.isoformat(),
                                'type': 'large_price_movement',
                                'price_change_percent': round(price_change * 100, 2),
                                'open_price': float(open_price),
                                'close_price': float(close_price)
                            })
                    
                    # Check for volume spikes
                    if volume_ratio and volume_ratio > self.quality_thresholds['volume_spike_threshold']:
                        suspicious_movements.append({
                            'symbol': symbol,
                            'date': date_val.isoformat(),
                            'type': 'volume_spike',
                            'volume_ratio': float(volume_ratio),
                            'volume': volume
                        })
                
                return {
                    'period_days': days_back,
                    'anomalies_found': len(suspicious_movements),
                    'anomalies': suspicious_movements
                }
                
        except Exception as e:
            logger.error(f"Error checking price anomalies: {e}")
            return {'error': str(e)}
    
    def generate_daily_report(self) -> Dict[str, Any]:
        """Generate comprehensive daily data quality report."""
        logger.info("Generating daily data quality report")
        
        try:
            report = {
                'report_date': date.today().isoformat(),
                'report_time': datetime.now().isoformat(),
                'freshness_check': self.check_data_freshness(),
                'missing_data_check': self.check_missing_data(),
                'anomaly_check': self.check_price_anomalies(),
            }
            
            # Calculate overall health score
            freshness_score = report['freshness_check'].get('freshness_percentage', 0)
            missing_data_count = len(report['missing_data_check'].get('missing_data_details', []))
            anomaly_count = report['anomaly_check'].get('anomalies_found', 0)
            
            # Simple scoring algorithm
            health_score = freshness_score
            if missing_data_count > 5:
                health_score -= 10
            if anomaly_count > 10:
                health_score -= 15
            
            report['overall_health_score'] = max(0, min(100, health_score))
            report['health_status'] = self._get_health_status(health_score)
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating daily report: {e}")
            return {'error': str(e)}
    
    def _get_health_status(self, score: float) -> str:
        """Get health status based on score."""
        if score >= 90:
            return 'excellent'
        elif score >= 75:
            return 'good'
        elif score >= 60:
            return 'fair'
        elif score >= 40:
            return 'poor'
        else:
            return 'critical'

class DataAlertManager:
    """Manages data quality alerts and notifications."""
    
    def __init__(self):
        self.alert_config = config.get('alerts', {})
        self.monitor = DataQualityMonitor()
    
    def check_alert_conditions(self) -> List[Dict[str, Any]]:
        """Check conditions that should trigger alerts."""
        alerts = []
        
        try:
            # Check data freshness
            freshness_report = self.monitor.check_data_freshness()
            if freshness_report.get('stale_stocks_count', 0) > 5:
                alerts.append({
                    'type': 'stale_data',
                    'severity': 'warning',
                    'message': f"{freshness_report['stale_stocks_count']} stocks have stale data",
                    'details': freshness_report
                })
            
            # Check missing data
            missing_report = self.monitor.check_missing_data()
            if missing_report.get('stocks_with_missing_data', 0) > 10:
                alerts.append({
                    'type': 'missing_data',
                    'severity': 'error',
                    'message': f"{missing_report['stocks_with_missing_data']} stocks have missing data",
                    'details': missing_report
                })
            
            # Check anomalies
            anomaly_report = self.monitor.check_price_anomalies()
            if anomaly_report.get('anomalies_found', 0) > 20:
                alerts.append({
                    'type': 'price_anomalies',
                    'severity': 'warning',
                    'message': f"{anomaly_report['anomalies_found']} price anomalies detected",
                    'details': anomaly_report
                })
            
            return alerts
            
        except Exception as e:
            logger.error(f"Error checking alert conditions: {e}")
            return [{
                'type': 'system_error',
                'severity': 'critical',
                'message': f"Error in alert system: {str(e)}"
            }]
    
    async def send_alerts(self, alerts: List[Dict[str, Any]]) -> bool:
        """Send alerts via configured channels."""
        if not alerts:
            return True
        
        try:
            # Log all alerts
            for alert in alerts:
                logger.warning(f"DATA ALERT: {alert['type']} - {alert['message']}")
            
            # Send email alerts if configured
            if self.alert_config.get('email', {}).get('enabled', False):
                await self._send_email_alerts(alerts)
            
            # Send SMS alerts if configured
            if self.alert_config.get('sms', {}).get('enabled', False):
                await self._send_sms_alerts(alerts)
            
            return True
            
        except Exception as e:
            logger.error(f"Error sending alerts: {e}")
            return False
    
    async def _send_email_alerts(self, alerts: List[Dict[str, Any]]):
        """Send email alerts."""
        # Implementation would depend on email service
        # This is a placeholder for email functionality
        logger.info(f"Would send {len(alerts)} email alerts")
    
    async def _send_sms_alerts(self, alerts: List[Dict[str, Any]]):
        """Send SMS alerts."""
        # Implementation would depend on SMS service (Twilio)
        # This is a placeholder for SMS functionality
        logger.info(f"Would send {len(alerts)} SMS alerts")
    
    async def run_monitoring_cycle(self):
        """Run a complete monitoring cycle."""
        logger.info("Starting data monitoring cycle")
        
        try:
            # Check for alert conditions
            alerts = self.check_alert_conditions()
            
            # Send alerts if any
            if alerts:
                await self.send_alerts(alerts)
                logger.info(f"Sent {len(alerts)} alerts")
            else:
                logger.info("No alerts triggered")
            
            # Generate daily report
            report = self.monitor.generate_daily_report()
            logger.info(f"Data health score: {report.get('overall_health_score', 'N/A')}")
            
            return {
                'alerts_sent': len(alerts),
                'health_report': report,
                'status': 'completed'
            }
            
        except Exception as e:
            logger.error(f"Error in monitoring cycle: {e}")
            return {'status': 'failed', 'error': str(e)}

# Global instances
data_monitor = DataQualityMonitor()
alert_manager = DataAlertManager()

async def run_monitoring():
    """Run monitoring - can be called from scheduler."""
    return await alert_manager.run_monitoring_cycle()

if __name__ == "__main__":
    import asyncio
    asyncio.run(run_monitoring())
