"""
Multi-Agent RL Manager for Trading Strategies.
Manages multiple PPO agents, one for each trading strategy.
"""

import asyncio
from datetime import datetime, date, timedelta
from typing import Dict, List, Any, Optional, Tuple
import numpy as np
import json
from concurrent.futures import ThreadPoolExecutor
import threading

from .ppo_optimizer import PPOOptimizer
from ..strategies.strategy_registry import strategy_registry
from ..data.database import db_manager
from ..data.crud import StockCRUD, RLModelCRUD
from ..utils.logger import get_logger
from ..utils.config import config

logger = get_logger(__name__)

class MultiAgentRLManager:
    """Manages multiple RL agents for different trading strategies."""
    
    def __init__(self):
        """Initialize multi-agent RL manager."""
        self.agents: Dict[str, PPOOptimizer] = {}
        self.parameter_bounds = self._get_strategy_parameter_bounds()
        self.training_symbols = self._get_training_symbols()
        
        # Training configuration
        self.config = config.get_rl_config()
        self.training_enabled = self.config.get('training_enabled', True)
        self.adaptation_frequency = self.config.get('adaptation_frequency', 'daily')  # daily, weekly
        self.max_concurrent_training = self.config.get('max_concurrent_training', 2)
        
        # Thread pool for concurrent training
        self.executor = ThreadPoolExecutor(max_workers=self.max_concurrent_training)
        
        # Initialize agents
        self._initialize_agents()
        
        logger.info(f"Initialized multi-agent RL manager with {len(self.agents)} agents")
    
    def _get_strategy_parameter_bounds(self) -> Dict[str, Dict[str, Tuple[float, float]]]:
        """Get parameter bounds for each strategy."""
        return {
            'adaptive_rsi': {
                'rsi_period': (10, 21),
                'base_oversold': (20, 35),
                'base_overbought': (65, 80),
                'volatility_multiplier': (0.1, 1.0),
                'min_volume_ratio': (1.0, 2.0),
                'min_signal_strength': (0.4, 0.8)
            },
            'enhanced_pivot': {
                'volume_multiplier': (1.2, 2.5),
                'min_breakout_volume': (1.5, 3.0),
                'pivot_strength_threshold': (0.4, 0.8),
                'breakout_confirmation_bars': (1, 5),
                'support_resistance_levels': (2, 5),
                'min_signal_strength': (0.4, 0.8)
            },
            'ml_enhanced_macd': {
                'fast_period': (8, 16),
                'slow_period': (20, 30),
                'signal_period': (6, 12),
                'min_ml_confidence': (0.5, 0.8),
                'feature_window': (15, 25),
                'trend_strength_threshold': (0.3, 0.7),
                'min_signal_strength': (0.4, 0.8)
            },
            'dynamic_moving_averages': {
                'short_period': (40, 60),
                'long_period': (180, 220),
                'adx_threshold': (20, 30),
                'trending_threshold': (25, 35),
                'ranging_threshold': (15, 25),
                'cross_confirmation_bars': (1, 4),
                'min_signal_strength': (0.5, 0.8)
            },
            'volume_confirmed_breakout': {
                'lookback_period': (15, 25),
                'volume_multiplier': (1.5, 3.0),
                'min_breakout_volume': (1.2, 2.0),
                'confirmation_bars': (2, 5),
                'min_breakout_percentage': (0.3, 1.0),
                'support_resistance_strength': (2, 5),
                'min_signal_strength': (0.5, 0.8)
            }
        }
    
    def _get_training_symbols(self) -> List[str]:
        """Get symbols for training."""
        try:
            with db_manager.get_session() as db:
                # Get top 20 Nifty 50 stocks by market cap for training
                nifty50_stocks = StockCRUD.get_nifty50_stocks(db)
                return [stock.symbol for stock in nifty50_stocks[:20]]
        except Exception as e:
            logger.error(f"Error getting training symbols: {e}")
            return ['RELIANCE', 'TCS', 'INFY', 'HDFCBANK', 'ICICIBANK']  # Fallback
    
    def _initialize_agents(self):
        """Initialize RL agents for each strategy."""
        for strategy_name, bounds in self.parameter_bounds.items():
            try:
                agent = PPOOptimizer(strategy_name, bounds)
                
                # Try to load existing model
                if agent.load_model():
                    logger.info(f"Loaded existing model for {strategy_name}")
                else:
                    logger.info(f"Initialized new model for {strategy_name}")
                
                self.agents[strategy_name] = agent
                
            except Exception as e:
                logger.error(f"Error initializing agent for {strategy_name}: {e}")
    
    async def train_all_agents(self, num_iterations: int = 50, episodes_per_iteration: int = 3):
        """Train all RL agents concurrently.
        
        Args:
            num_iterations: Number of training iterations
            episodes_per_iteration: Episodes per iteration
        """
        logger.info(f"Starting training for all {len(self.agents)} agents")
        
        try:
            # Create training tasks
            training_tasks = []
            
            for strategy_name, agent in self.agents.items():
                task = asyncio.create_task(
                    self._train_agent_async(agent, strategy_name, num_iterations, episodes_per_iteration)
                )
                training_tasks.append(task)
            
            # Wait for all training to complete
            results = await asyncio.gather(*training_tasks, return_exceptions=True)
            
            # Log results
            successful_training = 0
            for i, result in enumerate(results):
                strategy_name = list(self.agents.keys())[i]
                if isinstance(result, Exception):
                    logger.error(f"Training failed for {strategy_name}: {result}")
                else:
                    successful_training += 1
                    logger.info(f"Training completed for {strategy_name}")
            
            logger.info(f"Training completed: {successful_training}/{len(self.agents)} agents successful")
            
            # Store training summary
            await self._store_training_summary(successful_training, len(self.agents))
            
        except Exception as e:
            logger.error(f"Error in multi-agent training: {e}")
    
    async def _train_agent_async(
        self, 
        agent: PPOOptimizer, 
        strategy_name: str, 
        num_iterations: int, 
        episodes_per_iteration: int
    ):
        """Train a single agent asynchronously.
        
        Args:
            agent: PPO agent to train
            strategy_name: Strategy name
            num_iterations: Number of iterations
            episodes_per_iteration: Episodes per iteration
        """
        loop = asyncio.get_event_loop()
        
        # Run training in thread pool to avoid blocking
        await loop.run_in_executor(
            self.executor,
            agent.train,
            self.training_symbols,
            num_iterations,
            episodes_per_iteration
        )
    
    def get_optimal_parameters_for_all(self, symbol: str) -> Dict[str, Dict[str, Any]]:
        """Get optimal parameters for all strategies for a given symbol.
        
        Args:
            symbol: Stock symbol
            
        Returns:
            Dictionary mapping strategy names to optimal parameters
        """
        logger.info(f"Getting optimal parameters for all strategies on {symbol}")
        
        optimal_params = {}
        
        for strategy_name, agent in self.agents.items():
            try:
                params = agent.get_optimal_parameters(symbol)
                optimal_params[strategy_name] = params
                
            except Exception as e:
                logger.error(f"Error getting optimal parameters for {strategy_name}: {e}")
                optimal_params[strategy_name] = {}
        
        return optimal_params
    
    def adapt_parameters_realtime(self, symbol: str, market_conditions: Dict[str, float]) -> Dict[str, Dict[str, Any]]:
        """Adapt parameters in real-time based on market conditions.
        
        Args:
            symbol: Stock symbol
            market_conditions: Current market conditions
            
        Returns:
            Adapted parameters for all strategies
        """
        logger.info(f"Adapting parameters for {symbol} based on market conditions")
        
        adapted_params = {}
        
        for strategy_name, agent in self.agents.items():
            try:
                # Get base optimal parameters
                base_params = agent.get_optimal_parameters(symbol)
                
                # Apply market condition adjustments
                adjusted_params = self._adjust_for_market_conditions(
                    base_params, market_conditions, strategy_name
                )
                
                adapted_params[strategy_name] = adjusted_params
                
            except Exception as e:
                logger.error(f"Error adapting parameters for {strategy_name}: {e}")
                adapted_params[strategy_name] = {}
        
        return adapted_params
    
    def _adjust_for_market_conditions(
        self, 
        base_params: Dict[str, Any], 
        market_conditions: Dict[str, float],
        strategy_name: str
    ) -> Dict[str, Any]:
        """Adjust parameters based on market conditions.
        
        Args:
            base_params: Base parameters from RL agent
            market_conditions: Current market conditions
            strategy_name: Strategy name
            
        Returns:
            Adjusted parameters
        """
        adjusted_params = base_params.copy()
        
        try:
            volatility = market_conditions.get('volatility', 0.02)
            trend_strength = market_conditions.get('trend_strength', 0.5)
            volume_ratio = market_conditions.get('volume_ratio', 1.0)
            
            # Strategy-specific adjustments
            if strategy_name == 'adaptive_rsi':
                # Increase signal strength threshold in high volatility
                if volatility > 0.03:
                    adjusted_params['min_signal_strength'] = min(
                        adjusted_params.get('min_signal_strength', 0.6) + 0.1, 0.8
                    )
                
                # Adjust RSI thresholds based on trend
                if trend_strength > 0.7:  # Strong trend
                    adjusted_params['base_oversold'] = max(
                        adjusted_params.get('base_oversold', 30) - 5, 20
                    )
                    adjusted_params['base_overbought'] = min(
                        adjusted_params.get('base_overbought', 70) + 5, 80
                    )
            
            elif strategy_name == 'volume_confirmed_breakout':
                # Adjust volume requirements based on current volume
                if volume_ratio > 2.0:  # High volume environment
                    adjusted_params['volume_multiplier'] = max(
                        adjusted_params.get('volume_multiplier', 2.0) - 0.2, 1.5
                    )
                elif volume_ratio < 0.8:  # Low volume environment
                    adjusted_params['volume_multiplier'] = min(
                        adjusted_params.get('volume_multiplier', 2.0) + 0.3, 3.0
                    )
            
            elif strategy_name == 'dynamic_moving_averages':
                # Adjust confirmation bars based on volatility
                if volatility > 0.03:  # High volatility
                    adjusted_params['cross_confirmation_bars'] = min(
                        adjusted_params.get('cross_confirmation_bars', 2) + 1, 4
                    )
            
            return adjusted_params
            
        except Exception as e:
            logger.error(f"Error adjusting parameters: {e}")
            return base_params
    
    def evaluate_agent_performance(self, strategy_name: str, symbol: str) -> Dict[str, Any]:
        """Evaluate performance of a specific agent.
        
        Args:
            strategy_name: Strategy name
            symbol: Stock symbol to evaluate on
            
        Returns:
            Performance evaluation results
        """
        try:
            agent = self.agents.get(strategy_name)
            if not agent:
                return {'error': f'Agent {strategy_name} not found'}
            
            # Get optimal parameters
            optimal_params = agent.get_optimal_parameters(symbol)
            
            # Run backtest with optimal parameters
            from ..backtesting.backtest_runner import backtest_runner
            
            start_date = date.today() - timedelta(days=180)  # 6 months
            end_date = date.today() - timedelta(days=1)
            
            result = backtest_runner.engine.run_single_strategy_backtest(
                strategy_name, symbol, start_date, end_date, optimal_params
            )
            
            if 'error' not in result:
                # Calculate reward
                reward = agent.reward_function.calculate_reward(result)
                
                return {
                    'strategy_name': strategy_name,
                    'symbol': symbol,
                    'optimal_parameters': optimal_params,
                    'backtest_result': result,
                    'rl_reward': reward,
                    'evaluation_date': datetime.now().isoformat()
                }
            else:
                return {'error': result['error']}
                
        except Exception as e:
            logger.error(f"Error evaluating agent performance: {e}")
            return {'error': str(e)}
    
    async def continuous_adaptation(self):
        """Run continuous parameter adaptation based on market conditions."""
        logger.info("Starting continuous parameter adaptation")
        
        while True:
            try:
                # Get current market conditions for key symbols
                for symbol in self.training_symbols[:5]:  # Top 5 symbols
                    market_conditions = self._get_current_market_conditions(symbol)
                    
                    # Adapt parameters
                    adapted_params = self.adapt_parameters_realtime(symbol, market_conditions)
                    
                    # Store adapted parameters
                    await self._store_adapted_parameters(symbol, adapted_params)
                
                # Wait based on adaptation frequency
                if self.adaptation_frequency == 'daily':
                    await asyncio.sleep(24 * 3600)  # 24 hours
                elif self.adaptation_frequency == 'hourly':
                    await asyncio.sleep(3600)  # 1 hour
                else:
                    await asyncio.sleep(6 * 3600)  # 6 hours (default)
                
            except Exception as e:
                logger.error(f"Error in continuous adaptation: {e}")
                await asyncio.sleep(3600)  # Wait 1 hour before retrying
    
    def _get_current_market_conditions(self, symbol: str) -> Dict[str, float]:
        """Get current market conditions for a symbol.
        
        Args:
            symbol: Stock symbol
            
        Returns:
            Dictionary of market conditions
        """
        try:
            # Use the same state extractor as the agents
            state_extractor = self.agents[list(self.agents.keys())[0]].state_extractor
            state_features = state_extractor.extract_state(symbol)
            
            # Map features to named conditions
            return {
                'volatility': state_features[0] if len(state_features) > 0 else 0.02,
                'trend_strength': state_features[1] if len(state_features) > 1 else 0.5,
                'volume_ratio': state_features[2] if len(state_features) > 2 else 1.0,
                'rsi_level': state_features[3] if len(state_features) > 3 else 0.5,
                'market_regime': state_features[4] if len(state_features) > 4 else 0.5
            }
            
        except Exception as e:
            logger.error(f"Error getting market conditions for {symbol}: {e}")
            return {
                'volatility': 0.02,
                'trend_strength': 0.5,
                'volume_ratio': 1.0,
                'rsi_level': 0.5,
                'market_regime': 0.5
            }
    
    async def _store_training_summary(self, successful_agents: int, total_agents: int):
        """Store training summary in database.
        
        Args:
            successful_agents: Number of successfully trained agents
            total_agents: Total number of agents
        """
        try:
            with db_manager.get_session() as db:
                from ..data.crud import RLTrainingHistoryCRUD
                
                training_data = {
                    'training_date': datetime.now().date(),
                    'strategy_name': 'multi_agent_training',
                    'training_iterations': 50,  # Default
                    'avg_reward': 0.0,  # Would calculate from individual agents
                    'training_time_minutes': 0,  # Would track actual time
                    'parameters_optimized': json.dumps({
                        'successful_agents': successful_agents,
                        'total_agents': total_agents,
                        'training_symbols': self.training_symbols
                    })
                }
                
                RLTrainingHistoryCRUD.create_training_record(db, training_data)
                logger.info("Training summary stored in database")
                
        except Exception as e:
            logger.error(f"Error storing training summary: {e}")
    
    async def _store_adapted_parameters(self, symbol: str, adapted_params: Dict[str, Dict[str, Any]]):
        """Store adapted parameters in database.
        
        Args:
            symbol: Stock symbol
            adapted_params: Adapted parameters for all strategies
        """
        try:
            # Store in a simple format for now
            # In production, you might want a dedicated table for adapted parameters
            logger.debug(f"Adapted parameters for {symbol}: {adapted_params}")
            
        except Exception as e:
            logger.error(f"Error storing adapted parameters: {e}")
    
    def get_agent_status(self) -> Dict[str, Any]:
        """Get status of all RL agents.
        
        Returns:
            Status information for all agents
        """
        status = {
            'total_agents': len(self.agents),
            'training_enabled': self.training_enabled,
            'adaptation_frequency': self.adaptation_frequency,
            'training_symbols': self.training_symbols,
            'agents': {}
        }
        
        for strategy_name, agent in self.agents.items():
            status['agents'][strategy_name] = {
                'parameter_bounds': agent.parameter_bounds,
                'training_history_length': len(agent.training_history),
                'last_training': agent.training_history[-1]['timestamp'] if agent.training_history else None,
                'avg_recent_reward': np.mean([h['avg_reward'] for h in agent.training_history[-5:]]) if len(agent.training_history) >= 5 else 0
            }
        
        return status

# Global multi-agent manager instance
multi_agent_manager = MultiAgentRLManager()

async def train_all_rl_agents():
    """Train all RL agents - main entry point."""
    return await multi_agent_manager.train_all_agents()

def get_optimal_parameters(symbol: str) -> Dict[str, Dict[str, Any]]:
    """Get optimal parameters for all strategies."""
    return multi_agent_manager.get_optimal_parameters_for_all(symbol)

if __name__ == "__main__":
    # Train all agents
    asyncio.run(train_all_rl_agents())
