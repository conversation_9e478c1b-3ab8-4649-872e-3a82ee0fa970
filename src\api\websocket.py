"""
WebSocket Manager for Real-time Communication.
Handles WebSocket connections, subscriptions, and real-time data streaming.
"""

import asyncio
import json
from typing import Dict, List, Set, Any, Optional
from fastapi import WebSocket, WebSocketDisconnect, APIRouter, Depends, HTTPException
from datetime import datetime
import uuid

from .auth import auth_manager
from .models import WebSocketMessage, SubscriptionRequest
from ..utils.logger import get_logger

logger = get_logger(__name__)

class ConnectionManager:
    """Manages WebSocket connections and subscriptions."""
    
    def __init__(self):
        """Initialize connection manager."""
        self.active_connections: Dict[str, WebSocket] = {}
        self.user_connections: Dict[int, str] = {}  # user_id -> connection_id
        self.subscriptions: Dict[str, Set[str]] = {}  # connection_id -> channels
        self.channel_subscribers: Dict[str, Set[str]] = {}  # channel -> connection_ids
        
        # Available channels
        self.available_channels = {
            'system_status',
            'signals',
            'portfolio',
            'alerts',
            'backtesting',
            'rl_training',
            'market_data'
        }
    
    async def connect(self, websocket: WebSocket, user_id: int) -> str:
        """Accept WebSocket connection and return connection ID."""
        await websocket.accept()
        
        connection_id = str(uuid.uuid4())
        self.active_connections[connection_id] = websocket
        self.user_connections[user_id] = connection_id
        self.subscriptions[connection_id] = set()
        
        logger.info(f"WebSocket connection established: {connection_id} for user {user_id}")
        
        # Send welcome message
        await self.send_personal_message({
            'type': 'connection_established',
            'data': {
                'connection_id': connection_id,
                'available_channels': list(self.available_channels)
            }
        }, connection_id)
        
        return connection_id
    
    def disconnect(self, connection_id: str):
        """Disconnect WebSocket connection."""
        if connection_id in self.active_connections:
            # Remove from all channel subscriptions
            for channel in self.subscriptions.get(connection_id, set()):
                if channel in self.channel_subscribers:
                    self.channel_subscribers[channel].discard(connection_id)
            
            # Clean up
            del self.active_connections[connection_id]
            if connection_id in self.subscriptions:
                del self.subscriptions[connection_id]
            
            # Remove user connection mapping
            user_id_to_remove = None
            for user_id, conn_id in self.user_connections.items():
                if conn_id == connection_id:
                    user_id_to_remove = user_id
                    break
            
            if user_id_to_remove:
                del self.user_connections[user_id_to_remove]
            
            logger.info(f"WebSocket connection closed: {connection_id}")
    
    async def send_personal_message(self, message: Dict[str, Any], connection_id: str):
        """Send message to specific connection."""
        if connection_id in self.active_connections:
            try:
                websocket = self.active_connections[connection_id]
                await websocket.send_text(json.dumps(message, default=str))
            except Exception as e:
                logger.error(f"Error sending message to {connection_id}: {e}")
                self.disconnect(connection_id)
    
    async def broadcast(self, message: Dict[str, Any], channel: str = None):
        """Broadcast message to all connections or specific channel."""
        if channel:
            # Send to channel subscribers only
            subscribers = self.channel_subscribers.get(channel, set())
            for connection_id in subscribers.copy():  # Copy to avoid modification during iteration
                await self.send_personal_message(message, connection_id)
        else:
            # Send to all connections
            for connection_id in list(self.active_connections.keys()):
                await self.send_personal_message(message, connection_id)
    
    async def subscribe_to_channel(self, connection_id: str, channel: str) -> bool:
        """Subscribe connection to a channel."""
        if channel not in self.available_channels:
            return False
        
        if connection_id not in self.active_connections:
            return False
        
        # Add to subscriptions
        self.subscriptions[connection_id].add(channel)
        
        # Add to channel subscribers
        if channel not in self.channel_subscribers:
            self.channel_subscribers[channel] = set()
        self.channel_subscribers[channel].add(connection_id)
        
        logger.info(f"Connection {connection_id} subscribed to channel: {channel}")
        
        # Send confirmation
        await self.send_personal_message({
            'type': 'subscription_confirmed',
            'data': {
                'channel': channel,
                'subscribed': True
            }
        }, connection_id)
        
        return True
    
    async def unsubscribe_from_channel(self, connection_id: str, channel: str) -> bool:
        """Unsubscribe connection from a channel."""
        if connection_id not in self.subscriptions:
            return False
        
        # Remove from subscriptions
        self.subscriptions[connection_id].discard(channel)
        
        # Remove from channel subscribers
        if channel in self.channel_subscribers:
            self.channel_subscribers[channel].discard(connection_id)
        
        logger.info(f"Connection {connection_id} unsubscribed from channel: {channel}")
        
        # Send confirmation
        await self.send_personal_message({
            'type': 'subscription_confirmed',
            'data': {
                'channel': channel,
                'subscribed': False
            }
        }, connection_id)
        
        return True
    
    def get_connection_info(self) -> Dict[str, Any]:
        """Get connection statistics."""
        return {
            'total_connections': len(self.active_connections),
            'active_users': len(self.user_connections),
            'channel_subscriptions': {
                channel: len(subscribers) 
                for channel, subscribers in self.channel_subscribers.items()
            },
            'available_channels': list(self.available_channels)
        }

class WebSocketManager:
    """Main WebSocket manager."""
    
    def __init__(self):
        """Initialize WebSocket manager."""
        self.connection_manager = ConnectionManager()
        self.router = APIRouter()
        self._setup_routes()
        self._running = False
    
    def _setup_routes(self):
        """Setup WebSocket routes."""
        
        @self.router.websocket("/connect")
        async def websocket_endpoint(websocket: WebSocket, token: str):
            """Main WebSocket endpoint."""
            try:
                # Verify authentication
                user = await auth_manager.verify_token(token)
                if not user:
                    await websocket.close(code=4001, reason="Authentication failed")
                    return
                
                # Connect user
                connection_id = await self.connection_manager.connect(websocket, user['id'])
                
                try:
                    while True:
                        # Receive message from client
                        data = await websocket.receive_text()
                        message = json.loads(data)
                        
                        # Handle message
                        await self._handle_client_message(connection_id, message)
                        
                except WebSocketDisconnect:
                    logger.info(f"WebSocket disconnected: {connection_id}")
                except Exception as e:
                    logger.error(f"WebSocket error: {e}")
                finally:
                    self.connection_manager.disconnect(connection_id)
                    
            except Exception as e:
                logger.error(f"WebSocket connection error: {e}")
                await websocket.close(code=4000, reason="Connection error")
    
    async def _handle_client_message(self, connection_id: str, message: Dict[str, Any]):
        """Handle message from client."""
        try:
            message_type = message.get('type')
            data = message.get('data', {})
            
            if message_type == 'subscribe':
                # Subscribe to channels
                channels = data.get('channels', [])
                for channel in channels:
                    await self.connection_manager.subscribe_to_channel(connection_id, channel)
            
            elif message_type == 'unsubscribe':
                # Unsubscribe from channels
                channels = data.get('channels', [])
                for channel in channels:
                    await self.connection_manager.unsubscribe_from_channel(connection_id, channel)
            
            elif message_type == 'ping':
                # Respond to ping
                await self.connection_manager.send_personal_message({
                    'type': 'pong',
                    'data': {'timestamp': datetime.now().isoformat()}
                }, connection_id)
            
            elif message_type == 'get_status':
                # Send connection status
                status = self.connection_manager.get_connection_info()
                await self.connection_manager.send_personal_message({
                    'type': 'status',
                    'data': status
                }, connection_id)
            
            else:
                logger.warning(f"Unknown message type: {message_type}")
                
        except Exception as e:
            logger.error(f"Error handling client message: {e}")
    
    async def initialize(self):
        """Initialize WebSocket manager."""
        try:
            logger.info("WebSocket manager initialized")
        except Exception as e:
            logger.error(f"Error initializing WebSocket manager: {e}")
            raise
    
    async def start(self):
        """Start WebSocket manager."""
        if self._running:
            return
        
        self._running = True
        logger.info("WebSocket manager started")
        
        # Start background tasks
        asyncio.create_task(self._heartbeat_task())
        asyncio.create_task(self._cleanup_task())
    
    async def cleanup(self):
        """Cleanup WebSocket manager."""
        self._running = False
        
        # Disconnect all connections
        for connection_id in list(self.connection_manager.active_connections.keys()):
            self.connection_manager.disconnect(connection_id)
        
        logger.info("WebSocket manager cleaned up")
    
    async def _heartbeat_task(self):
        """Send periodic heartbeat to all connections."""
        while self._running:
            try:
                await self.connection_manager.broadcast({
                    'type': 'heartbeat',
                    'data': {'timestamp': datetime.now().isoformat()}
                })
                
                await asyncio.sleep(30)  # Send heartbeat every 30 seconds
                
            except Exception as e:
                logger.error(f"Error in heartbeat task: {e}")
                await asyncio.sleep(60)
    
    async def _cleanup_task(self):
        """Periodic cleanup of stale connections."""
        while self._running:
            try:
                # Check for stale connections
                stale_connections = []
                
                for connection_id, websocket in self.connection_manager.active_connections.items():
                    try:
                        # Try to send a ping
                        await websocket.ping()
                    except Exception:
                        stale_connections.append(connection_id)
                
                # Remove stale connections
                for connection_id in stale_connections:
                    self.connection_manager.disconnect(connection_id)
                
                if stale_connections:
                    logger.info(f"Cleaned up {len(stale_connections)} stale connections")
                
                await asyncio.sleep(300)  # Cleanup every 5 minutes
                
            except Exception as e:
                logger.error(f"Error in cleanup task: {e}")
                await asyncio.sleep(600)
    
    async def broadcast(self, message: Dict[str, Any], channel: str = None):
        """Broadcast message to connections."""
        await self.connection_manager.broadcast(message, channel)
    
    async def send_signal_update(self, signal_data: Dict[str, Any]):
        """Send trading signal update."""
        await self.broadcast({
            'type': 'signal_update',
            'data': signal_data,
            'timestamp': datetime.now().isoformat()
        }, 'signals')
    
    async def send_portfolio_update(self, portfolio_data: Dict[str, Any]):
        """Send portfolio update."""
        await self.broadcast({
            'type': 'portfolio_update',
            'data': portfolio_data,
            'timestamp': datetime.now().isoformat()
        }, 'portfolio')
    
    async def send_alert(self, alert_data: Dict[str, Any]):
        """Send alert notification."""
        await self.broadcast({
            'type': 'alert',
            'data': alert_data,
            'timestamp': datetime.now().isoformat()
        }, 'alerts')
    
    async def send_system_status(self, status_data: Dict[str, Any]):
        """Send system status update."""
        await self.broadcast({
            'type': 'system_status',
            'data': status_data,
            'timestamp': datetime.now().isoformat()
        }, 'system_status')
    
    async def send_backtest_update(self, backtest_data: Dict[str, Any]):
        """Send backtesting update."""
        await self.broadcast({
            'type': 'backtest_update',
            'data': backtest_data,
            'timestamp': datetime.now().isoformat()
        }, 'backtesting')
    
    async def send_rl_training_update(self, training_data: Dict[str, Any]):
        """Send RL training update."""
        await self.broadcast({
            'type': 'rl_training_update',
            'data': training_data,
            'timestamp': datetime.now().isoformat()
        }, 'rl_training')
    
    def get_stats(self) -> Dict[str, Any]:
        """Get WebSocket statistics."""
        return self.connection_manager.get_connection_info()

# Global WebSocket manager instance
websocket_manager = WebSocketManager()
