"""
Unit tests for RL Optimizer components.
"""

import pytest
import numpy as np
import torch
from unittest.mock import Mock, patch, MagicMock
from datetime import date, datetime, timedelta

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from src.rl_optimizer.ppo_optimizer import (
    PPOActor, PPOCritic, MarketStateExtractor, RewardFunction, PPOOptimizer
)
from src.rl_optimizer.multi_agent_manager import MultiAgentRLManager

class TestPPOActor:
    """Test cases for PPO Actor network."""
    
    def test_actor_initialization(self):
        """Test actor network initialization."""
        state_dim = 10
        action_dim = 5
        
        actor = PPOActor(state_dim, action_dim)
        
        assert actor.fc1.in_features == state_dim
        assert actor.mean_head.out_features == action_dim
        assert actor.std_head.out_features == action_dim
    
    def test_actor_forward_pass(self):
        """Test actor forward pass."""
        state_dim = 10
        action_dim = 5
        batch_size = 32
        
        actor = PPOActor(state_dim, action_dim)
        state = torch.randn(batch_size, state_dim)
        
        mean, std = actor.forward(state)
        
        assert mean.shape == (batch_size, action_dim)
        assert std.shape == (batch_size, action_dim)
        assert torch.all(std > 0)  # Std should be positive
        assert torch.all(torch.abs(mean) <= 1)  # Mean should be bounded by tanh
    
    def test_actor_get_action(self):
        """Test action sampling."""
        state_dim = 10
        action_dim = 5
        
        actor = PPOActor(state_dim, action_dim)
        state = torch.randn(1, state_dim)
        
        action, log_prob = actor.get_action(state)
        
        assert action.shape == (1, action_dim)
        assert log_prob.shape == (1,)
        assert torch.isfinite(log_prob).all()

class TestPPOCritic:
    """Test cases for PPO Critic network."""
    
    def test_critic_initialization(self):
        """Test critic network initialization."""
        state_dim = 10
        
        critic = PPOCritic(state_dim)
        
        assert critic.fc1.in_features == state_dim
        assert critic.value_head.out_features == 1
    
    def test_critic_forward_pass(self):
        """Test critic forward pass."""
        state_dim = 10
        batch_size = 32
        
        critic = PPOCritic(state_dim)
        state = torch.randn(batch_size, state_dim)
        
        value = critic.forward(state)
        
        assert value.shape == (batch_size, 1)
        assert torch.isfinite(value).all()

class TestMarketStateExtractor:
    """Test cases for Market State Extractor."""
    
    @pytest.fixture
    def extractor(self):
        """Create market state extractor."""
        return MarketStateExtractor()
    
    def test_extractor_initialization(self, extractor):
        """Test extractor initialization."""
        assert len(extractor.feature_names) == extractor.state_dim
        assert extractor.state_dim > 0
    
    @patch('src.rl_optimizer.ppo_optimizer.db_manager')
    @patch('src.rl_optimizer.ppo_optimizer.StockCRUD')
    @patch('src.rl_optimizer.ppo_optimizer.PriceCRUD')
    def test_extract_state(self, mock_price_crud, mock_stock_crud, mock_db_manager, extractor):
        """Test state extraction."""
        # Mock database responses
        mock_session = Mock()
        mock_db_manager.get_session.return_value.__enter__.return_value = mock_session
        
        mock_stock = Mock()
        mock_stock.id = 1
        mock_stock_crud.get_stock_by_symbol.return_value = mock_stock
        
        # Mock price data
        mock_prices = []
        for i in range(50):
            mock_price = Mock()
            mock_price.date = date.today() - timedelta(days=50-i)
            mock_price.close_price = 100 + i * 0.5
            mock_price.volume = 1000000
            mock_price.rsi_14 = 50.0
            mock_prices.append(mock_price)
        
        mock_price_crud.get_price_history.return_value = mock_prices
        
        # Extract state
        state = extractor.extract_state("TEST")
        
        assert isinstance(state, np.ndarray)
        assert len(state) == extractor.state_dim
        assert np.all(np.isfinite(state))
        assert np.all(np.abs(state) <= 3.0)  # Should be clipped
    
    def test_extract_state_insufficient_data(self, extractor):
        """Test state extraction with insufficient data."""
        with patch('src.rl_optimizer.ppo_optimizer.db_manager') as mock_db_manager:
            mock_session = Mock()
            mock_db_manager.get_session.return_value.__enter__.return_value = mock_session
            
            with patch('src.rl_optimizer.ppo_optimizer.StockCRUD') as mock_stock_crud:
                mock_stock_crud.get_stock_by_symbol.return_value = None
                
                state = extractor.extract_state("NONEXISTENT")
                
                assert isinstance(state, np.ndarray)
                assert len(state) == extractor.state_dim
                assert np.all(state == 0)  # Should return zeros

class TestRewardFunction:
    """Test cases for Reward Function."""
    
    @pytest.fixture
    def reward_function(self):
        """Create reward function."""
        return RewardFunction()
    
    def test_reward_calculation_good_performance(self, reward_function):
        """Test reward calculation for good performance."""
        backtest_result = {
            'total_return': 20.0,  # 20% return
            'sharpe_ratio': 1.5,
            'max_drawdown': 5.0,   # 5% drawdown
            'win_rate': 70.0,      # 70% win rate
            'total_trades': 50
        }
        
        reward = reward_function.calculate_reward(backtest_result)
        
        assert isinstance(reward, float)
        assert reward > 0  # Should be positive for good performance
        assert -2.0 <= reward <= 2.0  # Should be bounded
    
    def test_reward_calculation_poor_performance(self, reward_function):
        """Test reward calculation for poor performance."""
        backtest_result = {
            'total_return': -10.0,  # -10% return
            'sharpe_ratio': -0.5,
            'max_drawdown': 30.0,   # 30% drawdown
            'win_rate': 30.0,       # 30% win rate
            'total_trades': 20
        }
        
        reward = reward_function.calculate_reward(backtest_result)
        
        assert isinstance(reward, float)
        assert reward < 0  # Should be negative for poor performance
        assert -2.0 <= reward <= 2.0  # Should be bounded
    
    def test_reward_calculation_error_result(self, reward_function):
        """Test reward calculation for error result."""
        backtest_result = {'error': 'Backtest failed'}
        
        reward = reward_function.calculate_reward(backtest_result)
        
        assert reward == -1.0  # Should return penalty for errors

class TestPPOOptimizer:
    """Test cases for PPO Optimizer."""
    
    @pytest.fixture
    def parameter_bounds(self):
        """Create parameter bounds for testing."""
        return {
            'param1': (0.1, 1.0),
            'param2': (10, 50),
            'param3': (0.5, 2.0)
        }
    
    @pytest.fixture
    def optimizer(self, parameter_bounds):
        """Create PPO optimizer."""
        return PPOOptimizer("test_strategy", parameter_bounds)
    
    def test_optimizer_initialization(self, optimizer, parameter_bounds):
        """Test optimizer initialization."""
        assert optimizer.strategy_name == "test_strategy"
        assert optimizer.parameter_bounds == parameter_bounds
        assert len(optimizer.parameter_names) == len(parameter_bounds)
        assert optimizer.action_dim == len(parameter_bounds)
        assert optimizer.state_dim > 0
    
    def test_action_to_parameters(self, optimizer):
        """Test action to parameters conversion."""
        action = np.array([0.0, 0.5, -0.5])  # Actions in [-1, 1]
        
        parameters = optimizer.action_to_parameters(action)
        
        assert isinstance(parameters, dict)
        assert len(parameters) == len(optimizer.parameter_names)
        
        # Check bounds
        for param_name, value in parameters.items():
            min_val, max_val = optimizer.parameter_bounds[param_name]
            assert min_val <= value <= max_val
    
    def test_parameters_to_action(self, optimizer):
        """Test parameters to action conversion."""
        parameters = {
            'param1': 0.5,
            'param2': 30,
            'param3': 1.0
        }
        
        action = optimizer.parameters_to_action(parameters)
        
        assert isinstance(action, np.ndarray)
        assert len(action) == optimizer.action_dim
        assert np.all(-1 <= action) and np.all(action <= 1)
    
    def test_action_parameter_roundtrip(self, optimizer):
        """Test action-parameter conversion roundtrip."""
        original_action = np.array([0.2, -0.3, 0.8])
        
        # Convert to parameters and back
        parameters = optimizer.action_to_parameters(original_action)
        recovered_action = optimizer.parameters_to_action(parameters)
        
        # Should be approximately equal (within tolerance for integer params)
        np.testing.assert_allclose(original_action, recovered_action, atol=0.1)
    
    @patch('src.rl_optimizer.ppo_optimizer.backtest_runner')
    def test_evaluate_backtest(self, mock_backtest_runner, optimizer):
        """Test backtest evaluation."""
        # Mock backtest result
        mock_result = {
            'total_return': 15.0,
            'sharpe_ratio': 1.2,
            'max_drawdown': 8.0,
            'win_rate': 65.0,
            'total_trades': 30
        }
        
        mock_backtest_runner.engine.run_single_strategy_backtest.return_value = mock_result
        
        # Test evaluation
        parameters = {'param1': 0.5, 'param2': 30, 'param3': 1.0}
        reward = optimizer.evaluate_backtest("TEST", parameters)
        
        assert isinstance(reward, float)
        assert -2.0 <= reward <= 2.0
        mock_backtest_runner.engine.run_single_strategy_backtest.assert_called_once()
    
    def test_buffer_operations(self, optimizer):
        """Test experience buffer operations."""
        # Initially empty
        assert len(optimizer.buffer['states']) == 0
        
        # Add some dummy experience
        optimizer.buffer['states'].append(np.zeros(optimizer.state_dim))
        optimizer.buffer['actions'].append(np.zeros(optimizer.action_dim))
        optimizer.buffer['rewards'].append(0.5)
        optimizer.buffer['log_probs'].append(-1.0)
        optimizer.buffer['values'].append(0.3)
        optimizer.buffer['dones'].append(True)
        
        assert len(optimizer.buffer['states']) == 1
        
        # Clear buffer
        optimizer.clear_buffer()
        assert len(optimizer.buffer['states']) == 0

class TestMultiAgentRLManager:
    """Test cases for Multi-Agent RL Manager."""
    
    @pytest.fixture
    def manager(self):
        """Create multi-agent manager."""
        with patch('src.rl_optimizer.multi_agent_manager.strategy_registry'):
            with patch('src.rl_optimizer.multi_agent_manager.db_manager'):
                return MultiAgentRLManager()
    
    def test_manager_initialization(self, manager):
        """Test manager initialization."""
        assert isinstance(manager.agents, dict)
        assert isinstance(manager.parameter_bounds, dict)
        assert isinstance(manager.training_symbols, list)
    
    def test_get_strategy_parameter_bounds(self, manager):
        """Test parameter bounds retrieval."""
        bounds = manager._get_strategy_parameter_bounds()
        
        assert isinstance(bounds, dict)
        assert len(bounds) > 0
        
        # Check structure
        for strategy_name, strategy_bounds in bounds.items():
            assert isinstance(strategy_bounds, dict)
            for param_name, (min_val, max_val) in strategy_bounds.items():
                assert isinstance(min_val, (int, float))
                assert isinstance(max_val, (int, float))
                assert min_val < max_val
    
    @patch('src.rl_optimizer.multi_agent_manager.db_manager')
    @patch('src.rl_optimizer.multi_agent_manager.StockCRUD')
    def test_get_training_symbols(self, mock_stock_crud, mock_db_manager, manager):
        """Test training symbols retrieval."""
        # Mock database response
        mock_session = Mock()
        mock_db_manager.get_session.return_value.__enter__.return_value = mock_session
        
        mock_stocks = [Mock(symbol=f'STOCK{i}') for i in range(5)]
        mock_stock_crud.get_nifty50_stocks.return_value = mock_stocks
        
        symbols = manager._get_training_symbols()
        
        assert isinstance(symbols, list)
        assert len(symbols) == 5
        assert all(isinstance(symbol, str) for symbol in symbols)
    
    def test_adjust_for_market_conditions(self, manager):
        """Test parameter adjustment for market conditions."""
        base_params = {
            'min_signal_strength': 0.6,
            'base_oversold': 30,
            'volume_multiplier': 2.0
        }
        
        market_conditions = {
            'volatility': 0.04,  # High volatility
            'trend_strength': 0.8,  # Strong trend
            'volume_ratio': 2.5  # High volume
        }
        
        # Test RSI strategy adjustments
        adjusted = manager._adjust_for_market_conditions(
            base_params, market_conditions, 'adaptive_rsi'
        )
        
        assert isinstance(adjusted, dict)
        assert 'min_signal_strength' in adjusted
        
        # High volatility should increase signal strength threshold
        assert adjusted['min_signal_strength'] >= base_params['min_signal_strength']
    
    def test_get_agent_status(self, manager):
        """Test agent status retrieval."""
        status = manager.get_agent_status()
        
        assert isinstance(status, dict)
        assert 'total_agents' in status
        assert 'training_enabled' in status
        assert 'agents' in status
        assert isinstance(status['agents'], dict)

if __name__ == "__main__":
    pytest.main([__file__])
