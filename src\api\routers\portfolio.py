"""
Portfolio Router - Handles portfolio management operations.
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import List, Optional
from datetime import date, datetime, timedelta

from ..models import APIResponse, Position, Trade, PortfolioSummary, PaginationParams
from ..main import get_current_user
from ...data.database import db_manager
from ...data.crud import StockCRUD
from ...utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter()

@router.get("/summary", response_model=APIResponse)
async def get_portfolio_summary(current_user: dict = Depends(get_current_user)):
    """Get portfolio summary."""
    try:
        # Mock portfolio data - in production, this would come from actual portfolio database
        summary = {
            'total_value': 1250000.0,
            'cash_balance': 150000.0,
            'invested_amount': 1100000.0,
            'total_pnl': 250000.0,
            'total_pnl_percent': 25.0,
            'day_pnl': 15000.0,
            'day_pnl_percent': 1.2,
            'positions_count': 8,
            'active_strategies': 5
        }
        
        return APIResponse(
            success=True,
            message="Portfolio summary retrieved",
            data=summary
        )
        
    except Exception as e:
        logger.error(f"Error getting portfolio summary: {e}")
        raise HTTPException(status_code=500, detail="Failed to get portfolio summary")

@router.get("/positions", response_model=APIResponse)
async def get_positions(
    pagination: PaginationParams = Depends(),
    current_user: dict = Depends(get_current_user)
):
    """Get portfolio positions."""
    try:
        # Mock positions data
        positions = [
            {
                'id': 1,
                'symbol': 'RELIANCE',
                'quantity': 100,
                'avg_price': 2450.0,
                'current_price': 2520.0,
                'market_value': 252000.0,
                'unrealized_pnl': 7000.0,
                'unrealized_pnl_percent': 2.86,
                'entry_date': '2024-01-15T10:30:00',
                'strategy_name': 'adaptive_rsi'
            },
            {
                'id': 2,
                'symbol': 'TCS',
                'quantity': 50,
                'avg_price': 3800.0,
                'current_price': 3950.0,
                'market_value': 197500.0,
                'unrealized_pnl': 7500.0,
                'unrealized_pnl_percent': 3.95,
                'entry_date': '2024-01-20T14:15:00',
                'strategy_name': 'enhanced_pivot'
            }
        ]
        
        return APIResponse(
            success=True,
            message="Portfolio positions retrieved",
            data={
                "positions": positions,
                "total": len(positions),
                "page": pagination.page,
                "size": pagination.size
            }
        )
        
    except Exception as e:
        logger.error(f"Error getting positions: {e}")
        raise HTTPException(status_code=500, detail="Failed to get positions")

@router.get("/trades", response_model=APIResponse)
async def get_trades(
    pagination: PaginationParams = Depends(),
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    current_user: dict = Depends(get_current_user)
):
    """Get trade history."""
    try:
        # Mock trades data
        trades = [
            {
                'id': 1,
                'symbol': 'RELIANCE',
                'trade_type': 'BUY',
                'quantity': 100,
                'price': 2450.0,
                'total_value': 245000.0,
                'commission': 245.0,
                'trade_date': '2024-01-15T10:30:00',
                'strategy_name': 'adaptive_rsi',
                'signal_id': 123
            },
            {
                'id': 2,
                'symbol': 'TCS',
                'trade_type': 'BUY',
                'quantity': 50,
                'price': 3800.0,
                'total_value': 190000.0,
                'commission': 190.0,
                'trade_date': '2024-01-20T14:15:00',
                'strategy_name': 'enhanced_pivot',
                'signal_id': 124
            }
        ]
        
        return APIResponse(
            success=True,
            message="Trade history retrieved",
            data={
                "trades": trades,
                "total": len(trades),
                "page": pagination.page,
                "size": pagination.size
            }
        )
        
    except Exception as e:
        logger.error(f"Error getting trades: {e}")
        raise HTTPException(status_code=500, detail="Failed to get trades")

@router.get("/performance", response_model=APIResponse)
async def get_portfolio_performance(
    days: int = 30,
    current_user: dict = Depends(get_current_user)
):
    """Get portfolio performance metrics."""
    try:
        # Mock performance data
        performance = {
            'period_days': days,
            'total_return': 25.0,
            'annual_return': 28.5,
            'sharpe_ratio': 1.8,
            'max_drawdown': 8.2,
            'volatility': 15.6,
            'win_rate': 72.5,
            'profit_factor': 2.1,
            'best_performing_strategy': 'enhanced_pivot',
            'worst_performing_strategy': 'volume_confirmed_breakout'
        }
        
        return APIResponse(
            success=True,
            message="Portfolio performance retrieved",
            data=performance
        )
        
    except Exception as e:
        logger.error(f"Error getting portfolio performance: {e}")
        raise HTTPException(status_code=500, detail="Failed to get portfolio performance")
