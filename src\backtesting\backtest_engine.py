"""
Comprehensive Backtesting Engine using Backtrader.
Supports individual strategies, strategy combinations, and portfolio-level backtesting.
"""

import backtrader as bt
import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
from typing import Dict, List, Any, Optional, Tuple, Union
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json

from ..data.database import db_manager
from ..data.models import StockMetadata, DailyPrice, BacktestResult
from ..data.crud import StockCRUD, PriceCRUD, BacktestCRUD
from ..strategies.base_strategy import BaseStrategy, TradingSignal, SignalType
from ..strategies.strategy_registry import strategy_registry
from ..utils.logger import get_logger
from ..utils.config import config

logger = get_logger(__name__)

class PositionSizer(bt.Sizer):
    """Custom position sizer implementing 2% risk per trade."""
    
    def __init__(self):
        self.risk_per_trade = 0.02  # 2% risk per trade
    
    def _getsizing(self, comminfo, cash, data, isbuy):
        """Calculate position size based on risk management."""
        if not isbuy:
            return self.broker.getposition(data).size
        
        # Get current price and stop loss from strategy
        current_price = data.close[0]
        
        # Try to get stop loss from strategy (if available)
        stop_loss = getattr(self.strategy, '_current_stop_loss', None)
        
        if stop_loss and stop_loss > 0:
            # Calculate position size based on stop loss
            risk_amount = cash * self.risk_per_trade
            price_risk = abs(current_price - stop_loss)
            
            if price_risk > 0:
                shares = int(risk_amount / price_risk)
            else:
                shares = int(cash * 0.05 / current_price)  # Fallback to 5% of cash
        else:
            # Fallback position sizing (5% of portfolio)
            shares = int(cash * 0.05 / current_price)
        
        return max(1, shares)  # Minimum 1 share

class CommissionScheme(bt.CommInfoBase):
    """Custom commission scheme for Indian stock market."""
    
    params = (
        ('commission', 0.001),  # 0.1% commission
        ('mult', 1.0),
        ('margin', None),
        ('commtype', bt.CommInfoBase.COMM_PERC),
        ('stocklike', True),
        ('percabs', False),
    )
    
    def _getcommission(self, size, price, pseudoexec):
        """Calculate commission with minimum charges."""
        commission = abs(size) * price * self.p.commission
        return max(commission, 20.0)  # Minimum ₹20 commission

class BacktraderStrategy(bt.Strategy):
    """Backtrader strategy wrapper for our trading strategies."""
    
    params = (
        ('strategy_instance', None),
        ('symbol', ''),
        ('printlog', False),
    )
    
    def __init__(self):
        self.strategy_instance = self.params.strategy_instance
        self.symbol = self.params.symbol
        self.signals = []
        self.trades = []
        self.current_signal = None
        self._current_stop_loss = None
        
        # Track performance metrics
        self.trade_count = 0
        self.winning_trades = 0
        self.losing_trades = 0
        self.total_pnl = 0.0
        
    def log(self, txt, dt=None, doprint=False):
        """Logging function."""
        if self.params.printlog or doprint:
            dt = dt or self.datas[0].datetime.date(0)
            logger.debug(f'{dt.isoformat()}: {txt}')
    
    def notify_order(self, order):
        """Handle order notifications."""
        if order.status in [order.Submitted, order.Accepted]:
            return
        
        if order.status in [order.Completed]:
            if order.isbuy():
                self.log(f'BUY EXECUTED: Price: {order.executed.price:.2f}, '
                        f'Cost: {order.executed.value:.2f}, Comm: {order.executed.comm:.2f}')
            else:
                self.log(f'SELL EXECUTED: Price: {order.executed.price:.2f}, '
                        f'Cost: {order.executed.value:.2f}, Comm: {order.executed.comm:.2f}')
        
        elif order.status in [order.Canceled, order.Margin, order.Rejected]:
            self.log(f'Order {order.status}')
    
    def notify_trade(self, trade):
        """Handle trade notifications."""
        if not trade.isclosed:
            return
        
        self.trade_count += 1
        pnl = trade.pnl
        self.total_pnl += pnl
        
        if pnl > 0:
            self.winning_trades += 1
        else:
            self.losing_trades += 1
        
        self.trades.append({
            'entry_date': bt.num2date(trade.dtopen),
            'exit_date': bt.num2date(trade.dtclose),
            'entry_price': trade.price,
            'exit_price': trade.price + pnl / trade.size,
            'size': trade.size,
            'pnl': pnl,
            'pnl_percent': (pnl / (trade.price * abs(trade.size))) * 100,
            'duration': trade.barlen
        })
        
        self.log(f'TRADE CLOSED: PnL: {pnl:.2f}, PnL%: {(pnl/(trade.price * abs(trade.size)))*100:.2f}%')
    
    def next(self):
        """Main strategy logic executed on each bar."""
        if not self.strategy_instance:
            return
        
        # Get current data
        current_date = self.datas[0].datetime.date(0)
        current_price = self.datas[0].close[0]
        
        # Create data point for strategy
        data_point = {
            'date': current_date,
            'open_price': self.datas[0].open[0],
            'high_price': self.datas[0].high[0],
            'low_price': self.datas[0].low[0],
            'close_price': current_price,
            'volume': self.datas[0].volume[0],
        }
        
        # Add technical indicators if available
        for attr_name in dir(self.datas[0]):
            if attr_name.startswith(('sma_', 'ema_', 'rsi_', 'macd', 'bb_', 'volume_', 'pivot_', 'resistance_', 'support_')):
                try:
                    value = getattr(self.datas[0], attr_name)[0]
                    if not np.isnan(value):
                        data_point[attr_name] = value
                except (AttributeError, IndexError):
                    pass
        
        # Check for existing position
        position = self.broker.getposition(self.datas[0])
        
        # Exit logic for existing positions
        if position.size != 0:
            if self.current_signal:
                # Check stop loss
                if self.current_signal.stop_loss:
                    if ((position.size > 0 and current_price <= self.current_signal.stop_loss) or
                        (position.size < 0 and current_price >= self.current_signal.stop_loss)):
                        self.close()
                        self.current_signal = None
                        self._current_stop_loss = None
                        return
                
                # Check target price
                if self.current_signal.target_price:
                    if ((position.size > 0 and current_price >= self.current_signal.target_price) or
                        (position.size < 0 and current_price <= self.current_signal.target_price)):
                        self.close()
                        self.current_signal = None
                        self._current_stop_loss = None
                        return
        
        # Entry logic - only if no position
        if position.size == 0:
            # Create DataFrame for strategy (simplified for single data point)
            df = pd.DataFrame([data_point])
            
            try:
                # Generate signals from strategy
                signals = self.strategy_instance.calculate_signals(df)
                
                if signals:
                    signal = signals[-1]  # Take the latest signal
                    signal.symbol = self.symbol
                    
                    if signal.signal_type == SignalType.BUY:
                        self.buy()
                        self.current_signal = signal
                        self._current_stop_loss = signal.stop_loss
                        self.log(f'BUY SIGNAL: {signal.signal_strength:.2f} strength')
                    
                    elif signal.signal_type == SignalType.SELL:
                        self.sell()
                        self.current_signal = signal
                        self._current_stop_loss = signal.stop_loss
                        self.log(f'SELL SIGNAL: {signal.signal_strength:.2f} strength')
            
            except Exception as e:
                self.log(f'Error generating signals: {e}', doprint=True)

class BacktestEngine:
    """Main backtesting engine using Backtrader."""
    
    def __init__(self):
        """Initialize backtesting engine."""
        self.config = config.get_backtesting_config()
        self.initial_capital = self.config.get('initial_capital', 1000000)
        self.commission = self.config.get('commission', 0.001)
        self.slippage = self.config.get('slippage', 0.0005)
        
        # Results storage
        self.results = {}
        self.reports_dir = Path("reports/backtesting")
        self.reports_dir.mkdir(parents=True, exist_ok=True)
    
    def prepare_data(
        self, 
        symbol: str, 
        start_date: date, 
        end_date: date
    ) -> Optional[bt.feeds.PandasData]:
        """Prepare data feed for backtesting.
        
        Args:
            symbol: Stock symbol
            start_date: Start date for backtesting
            end_date: End date for backtesting
            
        Returns:
            Backtrader data feed or None if no data
        """
        try:
            with db_manager.get_session() as db:
                # Get stock metadata
                stock = StockCRUD.get_stock_by_symbol(db, symbol)
                if not stock:
                    logger.error(f"Stock {symbol} not found")
                    return None
                
                # Get price data
                price_data = PriceCRUD.get_price_history(db, stock.id, start_date, end_date)
                
                if not price_data:
                    logger.warning(f"No price data found for {symbol}")
                    return None
                
                # Convert to DataFrame
                data_records = []
                for price in price_data:
                    record = {
                        'datetime': price.date,
                        'open': float(price.open_price),
                        'high': float(price.high_price),
                        'low': float(price.low_price),
                        'close': float(price.close_price),
                        'volume': price.volume,
                    }
                    
                    # Add technical indicators
                    if price.sma_20:
                        record['sma_20'] = float(price.sma_20)
                    if price.sma_50:
                        record['sma_50'] = float(price.sma_50)
                    if price.sma_200:
                        record['sma_200'] = float(price.sma_200)
                    if price.rsi_14:
                        record['rsi_14'] = float(price.rsi_14)
                    if price.macd:
                        record['macd'] = float(price.macd)
                    if price.macd_signal:
                        record['macd_signal'] = float(price.macd_signal)
                    if price.volume_ratio:
                        record['volume_ratio'] = float(price.volume_ratio)
                    
                    data_records.append(record)
                
                # Create DataFrame
                df = pd.DataFrame(data_records)
                df['datetime'] = pd.to_datetime(df['datetime'])
                df.set_index('datetime', inplace=True)
                df.sort_index(inplace=True)
                
                # Create Backtrader data feed
                data_feed = bt.feeds.PandasData(
                    dataname=df,
                    datetime=None,  # Use index
                    open='open',
                    high='high',
                    low='low',
                    close='close',
                    volume='volume',
                    openinterest=None
                )
                
                # Add technical indicators as lines
                for col in df.columns:
                    if col not in ['open', 'high', 'low', 'close', 'volume']:
                        setattr(data_feed, col, df[col].values)
                
                logger.info(f"Prepared data for {symbol}: {len(df)} bars from {start_date} to {end_date}")
                return data_feed
                
        except Exception as e:
            logger.error(f"Error preparing data for {symbol}: {e}")
            return None
    
    def run_single_strategy_backtest(
        self, 
        strategy_name: str, 
        symbol: str,
        start_date: date, 
        end_date: date,
        parameters: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Run backtest for a single strategy on a single symbol.
        
        Args:
            strategy_name: Name of strategy to test
            symbol: Stock symbol
            start_date: Start date for backtesting
            end_date: End date for backtesting
            parameters: Strategy parameters override
            
        Returns:
            Backtesting results
        """
        logger.info(f"Running backtest: {strategy_name} on {symbol} from {start_date} to {end_date}")
        
        try:
            # Get strategy instance
            strategy_instance = strategy_registry.create_strategy(strategy_name, parameters)
            if not strategy_instance:
                return {'error': f'Strategy {strategy_name} not found'}
            
            # Prepare data
            data_feed = self.prepare_data(symbol, start_date, end_date)
            if not data_feed:
                return {'error': f'No data available for {symbol}'}
            
            # Create Cerebro engine
            cerebro = bt.Cerebro()
            
            # Add strategy
            cerebro.addstrategy(
                BacktraderStrategy,
                strategy_instance=strategy_instance,
                symbol=symbol,
                printlog=False
            )
            
            # Add data
            cerebro.adddata(data_feed)
            
            # Set initial capital
            cerebro.broker.setcash(self.initial_capital)
            
            # Add commission
            cerebro.broker.addcommissioninfo(CommissionScheme())
            
            # Add position sizer
            cerebro.addsizer(PositionSizer)
            
            # Add analyzers
            cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')
            cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
            cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
            cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')
            cerebro.addanalyzer(bt.analyzers.SQN, _name='sqn')
            
            # Run backtest
            initial_value = cerebro.broker.getvalue()
            results = cerebro.run()
            final_value = cerebro.broker.getvalue()
            
            # Extract results
            strategy_result = results[0]
            
            # Get analyzer results
            trade_analysis = strategy_result.analyzers.trades.get_analysis()
            sharpe_analysis = strategy_result.analyzers.sharpe.get_analysis()
            drawdown_analysis = strategy_result.analyzers.drawdown.get_analysis()
            returns_analysis = strategy_result.analyzers.returns.get_analysis()
            sqn_analysis = strategy_result.analyzers.sqn.get_analysis()
            
            # Calculate performance metrics
            total_return = ((final_value - initial_value) / initial_value) * 100
            
            # Calculate annualized return
            days = (end_date - start_date).days
            years = days / 365.25
            annual_return = ((final_value / initial_value) ** (1/years) - 1) * 100 if years > 0 else 0
            
            # Extract trade statistics
            total_trades = trade_analysis.get('total', {}).get('total', 0)
            winning_trades = trade_analysis.get('won', {}).get('total', 0)
            losing_trades = trade_analysis.get('lost', {}).get('total', 0)
            
            win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
            
            # Average win/loss
            avg_win = trade_analysis.get('won', {}).get('pnl', {}).get('average', 0)
            avg_loss = abs(trade_analysis.get('lost', {}).get('pnl', {}).get('average', 0))
            
            # Profit factor
            gross_profit = trade_analysis.get('won', {}).get('pnl', {}).get('total', 0)
            gross_loss = abs(trade_analysis.get('lost', {}).get('pnl', {}).get('total', 0))
            profit_factor = gross_profit / gross_loss if gross_loss > 0 else 0
            
            # Compile results
            backtest_results = {
                'strategy_name': strategy_name,
                'symbol': symbol,
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat(),
                'initial_capital': initial_value,
                'final_value': final_value,
                'total_return': total_return,
                'annual_return': annual_return,
                'sharpe_ratio': sharpe_analysis.get('sharperatio', 0),
                'max_drawdown': drawdown_analysis.get('max', {}).get('drawdown', 0),
                'calmar_ratio': annual_return / abs(drawdown_analysis.get('max', {}).get('drawdown', 1)) if drawdown_analysis.get('max', {}).get('drawdown', 0) != 0 else 0,
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': losing_trades,
                'win_rate': win_rate,
                'avg_win': avg_win,
                'avg_loss': avg_loss,
                'profit_factor': profit_factor,
                'sqn': sqn_analysis.get('sqn', 0),
                'parameters': parameters or {},
                'trades': strategy_result.trades if hasattr(strategy_result, 'trades') else []
            }
            
            # Store results in database
            self._store_backtest_results(backtest_results)
            
            logger.info(f"Backtest completed: {total_return:.2f}% return, {win_rate:.1f}% win rate")
            return backtest_results
            
        except Exception as e:
            logger.error(f"Error in backtest: {e}")
            return {'error': str(e)}

    def _store_backtest_results(self, results: Dict[str, Any]):
        """Store backtest results in database.

        Args:
            results: Backtest results dictionary
        """
        try:
            with db_manager.get_session() as db:
                backtest_data = {
                    'strategy_name': results['strategy_name'],
                    'start_date': datetime.fromisoformat(results['start_date']).date(),
                    'end_date': datetime.fromisoformat(results['end_date']).date(),
                    'initial_capital': results['initial_capital'],
                    'total_return': results['total_return'],
                    'annual_return': results['annual_return'],
                    'sharpe_ratio': results['sharpe_ratio'],
                    'max_drawdown': results['max_drawdown'],
                    'calmar_ratio': results['calmar_ratio'],
                    'total_trades': results['total_trades'],
                    'winning_trades': results['winning_trades'],
                    'losing_trades': results['losing_trades'],
                    'win_rate': results['win_rate'],
                    'avg_win': results['avg_win'],
                    'avg_loss': results['avg_loss'],
                    'profit_factor': results['profit_factor'],
                    'parameters': json.dumps(results['parameters'])
                }

                BacktestCRUD.create_backtest_result(db, backtest_data)
                logger.debug("Backtest results stored in database")

        except Exception as e:
            logger.error(f"Error storing backtest results: {e}")

    def run_portfolio_backtest(
        self,
        strategy_names: List[str],
        symbols: List[str],
        start_date: date,
        end_date: date,
        allocation_method: str = 'equal_weight'
    ) -> Dict[str, Any]:
        """Run portfolio-level backtest with multiple strategies and symbols.

        Args:
            strategy_names: List of strategy names
            symbols: List of stock symbols
            start_date: Start date for backtesting
            end_date: End date for backtesting
            allocation_method: Portfolio allocation method

        Returns:
            Portfolio backtesting results
        """
        logger.info(f"Running portfolio backtest: {len(strategy_names)} strategies, {len(symbols)} symbols")

        try:
            portfolio_results = {
                'strategy_names': strategy_names,
                'symbols': symbols,
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat(),
                'allocation_method': allocation_method,
                'individual_results': {},
                'portfolio_metrics': {}
            }

            # Run individual backtests
            individual_returns = []
            total_trades = 0
            total_winning_trades = 0

            for strategy_name in strategy_names:
                strategy_results = {}

                for symbol in symbols:
                    result = self.run_single_strategy_backtest(
                        strategy_name, symbol, start_date, end_date
                    )

                    if 'error' not in result:
                        strategy_results[symbol] = result
                        individual_returns.append(result['total_return'])
                        total_trades += result['total_trades']
                        total_winning_trades += result['winning_trades']

                portfolio_results['individual_results'][strategy_name] = strategy_results

            # Calculate portfolio metrics
            if individual_returns:
                portfolio_results['portfolio_metrics'] = {
                    'average_return': np.mean(individual_returns),
                    'portfolio_volatility': np.std(individual_returns),
                    'best_performer': max(individual_returns),
                    'worst_performer': min(individual_returns),
                    'total_trades': total_trades,
                    'total_winning_trades': total_winning_trades,
                    'portfolio_win_rate': (total_winning_trades / total_trades * 100) if total_trades > 0 else 0
                }

            return portfolio_results

        except Exception as e:
            logger.error(f"Error in portfolio backtest: {e}")
            return {'error': str(e)}

    def run_strategy_combination_backtest(
        self,
        strategy_combinations: List[Tuple[str, str]],
        symbols: List[str],
        start_date: date,
        end_date: date
    ) -> Dict[str, Any]:
        """Run backtest for strategy combinations (e.g., RSI+MACD).

        Args:
            strategy_combinations: List of strategy name tuples
            symbols: List of stock symbols
            start_date: Start date for backtesting
            end_date: End date for backtesting

        Returns:
            Combination backtesting results
        """
        logger.info(f"Running strategy combination backtest: {len(strategy_combinations)} combinations")

        try:
            combination_results = {}

            for combination in strategy_combinations:
                combo_name = '+'.join(combination)
                logger.info(f"Testing combination: {combo_name}")

                combo_results = {
                    'combination_name': combo_name,
                    'strategies': combination,
                    'symbol_results': {}
                }

                for symbol in symbols:
                    # Run individual strategy backtests
                    strategy_results = []

                    for strategy_name in combination:
                        result = self.run_single_strategy_backtest(
                            strategy_name, symbol, start_date, end_date
                        )

                        if 'error' not in result:
                            strategy_results.append(result)

                    # Combine results (simple average for now)
                    if strategy_results:
                        combined_return = np.mean([r['total_return'] for r in strategy_results])
                        combined_sharpe = np.mean([r['sharpe_ratio'] for r in strategy_results])
                        combined_drawdown = np.mean([r['max_drawdown'] for r in strategy_results])
                        combined_win_rate = np.mean([r['win_rate'] for r in strategy_results])

                        combo_results['symbol_results'][symbol] = {
                            'combined_return': combined_return,
                            'combined_sharpe': combined_sharpe,
                            'combined_drawdown': combined_drawdown,
                            'combined_win_rate': combined_win_rate,
                            'individual_results': strategy_results
                        }

                combination_results[combo_name] = combo_results

            return combination_results

        except Exception as e:
            logger.error(f"Error in combination backtest: {e}")
            return {'error': str(e)}

    def run_walk_forward_analysis(
        self,
        strategy_name: str,
        symbol: str,
        start_date: date,
        end_date: date,
        train_months: int = 12,
        test_months: int = 3,
        step_months: int = 1
    ) -> Dict[str, Any]:
        """Run walk-forward analysis for strategy validation.

        Args:
            strategy_name: Name of strategy to test
            symbol: Stock symbol
            start_date: Start date for analysis
            end_date: End date for analysis
            train_months: Training period in months
            test_months: Testing period in months
            step_months: Step size in months

        Returns:
            Walk-forward analysis results
        """
        logger.info(f"Running walk-forward analysis: {strategy_name} on {symbol}")

        try:
            wf_results = {
                'strategy_name': strategy_name,
                'symbol': symbol,
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat(),
                'train_months': train_months,
                'test_months': test_months,
                'step_months': step_months,
                'periods': []
            }

            current_date = start_date
            period_num = 1

            while current_date + timedelta(days=train_months*30 + test_months*30) <= end_date:
                # Define training and testing periods
                train_start = current_date
                train_end = current_date + timedelta(days=train_months*30)
                test_start = train_end + timedelta(days=1)
                test_end = test_start + timedelta(days=test_months*30)

                logger.info(f"WF Period {period_num}: Train {train_start} to {train_end}, Test {test_start} to {test_end}")

                # Run training backtest (for parameter optimization in future)
                train_result = self.run_single_strategy_backtest(
                    strategy_name, symbol, train_start, train_end
                )

                # Run testing backtest
                test_result = self.run_single_strategy_backtest(
                    strategy_name, symbol, test_start, test_end
                )

                period_result = {
                    'period': period_num,
                    'train_start': train_start.isoformat(),
                    'train_end': train_end.isoformat(),
                    'test_start': test_start.isoformat(),
                    'test_end': test_end.isoformat(),
                    'train_result': train_result,
                    'test_result': test_result
                }

                wf_results['periods'].append(period_result)

                # Move to next period
                current_date += timedelta(days=step_months*30)
                period_num += 1

            # Calculate walk-forward metrics
            test_returns = [p['test_result']['total_return'] for p in wf_results['periods']
                           if 'error' not in p['test_result']]

            if test_returns:
                wf_results['summary'] = {
                    'periods_tested': len(test_returns),
                    'average_return': np.mean(test_returns),
                    'return_volatility': np.std(test_returns),
                    'best_period': max(test_returns),
                    'worst_period': min(test_returns),
                    'positive_periods': sum(1 for r in test_returns if r > 0),
                    'consistency_ratio': sum(1 for r in test_returns if r > 0) / len(test_returns)
                }

            return wf_results

        except Exception as e:
            logger.error(f"Error in walk-forward analysis: {e}")
            return {'error': str(e)}

    def generate_performance_report(
        self,
        results: Dict[str, Any],
        save_plots: bool = True
    ) -> Dict[str, Any]:
        """Generate comprehensive performance report with visualizations.

        Args:
            results: Backtest results
            save_plots: Whether to save plot files

        Returns:
            Performance report with metrics and plot paths
        """
        try:
            report = {
                'summary': self._create_performance_summary(results),
                'detailed_metrics': self._calculate_detailed_metrics(results),
                'plots': {}
            }

            if save_plots:
                report['plots'] = self._generate_performance_plots(results)

            # Save report to file
            report_file = self.reports_dir / f"report_{results['strategy_name']}_{results['symbol']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2, default=str)

            report['report_file'] = str(report_file)

            return report

        except Exception as e:
            logger.error(f"Error generating performance report: {e}")
            return {'error': str(e)}

    def _create_performance_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Create performance summary."""
        return {
            'strategy': results['strategy_name'],
            'symbol': results['symbol'],
            'period': f"{results['start_date']} to {results['end_date']}",
            'total_return': f"{results['total_return']:.2f}%",
            'annual_return': f"{results['annual_return']:.2f}%",
            'sharpe_ratio': f"{results['sharpe_ratio']:.3f}",
            'max_drawdown': f"{results['max_drawdown']:.2f}%",
            'win_rate': f"{results['win_rate']:.1f}%",
            'total_trades': results['total_trades'],
            'profit_factor': f"{results['profit_factor']:.2f}"
        }

    def _calculate_detailed_metrics(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate additional detailed metrics."""
        try:
            trades = results.get('trades', [])

            if not trades:
                return {}

            # Trade duration analysis
            durations = [trade.get('duration', 0) for trade in trades]

            # PnL analysis
            pnls = [trade.get('pnl', 0) for trade in trades]
            winning_pnls = [pnl for pnl in pnls if pnl > 0]
            losing_pnls = [pnl for pnl in pnls if pnl < 0]

            return {
                'average_trade_duration': np.mean(durations) if durations else 0,
                'median_trade_duration': np.median(durations) if durations else 0,
                'longest_trade': max(durations) if durations else 0,
                'shortest_trade': min(durations) if durations else 0,
                'largest_win': max(winning_pnls) if winning_pnls else 0,
                'largest_loss': min(losing_pnls) if losing_pnls else 0,
                'average_win': np.mean(winning_pnls) if winning_pnls else 0,
                'average_loss': np.mean(losing_pnls) if losing_pnls else 0,
                'win_loss_ratio': (np.mean(winning_pnls) / abs(np.mean(losing_pnls))) if winning_pnls and losing_pnls else 0,
                'expectancy': np.mean(pnls) if pnls else 0
            }

        except Exception as e:
            logger.error(f"Error calculating detailed metrics: {e}")
            return {}

    def _generate_performance_plots(self, results: Dict[str, Any]) -> Dict[str, str]:
        """Generate performance visualization plots.

        Args:
            results: Backtest results

        Returns:
            Dictionary with plot file paths
        """
        try:
            plots = {}

            # Set style
            plt.style.use('seaborn-v0_8')

            # 1. Equity Curve Plot
            if results.get('trades'):
                equity_plot = self._plot_equity_curve(results)
                plots['equity_curve'] = equity_plot

            # 2. Trade Analysis Plot
            if results.get('trades'):
                trade_plot = self._plot_trade_analysis(results)
                plots['trade_analysis'] = trade_plot

            # 3. Monthly Returns Heatmap
            if results.get('trades'):
                heatmap_plot = self._plot_monthly_returns(results)
                plots['monthly_returns'] = heatmap_plot

            return plots

        except Exception as e:
            logger.error(f"Error generating plots: {e}")
            return {}

    def _plot_equity_curve(self, results: Dict[str, Any]) -> str:
        """Plot equity curve."""
        try:
            trades = results['trades']

            # Calculate cumulative PnL
            cumulative_pnl = []
            running_pnl = results['initial_capital']

            for trade in trades:
                running_pnl += trade['pnl']
                cumulative_pnl.append(running_pnl)

            # Create plot
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))

            # Equity curve
            ax1.plot(range(len(cumulative_pnl)), cumulative_pnl, linewidth=2, color='blue')
            ax1.set_title(f"Equity Curve - {results['strategy_name']} on {results['symbol']}")
            ax1.set_ylabel('Portfolio Value (₹)')
            ax1.grid(True, alpha=0.3)

            # Drawdown
            peak = np.maximum.accumulate(cumulative_pnl)
            drawdown = (np.array(cumulative_pnl) - peak) / peak * 100

            ax2.fill_between(range(len(drawdown)), drawdown, 0, color='red', alpha=0.3)
            ax2.set_title('Drawdown')
            ax2.set_ylabel('Drawdown (%)')
            ax2.set_xlabel('Trade Number')
            ax2.grid(True, alpha=0.3)

            plt.tight_layout()

            # Save plot
            plot_file = self.reports_dir / f"equity_curve_{results['strategy_name']}_{results['symbol']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            plt.savefig(plot_file, dpi=300, bbox_inches='tight')
            plt.close()

            return str(plot_file)

        except Exception as e:
            logger.error(f"Error plotting equity curve: {e}")
            return ""

    def _plot_trade_analysis(self, results: Dict[str, Any]) -> str:
        """Plot trade analysis."""
        try:
            trades = results['trades']

            # Extract trade data
            pnls = [trade['pnl'] for trade in trades]
            durations = [trade['duration'] for trade in trades]

            # Create subplots
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))

            # PnL distribution
            ax1.hist(pnls, bins=20, alpha=0.7, color='blue', edgecolor='black')
            ax1.set_title('PnL Distribution')
            ax1.set_xlabel('PnL (₹)')
            ax1.set_ylabel('Frequency')
            ax1.axvline(0, color='red', linestyle='--', alpha=0.7)

            # Trade duration distribution
            ax2.hist(durations, bins=20, alpha=0.7, color='green', edgecolor='black')
            ax2.set_title('Trade Duration Distribution')
            ax2.set_xlabel('Duration (bars)')
            ax2.set_ylabel('Frequency')

            # PnL vs Duration scatter
            colors = ['green' if pnl > 0 else 'red' for pnl in pnls]
            ax3.scatter(durations, pnls, c=colors, alpha=0.6)
            ax3.set_title('PnL vs Trade Duration')
            ax3.set_xlabel('Duration (bars)')
            ax3.set_ylabel('PnL (₹)')
            ax3.axhline(0, color='black', linestyle='-', alpha=0.3)

            # Cumulative PnL
            cumulative_pnl = np.cumsum(pnls)
            ax4.plot(range(len(cumulative_pnl)), cumulative_pnl, linewidth=2)
            ax4.set_title('Cumulative PnL')
            ax4.set_xlabel('Trade Number')
            ax4.set_ylabel('Cumulative PnL (₹)')
            ax4.grid(True, alpha=0.3)

            plt.tight_layout()

            # Save plot
            plot_file = self.reports_dir / f"trade_analysis_{results['strategy_name']}_{results['symbol']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            plt.savefig(plot_file, dpi=300, bbox_inches='tight')
            plt.close()

            return str(plot_file)

        except Exception as e:
            logger.error(f"Error plotting trade analysis: {e}")
            return ""

    def _plot_monthly_returns(self, results: Dict[str, Any]) -> str:
        """Plot monthly returns heatmap."""
        try:
            trades = results['trades']

            # Group trades by month
            monthly_returns = {}

            for trade in trades:
                if 'exit_date' in trade:
                    exit_date = trade['exit_date']
                    if hasattr(exit_date, 'strftime'):
                        month_key = exit_date.strftime('%Y-%m')
                        if month_key not in monthly_returns:
                            monthly_returns[month_key] = 0
                        monthly_returns[month_key] += trade['pnl']

            if not monthly_returns:
                return ""

            # Convert to DataFrame for heatmap
            months = sorted(monthly_returns.keys())
            returns = [monthly_returns[month] for month in months]

            # Create simple bar plot (heatmap needs more data structure)
            fig, ax = plt.subplots(figsize=(12, 6))

            colors = ['green' if r > 0 else 'red' for r in returns]
            bars = ax.bar(range(len(months)), returns, color=colors, alpha=0.7)

            ax.set_title('Monthly Returns')
            ax.set_xlabel('Month')
            ax.set_ylabel('Returns (₹)')
            ax.set_xticks(range(len(months)))
            ax.set_xticklabels(months, rotation=45)
            ax.axhline(0, color='black', linestyle='-', alpha=0.3)
            ax.grid(True, alpha=0.3)

            plt.tight_layout()

            # Save plot
            plot_file = self.reports_dir / f"monthly_returns_{results['strategy_name']}_{results['symbol']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            plt.savefig(plot_file, dpi=300, bbox_inches='tight')
            plt.close()

            return str(plot_file)

        except Exception as e:
            logger.error(f"Error plotting monthly returns: {e}")
            return ""
