"""
Broker Integration System - Supports multiple Indian brokers.
Provides unified interface for order execution, portfolio management, and market data.
"""

import asyncio
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, date
from enum import Enum
import json
import requests
from dataclasses import dataclass

from ..utils.logger import get_logger
from ..utils.config import config

logger = get_logger(__name__)

class OrderType(Enum):
    """Order types."""
    MARKET = "MARKET"
    LIMIT = "LIMIT"
    STOP_LOSS = "STOP_LOSS"
    STOP_LOSS_MARKET = "STOP_LOSS_MARKET"

class OrderSide(Enum):
    """Order sides."""
    BUY = "BUY"
    SELL = "SELL"

class OrderStatus(Enum):
    """Order status."""
    PENDING = "PENDING"
    OPEN = "OPEN"
    PARTIAL = "PARTIAL"
    COMPLETE = "COMPLETE"
    CANCELLED = "CANCELLED"
    REJECTED = "REJECTED"

class ProductType(Enum):
    """Product types."""
    CNC = "CNC"  # Cash and Carry
    MIS = "MIS"  # Margin Intraday Square-off
    NRML = "NRML"  # Normal

@dataclass
class Order:
    """Order data structure."""
    symbol: str
    side: OrderSide
    quantity: int
    order_type: OrderType
    price: Optional[float] = None
    stop_price: Optional[float] = None
    product_type: ProductType = ProductType.CNC
    validity: str = "DAY"
    order_id: Optional[str] = None
    status: OrderStatus = OrderStatus.PENDING
    filled_quantity: int = 0
    average_price: Optional[float] = None
    timestamp: Optional[datetime] = None
    broker_order_id: Optional[str] = None
    message: Optional[str] = None

@dataclass
class Position:
    """Position data structure."""
    symbol: str
    quantity: int
    average_price: float
    current_price: float
    pnl: float
    pnl_percent: float
    product_type: ProductType
    side: str  # LONG/SHORT

@dataclass
class Holding:
    """Holding data structure."""
    symbol: str
    quantity: int
    average_price: float
    current_price: float
    pnl: float
    pnl_percent: float

class BrokerInterface(ABC):
    """Abstract base class for broker integrations."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize broker interface."""
        self.config = config
        self.is_connected = False
        self.session_token = None
    
    @abstractmethod
    async def connect(self) -> bool:
        """Connect to broker API."""
        pass
    
    @abstractmethod
    async def disconnect(self):
        """Disconnect from broker API."""
        pass
    
    @abstractmethod
    async def place_order(self, order: Order) -> Order:
        """Place an order."""
        pass
    
    @abstractmethod
    async def modify_order(self, order_id: str, **kwargs) -> Order:
        """Modify an existing order."""
        pass
    
    @abstractmethod
    async def cancel_order(self, order_id: str) -> bool:
        """Cancel an order."""
        pass
    
    @abstractmethod
    async def get_order_status(self, order_id: str) -> Order:
        """Get order status."""
        pass
    
    @abstractmethod
    async def get_orders(self) -> List[Order]:
        """Get all orders."""
        pass
    
    @abstractmethod
    async def get_positions(self) -> List[Position]:
        """Get current positions."""
        pass
    
    @abstractmethod
    async def get_holdings(self) -> List[Holding]:
        """Get holdings."""
        pass
    
    @abstractmethod
    async def get_balance(self) -> Dict[str, float]:
        """Get account balance."""
        pass
    
    @abstractmethod
    async def get_ltp(self, symbol: str) -> float:
        """Get last traded price."""
        pass

class ZerodhaBroker(BrokerInterface):
    """Zerodha Kite broker integration."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize Zerodha broker."""
        super().__init__(config)
        self.api_key = config.get('api_key')
        self.api_secret = config.get('api_secret')
        self.access_token = config.get('access_token')
        self.base_url = "https://api.kite.trade"
        self.headers = {}
    
    async def connect(self) -> bool:
        """Connect to Zerodha Kite API."""
        try:
            if not self.access_token:
                logger.error("Zerodha access token not provided")
                return False
            
            self.headers = {
                'Authorization': f'token {self.api_key}:{self.access_token}',
                'Content-Type': 'application/json'
            }
            
            # Test connection
            response = requests.get(f"{self.base_url}/user/profile", headers=self.headers)
            
            if response.status_code == 200:
                self.is_connected = True
                logger.info("Connected to Zerodha Kite API")
                return True
            else:
                logger.error(f"Failed to connect to Zerodha: {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"Error connecting to Zerodha: {e}")
            return False
    
    async def disconnect(self):
        """Disconnect from Zerodha API."""
        self.is_connected = False
        self.headers = {}
        logger.info("Disconnected from Zerodha Kite API")
    
    async def place_order(self, order: Order) -> Order:
        """Place order with Zerodha."""
        try:
            if not self.is_connected:
                raise Exception("Not connected to broker")
            
            # Prepare order data
            order_data = {
                'tradingsymbol': order.symbol,
                'exchange': 'NSE',  # Default to NSE
                'transaction_type': order.side.value,
                'quantity': order.quantity,
                'order_type': order.order_type.value,
                'product': order.product_type.value,
                'validity': order.validity
            }
            
            if order.price:
                order_data['price'] = order.price
            
            if order.stop_price:
                order_data['trigger_price'] = order.stop_price
            
            # Place order
            response = requests.post(
                f"{self.base_url}/orders/regular",
                headers=self.headers,
                data=order_data
            )
            
            if response.status_code == 200:
                result = response.json()
                order.broker_order_id = result['data']['order_id']
                order.status = OrderStatus.OPEN
                order.timestamp = datetime.now()
                logger.info(f"Order placed successfully: {order.broker_order_id}")
            else:
                order.status = OrderStatus.REJECTED
                order.message = response.text
                logger.error(f"Order placement failed: {response.text}")
            
            return order
            
        except Exception as e:
            order.status = OrderStatus.REJECTED
            order.message = str(e)
            logger.error(f"Error placing order: {e}")
            return order
    
    async def modify_order(self, order_id: str, **kwargs) -> Order:
        """Modify order with Zerodha."""
        try:
            if not self.is_connected:
                raise Exception("Not connected to broker")
            
            # Prepare modification data
            modify_data = {}
            if 'quantity' in kwargs:
                modify_data['quantity'] = kwargs['quantity']
            if 'price' in kwargs:
                modify_data['price'] = kwargs['price']
            if 'order_type' in kwargs:
                modify_data['order_type'] = kwargs['order_type']
            
            response = requests.put(
                f"{self.base_url}/orders/regular/{order_id}",
                headers=self.headers,
                data=modify_data
            )
            
            if response.status_code == 200:
                logger.info(f"Order modified successfully: {order_id}")
                return await self.get_order_status(order_id)
            else:
                logger.error(f"Order modification failed: {response.text}")
                raise Exception(f"Order modification failed: {response.text}")
                
        except Exception as e:
            logger.error(f"Error modifying order: {e}")
            raise
    
    async def cancel_order(self, order_id: str) -> bool:
        """Cancel order with Zerodha."""
        try:
            if not self.is_connected:
                raise Exception("Not connected to broker")
            
            response = requests.delete(
                f"{self.base_url}/orders/regular/{order_id}",
                headers=self.headers
            )
            
            if response.status_code == 200:
                logger.info(f"Order cancelled successfully: {order_id}")
                return True
            else:
                logger.error(f"Order cancellation failed: {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"Error cancelling order: {e}")
            return False
    
    async def get_order_status(self, order_id: str) -> Order:
        """Get order status from Zerodha."""
        try:
            if not self.is_connected:
                raise Exception("Not connected to broker")
            
            response = requests.get(f"{self.base_url}/orders", headers=self.headers)
            
            if response.status_code == 200:
                orders = response.json()['data']
                
                for order_data in orders:
                    if order_data['order_id'] == order_id:
                        return self._parse_zerodha_order(order_data)
                
                raise Exception(f"Order not found: {order_id}")
            else:
                raise Exception(f"Failed to get orders: {response.text}")
                
        except Exception as e:
            logger.error(f"Error getting order status: {e}")
            raise
    
    async def get_orders(self) -> List[Order]:
        """Get all orders from Zerodha."""
        try:
            if not self.is_connected:
                raise Exception("Not connected to broker")
            
            response = requests.get(f"{self.base_url}/orders", headers=self.headers)
            
            if response.status_code == 200:
                orders_data = response.json()['data']
                orders = []
                
                for order_data in orders_data:
                    orders.append(self._parse_zerodha_order(order_data))
                
                return orders
            else:
                raise Exception(f"Failed to get orders: {response.text}")
                
        except Exception as e:
            logger.error(f"Error getting orders: {e}")
            return []
    
    async def get_positions(self) -> List[Position]:
        """Get positions from Zerodha."""
        try:
            if not self.is_connected:
                raise Exception("Not connected to broker")
            
            response = requests.get(f"{self.base_url}/portfolio/positions", headers=self.headers)
            
            if response.status_code == 200:
                positions_data = response.json()['data']
                positions = []
                
                for pos_data in positions_data['net']:
                    if pos_data['quantity'] != 0:  # Only non-zero positions
                        position = Position(
                            symbol=pos_data['tradingsymbol'],
                            quantity=pos_data['quantity'],
                            average_price=pos_data['average_price'],
                            current_price=pos_data['last_price'],
                            pnl=pos_data['pnl'],
                            pnl_percent=(pos_data['pnl'] / (pos_data['average_price'] * abs(pos_data['quantity']))) * 100,
                            product_type=ProductType(pos_data['product']),
                            side='LONG' if pos_data['quantity'] > 0 else 'SHORT'
                        )
                        positions.append(position)
                
                return positions
            else:
                raise Exception(f"Failed to get positions: {response.text}")
                
        except Exception as e:
            logger.error(f"Error getting positions: {e}")
            return []
    
    async def get_holdings(self) -> List[Holding]:
        """Get holdings from Zerodha."""
        try:
            if not self.is_connected:
                raise Exception("Not connected to broker")
            
            response = requests.get(f"{self.base_url}/portfolio/holdings", headers=self.headers)
            
            if response.status_code == 200:
                holdings_data = response.json()['data']
                holdings = []
                
                for holding_data in holdings_data:
                    if holding_data['quantity'] > 0:
                        holding = Holding(
                            symbol=holding_data['tradingsymbol'],
                            quantity=holding_data['quantity'],
                            average_price=holding_data['average_price'],
                            current_price=holding_data['last_price'],
                            pnl=holding_data['pnl'],
                            pnl_percent=(holding_data['pnl'] / (holding_data['average_price'] * holding_data['quantity'])) * 100
                        )
                        holdings.append(holding)
                
                return holdings
            else:
                raise Exception(f"Failed to get holdings: {response.text}")
                
        except Exception as e:
            logger.error(f"Error getting holdings: {e}")
            return []
    
    async def get_balance(self) -> Dict[str, float]:
        """Get account balance from Zerodha."""
        try:
            if not self.is_connected:
                raise Exception("Not connected to broker")
            
            response = requests.get(f"{self.base_url}/user/margins", headers=self.headers)
            
            if response.status_code == 200:
                margins = response.json()['data']
                equity_margin = margins['equity']
                
                return {
                    'available_cash': equity_margin['available']['cash'],
                    'used_margin': equity_margin['used']['debits'],
                    'total_margin': equity_margin['available']['cash'] + equity_margin['used']['debits']
                }
            else:
                raise Exception(f"Failed to get balance: {response.text}")
                
        except Exception as e:
            logger.error(f"Error getting balance: {e}")
            return {}
    
    async def get_ltp(self, symbol: str) -> float:
        """Get last traded price from Zerodha."""
        try:
            if not self.is_connected:
                raise Exception("Not connected to broker")
            
            # Format symbol for Zerodha
            instrument_token = f"NSE:{symbol}"
            
            response = requests.get(
                f"{self.base_url}/quote/ltp",
                headers=self.headers,
                params={'i': instrument_token}
            )
            
            if response.status_code == 200:
                data = response.json()['data']
                return data[instrument_token]['last_price']
            else:
                raise Exception(f"Failed to get LTP: {response.text}")
                
        except Exception as e:
            logger.error(f"Error getting LTP for {symbol}: {e}")
            return 0.0
    
    def _parse_zerodha_order(self, order_data: Dict[str, Any]) -> Order:
        """Parse Zerodha order data to Order object."""
        status_mapping = {
            'OPEN': OrderStatus.OPEN,
            'COMPLETE': OrderStatus.COMPLETE,
            'CANCELLED': OrderStatus.CANCELLED,
            'REJECTED': OrderStatus.REJECTED
        }
        
        return Order(
            symbol=order_data['tradingsymbol'],
            side=OrderSide(order_data['transaction_type']),
            quantity=order_data['quantity'],
            order_type=OrderType(order_data['order_type']),
            price=order_data.get('price'),
            stop_price=order_data.get('trigger_price'),
            product_type=ProductType(order_data['product']),
            validity=order_data['validity'],
            broker_order_id=order_data['order_id'],
            status=status_mapping.get(order_data['status'], OrderStatus.PENDING),
            filled_quantity=order_data['filled_quantity'],
            average_price=order_data.get('average_price'),
            timestamp=datetime.fromisoformat(order_data['order_timestamp'].replace('Z', '+00:00')) if order_data.get('order_timestamp') else None
        )

class MockBroker(BrokerInterface):
    """Mock broker for testing and simulation."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize mock broker."""
        super().__init__(config)
        self.orders: Dict[str, Order] = {}
        self.positions: List[Position] = []
        self.holdings: List[Holding] = []
        self.balance = {
            'available_cash': 1000000.0,
            'used_margin': 0.0,
            'total_margin': 1000000.0
        }
        self.order_counter = 1
    
    async def connect(self) -> bool:
        """Connect to mock broker."""
        self.is_connected = True
        logger.info("Connected to Mock Broker")
        return True
    
    async def disconnect(self):
        """Disconnect from mock broker."""
        self.is_connected = False
        logger.info("Disconnected from Mock Broker")
    
    async def place_order(self, order: Order) -> Order:
        """Place order with mock broker."""
        try:
            if not self.is_connected:
                raise Exception("Not connected to broker")
            
            # Generate order ID
            order.broker_order_id = f"MOCK_{self.order_counter:06d}"
            self.order_counter += 1
            
            # Simulate order execution
            order.status = OrderStatus.COMPLETE
            order.filled_quantity = order.quantity
            order.average_price = order.price or await self.get_ltp(order.symbol)
            order.timestamp = datetime.now()
            
            # Store order
            self.orders[order.broker_order_id] = order
            
            # Update positions
            await self._update_positions(order)
            
            logger.info(f"Mock order placed: {order.broker_order_id}")
            return order
            
        except Exception as e:
            order.status = OrderStatus.REJECTED
            order.message = str(e)
            logger.error(f"Mock order failed: {e}")
            return order
    
    async def modify_order(self, order_id: str, **kwargs) -> Order:
        """Modify mock order."""
        if order_id in self.orders:
            order = self.orders[order_id]
            
            if 'quantity' in kwargs:
                order.quantity = kwargs['quantity']
            if 'price' in kwargs:
                order.price = kwargs['price']
            
            logger.info(f"Mock order modified: {order_id}")
            return order
        else:
            raise Exception(f"Order not found: {order_id}")
    
    async def cancel_order(self, order_id: str) -> bool:
        """Cancel mock order."""
        if order_id in self.orders:
            self.orders[order_id].status = OrderStatus.CANCELLED
            logger.info(f"Mock order cancelled: {order_id}")
            return True
        return False
    
    async def get_order_status(self, order_id: str) -> Order:
        """Get mock order status."""
        if order_id in self.orders:
            return self.orders[order_id]
        else:
            raise Exception(f"Order not found: {order_id}")
    
    async def get_orders(self) -> List[Order]:
        """Get all mock orders."""
        return list(self.orders.values())
    
    async def get_positions(self) -> List[Position]:
        """Get mock positions."""
        return self.positions
    
    async def get_holdings(self) -> List[Holding]:
        """Get mock holdings."""
        return self.holdings
    
    async def get_balance(self) -> Dict[str, float]:
        """Get mock balance."""
        return self.balance
    
    async def get_ltp(self, symbol: str) -> float:
        """Get mock LTP."""
        # Return mock prices based on symbol
        mock_prices = {
            'RELIANCE': 2520.0,
            'TCS': 3950.0,
            'INFY': 1680.0,
            'HDFCBANK': 1580.0,
            'ICICIBANK': 980.0
        }
        return mock_prices.get(symbol, 1000.0)
    
    async def _update_positions(self, order: Order):
        """Update positions after order execution."""
        # Find existing position
        existing_position = None
        for pos in self.positions:
            if pos.symbol == order.symbol and pos.product_type == order.product_type:
                existing_position = pos
                break
        
        if existing_position:
            # Update existing position
            if order.side == OrderSide.BUY:
                new_quantity = existing_position.quantity + order.filled_quantity
                new_avg_price = ((existing_position.average_price * existing_position.quantity) + 
                               (order.average_price * order.filled_quantity)) / new_quantity
                existing_position.quantity = new_quantity
                existing_position.average_price = new_avg_price
            else:  # SELL
                existing_position.quantity -= order.filled_quantity
                
                # Remove position if quantity becomes zero
                if existing_position.quantity <= 0:
                    self.positions.remove(existing_position)
        else:
            # Create new position
            if order.side == OrderSide.BUY:
                new_position = Position(
                    symbol=order.symbol,
                    quantity=order.filled_quantity,
                    average_price=order.average_price,
                    current_price=order.average_price,
                    pnl=0.0,
                    pnl_percent=0.0,
                    product_type=order.product_type,
                    side='LONG'
                )
                self.positions.append(new_position)

class BrokerFactory:
    """Factory for creating broker instances."""
    
    @staticmethod
    def create_broker(broker_name: str, config: Dict[str, Any]) -> BrokerInterface:
        """Create broker instance."""
        broker_name = broker_name.lower()
        
        if broker_name == 'zerodha':
            return ZerodhaBroker(config)
        elif broker_name == 'mock':
            return MockBroker(config)
        else:
            raise ValueError(f"Unsupported broker: {broker_name}")

# Global broker instance
_broker_instance: Optional[BrokerInterface] = None

def get_broker() -> BrokerInterface:
    """Get current broker instance."""
    global _broker_instance
    
    if _broker_instance is None:
        # Initialize broker from config
        broker_config = config.get_broker_config()
        broker_name = broker_config.get('broker_name', 'mock')
        
        _broker_instance = BrokerFactory.create_broker(broker_name, broker_config)
    
    return _broker_instance

async def initialize_broker() -> bool:
    """Initialize broker connection."""
    try:
        broker = get_broker()
        return await broker.connect()
    except Exception as e:
        logger.error(f"Failed to initialize broker: {e}")
        return False
