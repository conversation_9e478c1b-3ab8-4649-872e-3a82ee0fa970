"""CRUD operations for database models."""

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc, func
from typing import List, Optional, Dict, Any
from datetime import date, datetime, timedelta
from decimal import Decimal

from .models import (
    StockMetadata, DailyPrice, CorporateAction,
    StrategySignal, BacktestResult, PortfolioPosition, TradeHistory,
    RLModel, RLTrainingHistory, RLParameterAdaptation
)
from ..utils.logger import get_logger

logger = get_logger(__name__)

class StockCRUD:
    """CRUD operations for stock metadata."""
    
    @staticmethod
    def create_stock(db: Session, stock_data: Dict[str, Any]) -> StockMetadata:
        """Create a new stock entry."""
        stock = StockMetadata(**stock_data)
        db.add(stock)
        db.flush()
        return stock
    
    @staticmethod
    def get_stock_by_symbol(db: Session, symbol: str) -> Optional[StockMetadata]:
        """Get stock by symbol."""
        return db.query(StockMetadata).filter(StockMetadata.symbol == symbol).first()
    
    @staticmethod
    def get_active_stocks(db: Session) -> List[StockMetadata]:
        """Get all active stocks."""
        return db.query(StockMetadata).filter(StockMetadata.is_active == True).all()
    
    @staticmethod
    def get_nifty50_stocks(db: Session) -> List[StockMetadata]:
        """Get Nifty 50 stocks."""
        return db.query(StockMetadata).filter(
            and_(StockMetadata.is_nifty50 == True, StockMetadata.is_active == True)
        ).all()
    
    @staticmethod
    def update_stock(db: Session, stock_id: int, update_data: Dict[str, Any]) -> Optional[StockMetadata]:
        """Update stock metadata."""
        stock = db.query(StockMetadata).filter(StockMetadata.id == stock_id).first()
        if stock:
            for key, value in update_data.items():
                setattr(stock, key, value)
            db.flush()
        return stock

class PriceCRUD:
    """CRUD operations for daily price data."""
    
    @staticmethod
    def create_price_record(db: Session, price_data: Dict[str, Any]) -> DailyPrice:
        """Create a new price record."""
        price = DailyPrice(**price_data)
        db.add(price)
        db.flush()
        return price
    
    @staticmethod
    def get_latest_price(db: Session, stock_id: int) -> Optional[DailyPrice]:
        """Get latest price for a stock."""
        return db.query(DailyPrice).filter(
            DailyPrice.stock_id == stock_id
        ).order_by(desc(DailyPrice.date)).first()
    
    @staticmethod
    def get_price_history(
        db: Session, 
        stock_id: int, 
        start_date: date, 
        end_date: date
    ) -> List[DailyPrice]:
        """Get price history for a date range."""
        return db.query(DailyPrice).filter(
            and_(
                DailyPrice.stock_id == stock_id,
                DailyPrice.date >= start_date,
                DailyPrice.date <= end_date
            )
        ).order_by(asc(DailyPrice.date)).all()
    
    @staticmethod
    def get_missing_dates(
        db: Session, 
        stock_id: int, 
        start_date: date, 
        end_date: date
    ) -> List[date]:
        """Get dates with missing price data."""
        existing_dates = db.query(DailyPrice.date).filter(
            and_(
                DailyPrice.stock_id == stock_id,
                DailyPrice.date >= start_date,
                DailyPrice.date <= end_date
            )
        ).all()
        
        existing_dates_set = {d[0] for d in existing_dates}
        
        # Generate all business days in range
        current_date = start_date
        all_dates = []
        while current_date <= end_date:
            if current_date.weekday() < 5:  # Monday = 0, Friday = 4
                all_dates.append(current_date)
            current_date += timedelta(days=1)
        
        return [d for d in all_dates if d not in existing_dates_set]
    
    @staticmethod
    def bulk_upsert_prices(db: Session, price_records: List[Dict[str, Any]]) -> int:
        """Bulk insert/update price records."""
        count = 0
        for price_data in price_records:
            existing = db.query(DailyPrice).filter(
                and_(
                    DailyPrice.stock_id == price_data['stock_id'],
                    DailyPrice.date == price_data['date']
                )
            ).first()
            
            if existing:
                for key, value in price_data.items():
                    setattr(existing, key, value)
            else:
                db.add(DailyPrice(**price_data))
            count += 1
        
        db.flush()
        return count

class CorporateActionCRUD:
    """CRUD operations for corporate actions."""
    
    @staticmethod
    def create_corporate_action(db: Session, action_data: Dict[str, Any]) -> CorporateAction:
        """Create a new corporate action."""
        action = CorporateAction(**action_data)
        db.add(action)
        db.flush()
        return action
    
    @staticmethod
    def get_pending_actions(db: Session) -> List[CorporateAction]:
        """Get unprocessed corporate actions."""
        return db.query(CorporateAction).filter(
            CorporateAction.is_processed == False
        ).order_by(asc(CorporateAction.ex_date)).all()
    
    @staticmethod
    def get_actions_for_stock(
        db: Session, 
        stock_id: int, 
        start_date: Optional[date] = None
    ) -> List[CorporateAction]:
        """Get corporate actions for a stock."""
        query = db.query(CorporateAction).filter(CorporateAction.stock_id == stock_id)
        
        if start_date:
            query = query.filter(CorporateAction.ex_date >= start_date)
        
        return query.order_by(asc(CorporateAction.ex_date)).all()

class SignalCRUD:
    """CRUD operations for strategy signals."""
    
    @staticmethod
    def create_signal(db: Session, signal_data: Dict[str, Any]) -> StrategySignal:
        """Create a new trading signal."""
        signal = StrategySignal(**signal_data)
        db.add(signal)
        db.flush()
        return signal
    
    @staticmethod
    def get_latest_signals(
        db: Session, 
        strategy_name: Optional[str] = None,
        signal_type: Optional[str] = None,
        limit: int = 100
    ) -> List[StrategySignal]:
        """Get latest trading signals."""
        query = db.query(StrategySignal).filter(StrategySignal.is_valid == True)
        
        if strategy_name:
            query = query.filter(StrategySignal.strategy_name == strategy_name)
        
        if signal_type:
            query = query.filter(StrategySignal.signal_type == signal_type)
        
        return query.order_by(desc(StrategySignal.signal_time)).limit(limit).all()
    
    @staticmethod
    def get_signals_for_date(
        db: Session, 
        signal_date: date,
        strategy_name: Optional[str] = None
    ) -> List[StrategySignal]:
        """Get signals for a specific date."""
        query = db.query(StrategySignal).filter(
            and_(
                StrategySignal.signal_date == signal_date,
                StrategySignal.is_valid == True
            )
        )
        
        if strategy_name:
            query = query.filter(StrategySignal.strategy_name == strategy_name)
        
        return query.all()

class BacktestCRUD:
    """CRUD operations for backtest results."""
    
    @staticmethod
    def create_backtest_result(db: Session, result_data: Dict[str, Any]) -> BacktestResult:
        """Create a new backtest result."""
        result = BacktestResult(**result_data)
        db.add(result)
        db.flush()
        return result
    
    @staticmethod
    def get_strategy_results(
        db: Session, 
        strategy_name: str,
        limit: int = 10
    ) -> List[BacktestResult]:
        """Get backtest results for a strategy."""
        return db.query(BacktestResult).filter(
            BacktestResult.strategy_name == strategy_name
        ).order_by(desc(BacktestResult.created_at)).limit(limit).all()
    
    @staticmethod
    def get_best_performing_strategies(db: Session, metric: str = 'sharpe_ratio') -> List[BacktestResult]:
        """Get best performing strategies by metric."""
        order_column = getattr(BacktestResult, metric, BacktestResult.sharpe_ratio)
        return db.query(BacktestResult).order_by(desc(order_column)).limit(10).all()

class RLModelCRUD:
    """CRUD operations for RL models."""

    @staticmethod
    def create_or_update_model(db: Session, model_data: Dict[str, Any]) -> RLModel:
        """Create or update an RL model."""
        existing = db.query(RLModel).filter(
            and_(
                RLModel.strategy_name == model_data['strategy_name'],
                RLModel.is_active == True
            )
        ).first()

        if existing:
            # Update existing model
            for key, value in model_data.items():
                setattr(existing, key, value)
            existing.version += 1
            db.flush()
            return existing
        else:
            # Create new model
            model = RLModel(**model_data)
            db.add(model)
            db.flush()
            return model

    @staticmethod
    def get_active_model(db: Session, strategy_name: str) -> Optional[RLModel]:
        """Get active RL model for a strategy."""
        return db.query(RLModel).filter(
            and_(
                RLModel.strategy_name == strategy_name,
                RLModel.is_active == True
            )
        ).first()

    @staticmethod
    def get_all_active_models(db: Session) -> List[RLModel]:
        """Get all active RL models."""
        return db.query(RLModel).filter(RLModel.is_active == True).all()

    @staticmethod
    def deactivate_model(db: Session, model_id: int) -> bool:
        """Deactivate an RL model."""
        model = db.query(RLModel).filter(RLModel.id == model_id).first()
        if model:
            model.is_active = False
            db.flush()
            return True
        return False

class RLTrainingHistoryCRUD:
    """CRUD operations for RL training history."""

    @staticmethod
    def create_training_record(db: Session, training_data: Dict[str, Any]) -> RLTrainingHistory:
        """Create a new training history record."""
        record = RLTrainingHistory(**training_data)
        db.add(record)
        db.flush()
        return record

    @staticmethod
    def get_training_history(
        db: Session,
        strategy_name: str,
        limit: int = 50
    ) -> List[RLTrainingHistory]:
        """Get training history for a strategy."""
        return db.query(RLTrainingHistory).filter(
            RLTrainingHistory.strategy_name == strategy_name
        ).order_by(desc(RLTrainingHistory.training_date)).limit(limit).all()

    @staticmethod
    def get_recent_training_performance(db: Session, days: int = 30) -> List[RLTrainingHistory]:
        """Get recent training performance across all strategies."""
        cutoff_date = datetime.now().date() - timedelta(days=days)
        return db.query(RLTrainingHistory).filter(
            RLTrainingHistory.training_date >= cutoff_date
        ).order_by(desc(RLTrainingHistory.avg_reward)).all()

class RLParameterAdaptationCRUD:
    """CRUD operations for RL parameter adaptations."""

    @staticmethod
    def create_adaptation_record(db: Session, adaptation_data: Dict[str, Any]) -> RLParameterAdaptation:
        """Create a new parameter adaptation record."""
        record = RLParameterAdaptation(**adaptation_data)
        db.add(record)
        db.flush()
        return record

    @staticmethod
    def get_recent_adaptations(
        db: Session,
        strategy_name: str,
        stock_id: Optional[int] = None,
        days: int = 7
    ) -> List[RLParameterAdaptation]:
        """Get recent parameter adaptations."""
        cutoff_date = datetime.now().date() - timedelta(days=days)

        query = db.query(RLParameterAdaptation).filter(
            and_(
                RLParameterAdaptation.strategy_name == strategy_name,
                RLParameterAdaptation.adaptation_date >= cutoff_date
            )
        )

        if stock_id:
            query = query.filter(RLParameterAdaptation.stock_id == stock_id)

        return query.order_by(desc(RLParameterAdaptation.adaptation_date)).all()

    @staticmethod
    def get_adaptation_performance(
        db: Session,
        strategy_name: str,
        days: int = 30
    ) -> Dict[str, Any]:
        """Get adaptation performance metrics."""
        cutoff_date = datetime.now().date() - timedelta(days=days)

        adaptations = db.query(RLParameterAdaptation).filter(
            and_(
                RLParameterAdaptation.strategy_name == strategy_name,
                RLParameterAdaptation.adaptation_date >= cutoff_date,
                RLParameterAdaptation.actual_performance.isnot(None)
            )
        ).all()

        if not adaptations:
            return {}

        total_adaptations = len(adaptations)
        successful_adaptations = len([a for a in adaptations if a.actual_performance > 0])
        avg_improvement = sum([a.actual_performance for a in adaptations]) / total_adaptations

        return {
            'total_adaptations': total_adaptations,
            'successful_adaptations': successful_adaptations,
            'success_rate': successful_adaptations / total_adaptations * 100,
            'avg_improvement': float(avg_improvement),
            'period_days': days
        }
