"""
Backtesting Integration - Integrates backtesting framework with main system.
Provides scheduled backtesting and performance monitoring.
"""

import asyncio
from datetime import datetime, date, timedelta
from typing import Dict, List, Any, Optional
import schedule
import json

from .backtest_runner import backtest_runner
from .backtest_utils import ReportGenerator, BacktestValidator
from ..data.database import db_manager
from ..data.crud import BacktestCRUD
from ..utils.logger import get_logger
from ..utils.config import config

logger = get_logger(__name__)

class BacktestingIntegration:
    """Integration layer for backtesting framework."""
    
    def __init__(self):
        """Initialize backtesting integration."""
        self.runner = backtest_runner
        self.report_generator = ReportGenerator()
        self.validator = BacktestValidator()
        
        # Configuration
        self.config = config.get_backtesting_config()
        self.auto_backtest_enabled = self.config.get('auto_backtest_enabled', False)
        self.backtest_frequency = self.config.get('backtest_frequency', 'weekly')
        
        # Setup scheduled backtesting
        if self.auto_backtest_enabled:
            self._setup_scheduled_backtesting()
    
    def _setup_scheduled_backtesting(self):
        """Setup scheduled backtesting tasks."""
        try:
            if self.backtest_frequency == 'daily':
                schedule.every().day.at("18:00").do(self._run_scheduled_backtest)
            elif self.backtest_frequency == 'weekly':
                schedule.every().sunday.at("10:00").do(self._run_scheduled_backtest)
            elif self.backtest_frequency == 'monthly':
                schedule.every().month.do(self._run_scheduled_backtest)
            
            logger.info(f"Scheduled backtesting configured: {self.backtest_frequency}")
            
        except Exception as e:
            logger.error(f"Error setting up scheduled backtesting: {e}")
    
    async def run_comprehensive_backtest(
        self, 
        include_walk_forward: bool = True,
        generate_reports: bool = True
    ) -> Dict[str, Any]:
        """Run comprehensive backtesting suite.
        
        Args:
            include_walk_forward: Whether to include walk-forward analysis
            generate_reports: Whether to generate detailed reports
            
        Returns:
            Comprehensive backtest results
        """
        logger.info("Starting comprehensive backtesting suite")
        
        try:
            start_time = datetime.now()
            
            # Run all strategies backtest
            logger.info("Running all strategies backtest...")
            all_strategies_results = self.runner.run_all_strategies_backtest()
            
            # Run strategy combinations test
            logger.info("Running strategy combinations test...")
            combinations_results = self.runner.run_best_combinations_test()
            
            # Run walk-forward validation if requested
            wf_results = {}
            if include_walk_forward and all_strategies_results.get('summary', {}).get('best_strategy'):
                logger.info("Running walk-forward validation...")
                best_strategy = all_strategies_results['summary']['best_strategy']
                wf_results = self.runner.run_walk_forward_validation(best_strategy)
            
            # Validate results
            validation_results = self._validate_all_results(all_strategies_results)
            
            # Generate reports if requested
            reports = {}
            if generate_reports:
                reports = await self._generate_comprehensive_reports(
                    all_strategies_results, combinations_results, wf_results
                )
            
            # Compile final results
            comprehensive_results = {
                'execution_time': start_time.isoformat(),
                'duration_seconds': (datetime.now() - start_time).total_seconds(),
                'all_strategies': all_strategies_results,
                'combinations': combinations_results,
                'walk_forward_validation': wf_results,
                'validation': validation_results,
                'reports': reports,
                'summary': self._create_comprehensive_summary(
                    all_strategies_results, combinations_results, wf_results
                )
            }
            
            # Store results in database
            await self._store_comprehensive_results(comprehensive_results)
            
            logger.info(f"Comprehensive backtesting completed in {comprehensive_results['duration_seconds']:.2f} seconds")
            return comprehensive_results
            
        except Exception as e:
            logger.error(f"Error in comprehensive backtest: {e}")
            return {'error': str(e)}
    
    async def run_strategy_optimization(
        self, 
        strategy_name: str,
        parameter_ranges: Dict[str, List[Any]],
        optimization_metric: str = 'sharpe_ratio'
    ) -> Dict[str, Any]:
        """Run parameter optimization for a strategy.
        
        Args:
            strategy_name: Strategy to optimize
            parameter_ranges: Dictionary of parameter ranges to test
            optimization_metric: Metric to optimize for
            
        Returns:
            Optimization results
        """
        logger.info(f"Starting parameter optimization for {strategy_name}")
        
        try:
            optimization_results = {
                'strategy_name': strategy_name,
                'parameter_ranges': parameter_ranges,
                'optimization_metric': optimization_metric,
                'results': [],
                'best_parameters': None,
                'best_score': -float('inf')
            }
            
            # Generate parameter combinations
            param_combinations = self._generate_parameter_combinations(parameter_ranges)
            
            logger.info(f"Testing {len(param_combinations)} parameter combinations")
            
            # Test each parameter combination
            for i, params in enumerate(param_combinations):
                logger.info(f"Testing combination {i+1}/{len(param_combinations)}: {params}")
                
                # Run backtest with these parameters
                result = self.runner.engine.run_single_strategy_backtest(
                    strategy_name, 
                    "RELIANCE",  # Use RELIANCE as test symbol
                    date(2022, 1, 1), 
                    date(2023, 12, 31),
                    parameters=params
                )
                
                if 'error' not in result:
                    score = result.get(optimization_metric, 0)
                    
                    optimization_results['results'].append({
                        'parameters': params,
                        'score': score,
                        'full_result': result
                    })
                    
                    # Update best parameters
                    if score > optimization_results['best_score']:
                        optimization_results['best_score'] = score
                        optimization_results['best_parameters'] = params
            
            # Sort results by score
            optimization_results['results'].sort(key=lambda x: x['score'], reverse=True)
            
            logger.info(f"Optimization completed. Best {optimization_metric}: {optimization_results['best_score']:.3f}")
            return optimization_results
            
        except Exception as e:
            logger.error(f"Error in strategy optimization: {e}")
            return {'error': str(e)}
    
    def _generate_parameter_combinations(self, parameter_ranges: Dict[str, List[Any]]) -> List[Dict[str, Any]]:
        """Generate all combinations of parameters."""
        import itertools
        
        keys = list(parameter_ranges.keys())
        values = list(parameter_ranges.values())
        
        combinations = []
        for combination in itertools.product(*values):
            param_dict = dict(zip(keys, combination))
            combinations.append(param_dict)
        
        return combinations
    
    def _validate_all_results(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Validate all backtest results."""
        validation_summary = {
            'total_strategies_tested': 0,
            'valid_results': 0,
            'invalid_results': 0,
            'warnings_count': 0,
            'overfitting_detected': 0,
            'strategy_validations': {}
        }
        
        try:
            for strategy_name, strategy_results in results.get('strategy_results', {}).items():
                strategy_validation = {
                    'symbols_tested': len(strategy_results),
                    'valid_symbols': 0,
                    'invalid_symbols': 0,
                    'symbol_validations': {}
                }
                
                for symbol, result in strategy_results.items():
                    validation = self.validator.validate_results(result)
                    overfitting = self.validator.detect_overfitting(result)
                    
                    strategy_validation['symbol_validations'][symbol] = {
                        'validation': validation,
                        'overfitting': overfitting
                    }
                    
                    if validation['is_valid']:
                        strategy_validation['valid_symbols'] += 1
                        validation_summary['valid_results'] += 1
                    else:
                        strategy_validation['invalid_symbols'] += 1
                        validation_summary['invalid_results'] += 1
                    
                    validation_summary['warnings_count'] += len(validation.get('warnings', []))
                    
                    if overfitting.get('overfitting_detected', False):
                        validation_summary['overfitting_detected'] += 1
                
                validation_summary['strategy_validations'][strategy_name] = strategy_validation
                validation_summary['total_strategies_tested'] += 1
            
            return validation_summary
            
        except Exception as e:
            logger.error(f"Error validating results: {e}")
            return {'error': str(e)}
    
    async def _generate_comprehensive_reports(
        self, 
        all_strategies: Dict[str, Any],
        combinations: Dict[str, Any],
        walk_forward: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate comprehensive reports."""
        reports = {
            'html_reports': [],
            'csv_reports': [],
            'summary_report': None
        }
        
        try:
            # Generate individual strategy reports
            for strategy_name, strategy_results in all_strategies.get('strategy_results', {}).items():
                for symbol, result in strategy_results.items():
                    if 'error' not in result:
                        # Generate HTML report
                        html_report = self.report_generator.generate_html_report(result)
                        if html_report:
                            reports['html_reports'].append(html_report)
                        
                        # Generate CSV report
                        csv_report = self.report_generator.generate_csv_report(result)
                        if csv_report:
                            reports['csv_reports'].append(csv_report)
            
            # Generate summary report
            summary_data = {
                'execution_date': datetime.now().isoformat(),
                'strategies_tested': len(all_strategies.get('strategy_results', {})),
                'best_strategy': all_strategies.get('summary', {}).get('best_strategy'),
                'best_return': all_strategies.get('summary', {}).get('best_return'),
                'combinations_tested': len(combinations) if combinations else 0,
                'walk_forward_completed': bool(walk_forward)
            }
            
            summary_report = self.report_generator.generate_html_report(summary_data)
            reports['summary_report'] = summary_report
            
            return reports
            
        except Exception as e:
            logger.error(f"Error generating reports: {e}")
            return {'error': str(e)}
    
    def _create_comprehensive_summary(
        self, 
        all_strategies: Dict[str, Any],
        combinations: Dict[str, Any],
        walk_forward: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Create comprehensive summary of all results."""
        try:
            summary = {
                'execution_date': datetime.now().isoformat(),
                'strategies_performance': {},
                'best_overall_strategy': None,
                'best_overall_return': -float('inf'),
                'recommendations': []
            }
            
            # Analyze strategy performance
            for strategy_name, strategy_results in all_strategies.get('strategy_results', {}).items():
                if strategy_results:
                    returns = [result['total_return'] for result in strategy_results.values() if 'error' not in result]
                    sharpe_ratios = [result['sharpe_ratio'] for result in strategy_results.values() if 'error' not in result]
                    
                    if returns:
                        avg_return = sum(returns) / len(returns)
                        avg_sharpe = sum(sharpe_ratios) / len(sharpe_ratios)
                        
                        summary['strategies_performance'][strategy_name] = {
                            'average_return': avg_return,
                            'average_sharpe': avg_sharpe,
                            'symbols_tested': len(strategy_results),
                            'success_rate': len([r for r in returns if r > 0]) / len(returns) * 100
                        }
                        
                        if avg_return > summary['best_overall_return']:
                            summary['best_overall_return'] = avg_return
                            summary['best_overall_strategy'] = strategy_name
            
            # Generate recommendations
            if summary['best_overall_strategy']:
                summary['recommendations'].append(
                    f"Consider focusing on {summary['best_overall_strategy']} strategy "
                    f"with {summary['best_overall_return']:.2f}% average return"
                )
            
            if walk_forward.get('validation_summary', {}).get('validation_passed'):
                summary['recommendations'].append(
                    "Walk-forward validation passed - strategy shows consistent performance"
                )
            
            return summary
            
        except Exception as e:
            logger.error(f"Error creating comprehensive summary: {e}")
            return {'error': str(e)}
    
    async def _store_comprehensive_results(self, results: Dict[str, Any]):
        """Store comprehensive results in database."""
        try:
            # Store summary in database
            with db_manager.get_session() as db:
                summary_data = {
                    'strategy_name': 'comprehensive_backtest',
                    'start_date': date.today() - timedelta(days=365),
                    'end_date': date.today(),
                    'initial_capital': 1000000,
                    'total_return': results.get('summary', {}).get('best_overall_return', 0),
                    'annual_return': results.get('summary', {}).get('best_overall_return', 0),
                    'sharpe_ratio': 0,
                    'max_drawdown': 0,
                    'calmar_ratio': 0,
                    'total_trades': 0,
                    'winning_trades': 0,
                    'losing_trades': 0,
                    'win_rate': 0,
                    'avg_win': 0,
                    'avg_loss': 0,
                    'profit_factor': 0,
                    'parameters': json.dumps(results.get('summary', {}))
                }
                
                BacktestCRUD.create_backtest_result(db, summary_data)
                logger.info("Comprehensive backtest results stored in database")
                
        except Exception as e:
            logger.error(f"Error storing comprehensive results: {e}")
    
    def _run_scheduled_backtest(self):
        """Run scheduled backtest."""
        logger.info("Running scheduled backtest")
        
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            result = loop.run_until_complete(
                self.run_comprehensive_backtest(
                    include_walk_forward=False,  # Skip WF for scheduled runs
                    generate_reports=True
                )
            )
            
            loop.close()
            
            if 'error' in result:
                logger.error(f"Scheduled backtest failed: {result['error']}")
            else:
                logger.info("Scheduled backtest completed successfully")
                
        except Exception as e:
            logger.error(f"Error in scheduled backtest: {e}")
    
    def get_latest_backtest_results(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get latest backtest results from database.
        
        Args:
            limit: Maximum number of results to return
            
        Returns:
            List of latest backtest results
        """
        try:
            with db_manager.get_session() as db:
                results = BacktestCRUD.get_strategy_results('comprehensive_backtest', limit)
                
                return [{
                    'id': result.id,
                    'strategy_name': result.strategy_name,
                    'start_date': result.start_date.isoformat(),
                    'end_date': result.end_date.isoformat(),
                    'total_return': float(result.total_return),
                    'annual_return': float(result.annual_return),
                    'sharpe_ratio': float(result.sharpe_ratio),
                    'max_drawdown': float(result.max_drawdown),
                    'created_at': result.created_at.isoformat()
                } for result in results]
                
        except Exception as e:
            logger.error(f"Error getting latest backtest results: {e}")
            return []

# Global integration instance
backtesting_integration = BacktestingIntegration()

async def run_full_backtesting_suite():
    """Run full backtesting suite - main entry point."""
    return await backtesting_integration.run_comprehensive_backtest()

if __name__ == "__main__":
    # Run full backtesting suite
    results = asyncio.run(run_full_backtesting_suite())
    print("Full backtesting suite completed. Check logs and reports for details.")
