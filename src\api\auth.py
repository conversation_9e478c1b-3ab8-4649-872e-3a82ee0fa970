"""
Authentication and Authorization System.
Handles user authentication, JWT tokens, and access control.
"""

import jwt
import bcrypt
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from fastapi import HTTPException, status
import secrets

from ..data.database import db_manager
from ..utils.logger import get_logger
from ..utils.config import config

logger = get_logger(__name__)

class AuthManager:
    """Authentication manager."""
    
    def __init__(self):
        """Initialize authentication manager."""
        self.secret_key = config.get('jwt_secret_key', secrets.token_urlsafe(32))
        self.algorithm = "HS256"
        self.access_token_expire_minutes = config.get('access_token_expire_minutes', 1440)  # 24 hours
        
        # Default admin user
        self.default_admin = {
            'username': 'admin',
            'password': 'admin123',  # Should be changed in production
            'email': '<EMAIL>',
            'full_name': 'System Administrator',
            'is_admin': True
        }
    
    async def initialize(self):
        """Initialize authentication system."""
        try:
            # Create users table if not exists
            await self._create_users_table()
            
            # Create default admin user
            await self._create_default_admin()
            
            logger.info("Authentication system initialized")
            
        except Exception as e:
            logger.error(f"Error initializing authentication: {e}")
            raise
    
    async def _create_users_table(self):
        """Create users table."""
        try:
            with db_manager.get_session() as db:
                # Create users table using raw SQL for simplicity
                db.execute("""
                    CREATE TABLE IF NOT EXISTS users (
                        id SERIAL PRIMARY KEY,
                        username VARCHAR(50) UNIQUE NOT NULL,
                        email VARCHAR(100) UNIQUE NOT NULL,
                        password_hash VARCHAR(255) NOT NULL,
                        full_name VARCHAR(100),
                        is_admin BOOLEAN DEFAULT FALSE,
                        is_active BOOLEAN DEFAULT TRUE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        last_login TIMESTAMP
                    )
                """)
                db.commit()
                
        except Exception as e:
            logger.error(f"Error creating users table: {e}")
            raise
    
    async def _create_default_admin(self):
        """Create default admin user if not exists."""
        try:
            with db_manager.get_session() as db:
                # Check if admin user exists
                result = db.execute(
                    "SELECT id FROM users WHERE username = %s",
                    (self.default_admin['username'],)
                ).fetchone()
                
                if not result:
                    # Create admin user
                    password_hash = self._hash_password(self.default_admin['password'])
                    
                    db.execute("""
                        INSERT INTO users (username, email, password_hash, full_name, is_admin)
                        VALUES (%s, %s, %s, %s, %s)
                    """, (
                        self.default_admin['username'],
                        self.default_admin['email'],
                        password_hash,
                        self.default_admin['full_name'],
                        self.default_admin['is_admin']
                    ))
                    db.commit()
                    
                    logger.info("Default admin user created")
                
        except Exception as e:
            logger.error(f"Error creating default admin: {e}")
            raise
    
    def _hash_password(self, password: str) -> str:
        """Hash password using bcrypt."""
        salt = bcrypt.gensalt()
        return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
    
    def _verify_password(self, password: str, password_hash: str) -> bool:
        """Verify password against hash."""
        return bcrypt.checkpw(password.encode('utf-8'), password_hash.encode('utf-8'))
    
    def _create_access_token(self, data: Dict[str, Any]) -> str:
        """Create JWT access token."""
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        to_encode.update({"exp": expire})
        
        return jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
    
    async def authenticate_user(self, username: str, password: str) -> Optional[Dict[str, Any]]:
        """Authenticate user credentials."""
        try:
            with db_manager.get_session() as db:
                result = db.execute("""
                    SELECT id, username, email, password_hash, full_name, is_admin, is_active
                    FROM users WHERE username = %s AND is_active = TRUE
                """, (username,)).fetchone()
                
                if not result:
                    return None
                
                user_data = {
                    'id': result[0],
                    'username': result[1],
                    'email': result[2],
                    'password_hash': result[3],
                    'full_name': result[4],
                    'is_admin': result[5],
                    'is_active': result[6]
                }
                
                # Verify password
                if not self._verify_password(password, user_data['password_hash']):
                    return None
                
                # Update last login
                db.execute(
                    "UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = %s",
                    (user_data['id'],)
                )
                db.commit()
                
                # Remove password hash from response
                del user_data['password_hash']
                
                return user_data
                
        except Exception as e:
            logger.error(f"Error authenticating user: {e}")
            return None
    
    async def create_user(self, user_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Create new user."""
        try:
            with db_manager.get_session() as db:
                # Check if username or email already exists
                result = db.execute("""
                    SELECT id FROM users WHERE username = %s OR email = %s
                """, (user_data['username'], user_data['email'])).fetchone()
                
                if result:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Username or email already exists"
                    )
                
                # Hash password
                password_hash = self._hash_password(user_data['password'])
                
                # Insert user
                result = db.execute("""
                    INSERT INTO users (username, email, password_hash, full_name, is_admin)
                    VALUES (%s, %s, %s, %s, %s)
                    RETURNING id, username, email, full_name, is_admin, is_active, created_at
                """, (
                    user_data['username'],
                    user_data['email'],
                    password_hash,
                    user_data.get('full_name'),
                    user_data.get('is_admin', False)
                )).fetchone()
                
                db.commit()
                
                return {
                    'id': result[0],
                    'username': result[1],
                    'email': result[2],
                    'full_name': result[3],
                    'is_admin': result[4],
                    'is_active': result[5],
                    'created_at': result[6]
                }
                
        except Exception as e:
            logger.error(f"Error creating user: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create user"
            )
    
    async def login(self, username: str, password: str) -> Dict[str, Any]:
        """Login user and return token."""
        user = await self.authenticate_user(username, password)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect username or password",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Create access token
        access_token = self._create_access_token(
            data={"sub": user['username'], "user_id": user['id']}
        )
        
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "expires_in": self.access_token_expire_minutes * 60,
            "user_info": user
        }
    
    async def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Verify JWT token and return user info."""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            username: str = payload.get("sub")
            user_id: int = payload.get("user_id")
            
            if username is None or user_id is None:
                return None
            
            # Get user from database
            with db_manager.get_session() as db:
                result = db.execute("""
                    SELECT id, username, email, full_name, is_admin, is_active
                    FROM users WHERE id = %s AND username = %s AND is_active = TRUE
                """, (user_id, username)).fetchone()
                
                if not result:
                    return None
                
                return {
                    'id': result[0],
                    'username': result[1],
                    'email': result[2],
                    'full_name': result[3],
                    'is_admin': result[4],
                    'is_active': result[5]
                }
                
        except jwt.ExpiredSignatureError:
            logger.warning("Token has expired")
            return None
        except jwt.JWTError as e:
            logger.warning(f"JWT error: {e}")
            return None
        except Exception as e:
            logger.error(f"Error verifying token: {e}")
            return None
    
    async def get_user_by_id(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Get user by ID."""
        try:
            with db_manager.get_session() as db:
                result = db.execute("""
                    SELECT id, username, email, full_name, is_admin, is_active, created_at, last_login
                    FROM users WHERE id = %s
                """, (user_id,)).fetchone()
                
                if not result:
                    return None
                
                return {
                    'id': result[0],
                    'username': result[1],
                    'email': result[2],
                    'full_name': result[3],
                    'is_admin': result[4],
                    'is_active': result[5],
                    'created_at': result[6],
                    'last_login': result[7]
                }
                
        except Exception as e:
            logger.error(f"Error getting user by ID: {e}")
            return None
    
    async def get_all_users(self) -> List[Dict[str, Any]]:
        """Get all users (admin only)."""
        try:
            with db_manager.get_session() as db:
                results = db.execute("""
                    SELECT id, username, email, full_name, is_admin, is_active, created_at, last_login
                    FROM users ORDER BY created_at DESC
                """).fetchall()
                
                return [{
                    'id': row[0],
                    'username': row[1],
                    'email': row[2],
                    'full_name': row[3],
                    'is_admin': row[4],
                    'is_active': row[5],
                    'created_at': row[6],
                    'last_login': row[7]
                } for row in results]
                
        except Exception as e:
            logger.error(f"Error getting all users: {e}")
            return []
    
    async def update_user(self, user_id: int, update_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Update user information."""
        try:
            with db_manager.get_session() as db:
                # Build update query dynamically
                update_fields = []
                values = []
                
                for field in ['email', 'full_name', 'is_admin', 'is_active']:
                    if field in update_data:
                        update_fields.append(f"{field} = %s")
                        values.append(update_data[field])
                
                if 'password' in update_data:
                    update_fields.append("password_hash = %s")
                    values.append(self._hash_password(update_data['password']))
                
                if not update_fields:
                    return await self.get_user_by_id(user_id)
                
                values.append(user_id)
                
                query = f"""
                    UPDATE users SET {', '.join(update_fields)}
                    WHERE id = %s
                    RETURNING id, username, email, full_name, is_admin, is_active, created_at
                """
                
                result = db.execute(query, values).fetchone()
                db.commit()
                
                if not result:
                    return None
                
                return {
                    'id': result[0],
                    'username': result[1],
                    'email': result[2],
                    'full_name': result[3],
                    'is_admin': result[4],
                    'is_active': result[5],
                    'created_at': result[6]
                }
                
        except Exception as e:
            logger.error(f"Error updating user: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update user"
            )
    
    async def delete_user(self, user_id: int) -> bool:
        """Delete user (soft delete by setting is_active = False)."""
        try:
            with db_manager.get_session() as db:
                result = db.execute(
                    "UPDATE users SET is_active = FALSE WHERE id = %s",
                    (user_id,)
                )
                db.commit()
                
                return result.rowcount > 0
                
        except Exception as e:
            logger.error(f"Error deleting user: {e}")
            return False

# Global auth manager instance
auth_manager = AuthManager()
