# Portfolio Optimization Configuration
# Configuration for portfolio optimization and analytics system

# Default Portfolio Constraints
default_constraints:
  min_weight: 0.01          # Minimum 1% allocation per asset
  max_weight: 0.30          # Maximum 30% allocation per asset
  max_concentration: 0.20   # Maximum 20% concentration in any single asset
  min_positions: 5          # Minimum number of positions
  max_positions: 20         # Maximum number of positions
  
  # Sector concentration limits
  sector_limits:
    Technology: 0.40        # Maximum 40% in technology
    Banking: 0.35           # Maximum 35% in banking
    Energy: 0.25            # Maximum 25% in energy
    Healthcare: 0.20        # Maximum 20% in healthcare
    Consumer: 0.30          # Maximum 30% in consumer goods
    Industrial: 0.25        # Maximum 25% in industrial
    Materials: 0.15         # Maximum 15% in materials
    Utilities: 0.10         # Maximum 10% in utilities
    Telecom: 0.15           # Maximum 15% in telecom
    
  # Risk limits
  turnover_limit: 0.50      # Maximum 50% turnover per rebalancing
  tracking_error_limit: 0.08 # Maximum 8% tracking error

# Optimization Methods Configuration
optimization_methods:
  max_sharpe:
    enabled: true
    risk_free_rate: 0.06    # 6% risk-free rate (Indian context)
    max_iterations: 1000
    tolerance: 1e-9
    
  min_variance:
    enabled: true
    regularization: 1e-8    # Covariance matrix regularization
    max_iterations: 1000
    
  target_return:
    enabled: true
    default_target_return: 0.15  # 15% default target
    min_target_return: 0.05      # 5% minimum target
    max_target_return: 0.30      # 30% maximum target
    
  risk_parity_erc:
    enabled: true
    risk_tolerance: 1e-6    # Risk contribution tolerance
    max_iterations: 1000
    
  risk_parity_hrp:
    enabled: true
    linkage_method: "ward"  # Hierarchical clustering method
    distance_metric: "correlation"
    
  black_litterman:
    enabled: true
    tau: 0.025              # Scaling factor (1/40 for monthly data)
    risk_aversion: 3.0      # Risk aversion parameter
    confidence_scaling: true # Scale uncertainty by confidence
    
  equal_weight:
    enabled: true
    rebalancing_frequency: 90  # Quarterly rebalancing

# Black-Litterman Configuration
black_litterman:
  default_tau: 0.025
  default_risk_aversion: 3.0
  
  # View types and their default parameters
  view_types:
    absolute:
      default_confidence: 0.7
      max_confidence: 0.95
      min_confidence: 0.3
      
    relative:
      default_confidence: 0.6
      max_confidence: 0.90
      min_confidence: 0.3
  
  # Market equilibrium estimation
  market_equilibrium:
    use_market_caps: true
    fallback_method: "equal_weight"
    min_market_cap: 1000    # Minimum ₹1000 Cr market cap

# Rebalancing Configuration
rebalancing:
  # Default rebalancing settings
  default_trigger: "threshold_based"
  default_frequency_days: 30
  default_threshold_percent: 0.05
  default_transaction_cost: 0.001
  default_min_trade_size: 0.02
  default_max_turnover: 0.50
  
  # Trigger-specific settings
  triggers:
    time_based:
      enabled: true
      min_frequency_days: 7
      max_frequency_days: 365
      
    threshold_based:
      enabled: true
      min_threshold: 0.01     # 1% minimum threshold
      max_threshold: 0.20     # 20% maximum threshold
      
    volatility_based:
      enabled: true
      min_volatility: 0.10    # 10% minimum volatility
      max_volatility: 0.50    # 50% maximum volatility
      lookback_days: 20       # 20-day volatility calculation
      
    performance_based:
      enabled: true
      min_performance_threshold: 0.02  # 2% minimum threshold
      max_performance_threshold: 0.15  # 15% maximum threshold
      lookback_days: 20                # 20-day performance calculation
  
  # Transaction cost modeling
  transaction_costs:
    equity:
      brokerage: 0.0005     # 0.05% brokerage
      stt: 0.001            # 0.1% STT
      stamp_duty: 0.00015   # 0.015% stamp duty
      gst: 0.00009          # 0.009% GST on brokerage
      total: 0.00174        # Total ~0.174%
    
    # Market impact costs (simplified)
    market_impact:
      small_trade: 0.0002   # <₹1L
      medium_trade: 0.0005  # ₹1L-₹10L
      large_trade: 0.001    # >₹10L

# Performance Analytics Configuration
performance_analytics:
  # Risk-free rate and market parameters
  risk_free_rate: 0.06      # 6% risk-free rate
  trading_days_per_year: 252
  
  # Rolling metrics configuration
  rolling_metrics:
    default_window: 60      # 60-day rolling window
    min_window: 20          # Minimum 20-day window
    max_window: 252         # Maximum 1-year window
    
    # Available metrics
    available_metrics:
      - "return"
      - "volatility"
      - "sharpe"
      - "sortino"
      - "max_drawdown"
      - "var_95"
      - "cvar_95"
  
  # Attribution analysis
  attribution:
    enabled: true
    benchmark_method: "equal_weight"  # Default benchmark
    sector_attribution: true
    factor_attribution: false        # Requires factor model
  
  # Risk decomposition
  risk_decomposition:
    enabled: true
    factor_model: false              # Simple risk decomposition
    component_var: true
    marginal_contributions: true

# Data Requirements
data_requirements:
  min_lookback_days: 60       # Minimum 60 days of data
  default_lookback_days: 252  # Default 1 year
  max_lookback_days: 1260     # Maximum 5 years
  
  # Data quality checks
  quality_checks:
    min_price: 1.0            # Minimum ₹1 price
    max_price_change: 0.20    # Maximum 20% daily change
    min_volume: 1000          # Minimum 1000 shares volume
    max_missing_days: 10      # Maximum 10 missing days

# Optimization Engine Settings
optimization_engine:
  # Solver settings
  solver:
    method: "SLSQP"           # Sequential Least Squares Programming
    max_iterations: 1000
    tolerance: 1e-9
    
  # Numerical stability
  numerical:
    covariance_regularization: 1e-8
    weight_precision: 6       # 6 decimal places
    return_precision: 6
    
  # Parallel processing
  parallel:
    enabled: true
    max_workers: 4            # Maximum parallel workers
    chunk_size: 100           # Chunk size for parallel processing

# Caching Configuration
caching:
  enabled: true
  
  # Cache settings
  covariance_cache:
    enabled: true
    ttl_seconds: 3600         # 1 hour TTL
    max_size: 1000            # Maximum cache entries
    
  optimization_cache:
    enabled: true
    ttl_seconds: 1800         # 30 minutes TTL
    max_size: 500
    
  performance_cache:
    enabled: true
    ttl_seconds: 900          # 15 minutes TTL
    max_size: 200

# Logging Configuration
logging:
  level: "INFO"
  optimization_log: "logs/portfolio_optimization.log"
  performance_log: "logs/performance_analytics.log"
  rebalancing_log: "logs/rebalancing.log"
  
  # Log rotation
  max_file_size: "50MB"
  backup_count: 5
  
  # Detailed logging
  log_optimization_details: true
  log_performance_calculations: false
  log_rebalancing_actions: true

# API Configuration
api:
  # Rate limiting
  rate_limiting:
    enabled: true
    requests_per_minute: 60
    
  # Response formatting
  response:
    precision: 6              # Decimal precision
    include_metadata: true
    include_recommendations: true
    
  # Validation
  validation:
    max_symbols: 50           # Maximum symbols per request
    min_symbols: 2            # Minimum symbols per request
    max_lookback_days: 1260   # Maximum lookback period

# Integration Settings
integration:
  # Live trading integration
  live_trading:
    enabled: true
    auto_rebalancing: false   # Automatic rebalancing
    signal_integration: true  # Use strategy signals as views
    
  # Strategy integration
  strategies:
    signal_to_view_mapping: true
    confidence_scaling: true
    signal_threshold: 0.5     # Minimum signal strength
    
  # RL optimizer integration
  rl_optimizer:
    enabled: true
    parameter_optimization: true
    adaptive_constraints: false

# Monitoring and Alerts
monitoring:
  enabled: true
  
  # Performance alerts
  alerts:
    max_drawdown_threshold: 0.15      # 15% drawdown alert
    low_sharpe_threshold: 0.5         # Sharpe ratio < 0.5
    high_volatility_threshold: 0.30   # 30% volatility alert
    concentration_threshold: 0.25     # 25% concentration alert
    
  # System health monitoring
  health_checks:
    optimization_success_rate: 0.95   # 95% success rate
    average_optimization_time: 30     # 30 seconds max
    cache_hit_rate: 0.80              # 80% cache hit rate

# Development and Testing
development:
  # Testing configuration
  testing:
    use_mock_data: false
    test_symbols: ["RELIANCE", "TCS", "HDFCBANK", "INFY", "ICICIBANK"]
    test_portfolio_value: 1000000
    
  # Debugging
  debug:
    save_intermediate_results: false
    detailed_error_messages: true
    performance_profiling: false
    
  # Validation
  validation:
    cross_validation: false
    monte_carlo_simulation: false
    stress_testing: false
