"""
Risk Management System - Comprehensive risk controls for live trading.
Implements position sizing, stop losses, drawdown limits, and portfolio risk management.
"""

import asyncio
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, date, timedelta
from enum import Enum
from dataclasses import dataclass
import numpy as np

from .broker_integration import get_broker, Position, Holding, OrderSide
from .order_manager import OrderRequest, OrderType, ProductType, order_manager
from ..data.database import db_manager
from ..data.crud import StockCRUD, PriceCRUD
from ..utils.logger import get_logger
from ..utils.config import config

logger = get_logger(__name__)

class RiskLevel(Enum):
    """Risk levels."""
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"

class RiskViolationType(Enum):
    """Risk violation types."""
    POSITION_SIZE = "POSITION_SIZE"
    PORTFOLIO_CONCENTRATION = "PORTFOLIO_CONCENTRATION"
    DAILY_LOSS_LIMIT = "DAILY_LOSS_LIMIT"
    DRAWDOWN_LIMIT = "DRAWDOWN_LIMIT"
    VOLATILITY_LIMIT = "VOLATILITY_LIMIT"
    CORRELATION_LIMIT = "CORRELATION_LIMIT"
    SECTOR_CONCENTRATION = "SECTOR_CONCENTRATION"

@dataclass
class RiskViolation:
    """Risk violation details."""
    violation_type: RiskViolationType
    severity: RiskLevel
    symbol: str
    current_value: float
    limit_value: float
    message: str
    timestamp: datetime
    action_required: str

@dataclass
class PositionRisk:
    """Position risk metrics."""
    symbol: str
    position_size: float
    portfolio_weight: float
    var_1d: float  # 1-day Value at Risk
    volatility: float
    beta: float
    correlation_with_portfolio: float
    sector: str
    risk_score: float

@dataclass
class PortfolioRisk:
    """Portfolio risk metrics."""
    total_value: float
    total_pnl: float
    daily_pnl: float
    max_drawdown: float
    current_drawdown: float
    portfolio_var: float
    portfolio_volatility: float
    sharpe_ratio: float
    beta: float
    concentration_risk: float
    sector_concentration: Dict[str, float]

class RiskManager:
    """Comprehensive risk management system."""
    
    def __init__(self):
        """Initialize risk manager."""
        self.broker = get_broker()
        
        # Risk limits from configuration
        self.config = config.get_risk_config()
        self.max_position_size = self.config.get('max_position_size', 500000)  # ₹5L per position
        self.max_portfolio_concentration = self.config.get('max_portfolio_concentration', 0.15)  # 15% per stock
        self.max_sector_concentration = self.config.get('max_sector_concentration', 0.30)  # 30% per sector
        self.daily_loss_limit = self.config.get('daily_loss_limit', 50000)  # ₹50K daily loss
        self.max_drawdown_limit = self.config.get('max_drawdown_limit', 0.10)  # 10% max drawdown
        self.max_portfolio_var = self.config.get('max_portfolio_var', 100000)  # ₹1L VaR
        self.max_position_volatility = self.config.get('max_position_volatility', 0.30)  # 30% volatility
        self.max_correlation = self.config.get('max_correlation', 0.80)  # 80% correlation limit
        
        # Risk monitoring
        self.risk_violations: List[RiskViolation] = []
        self.position_risks: Dict[str, PositionRisk] = {}
        self.portfolio_risk: Optional[PortfolioRisk] = None
        
        # Emergency controls
        self.emergency_stop = False
        self.trading_halted = False
        
        # Historical data for risk calculations
        self.price_history: Dict[str, List[float]] = {}
        self.portfolio_history: List[float] = []
        
        logger.info("Risk Manager initialized")
    
    async def start(self):
        """Start risk manager."""
        # Start risk monitoring
        asyncio.create_task(self._monitor_risk())
        asyncio.create_task(self._update_risk_metrics())
        
        logger.info("Risk Manager started")
    
    async def validate_order(self, order_request: OrderRequest) -> Tuple[bool, Optional[str]]:
        """Validate order against risk limits."""
        try:
            # Get current positions and portfolio value
            positions = await self.broker.get_positions()
            holdings = await self.broker.get_holdings()
            balance = await self.broker.get_balance()
            
            portfolio_value = balance.get('total_margin', 0)
            for pos in positions:
                portfolio_value += pos.current_price * abs(pos.quantity)
            for holding in holdings:
                portfolio_value += holding.current_price * holding.quantity
            
            # Calculate order value
            order_price = order_request.price or await self.broker.get_ltp(order_request.symbol)
            order_value = order_price * order_request.quantity
            
            # Check position size limit
            if order_value > self.max_position_size:
                return False, f"Order value ₹{order_value:,.0f} exceeds position size limit ₹{self.max_position_size:,.0f}"
            
            # Check portfolio concentration
            if portfolio_value > 0:
                concentration = order_value / portfolio_value
                if concentration > self.max_portfolio_concentration:
                    return False, f"Order would create {concentration:.1%} concentration, exceeds limit {self.max_portfolio_concentration:.1%}"
            
            # Check existing position
            existing_position = None
            for pos in positions:
                if pos.symbol == order_request.symbol:
                    existing_position = pos
                    break
            
            if existing_position:
                # Check if order would increase position beyond limits
                if order_request.side == OrderSide.BUY and existing_position.quantity > 0:
                    new_position_value = (existing_position.quantity + order_request.quantity) * order_price
                    if new_position_value > self.max_position_size:
                        return False, f"Order would increase position to ₹{new_position_value:,.0f}, exceeds limit"
                elif order_request.side == OrderSide.SELL and existing_position.quantity < 0:
                    new_position_value = abs(existing_position.quantity + order_request.quantity) * order_price
                    if new_position_value > self.max_position_size:
                        return False, f"Order would increase short position to ₹{new_position_value:,.0f}, exceeds limit"
            
            # Check daily loss limit
            if self.portfolio_risk and self.portfolio_risk.daily_pnl < -self.daily_loss_limit:
                return False, f"Daily loss limit reached: ₹{abs(self.portfolio_risk.daily_pnl):,.0f}"
            
            # Check drawdown limit
            if self.portfolio_risk and self.portfolio_risk.current_drawdown > self.max_drawdown_limit:
                return False, f"Drawdown limit exceeded: {self.portfolio_risk.current_drawdown:.1%}"
            
            # Check emergency stop
            if self.emergency_stop:
                return False, "Emergency stop activated - all trading halted"
            
            # Check trading halt
            if self.trading_halted:
                return False, "Trading halted due to risk violations"
            
            # Check sector concentration
            sector_check = await self._check_sector_concentration(order_request.symbol, order_value, portfolio_value)
            if not sector_check[0]:
                return False, sector_check[1]
            
            # Check volatility limits
            volatility_check = await self._check_volatility_limits(order_request.symbol)
            if not volatility_check[0]:
                return False, volatility_check[1]
            
            return True, None
            
        except Exception as e:
            logger.error(f"Error validating order: {e}")
            return False, f"Risk validation error: {str(e)}"
    
    async def calculate_position_size(self, symbol: str, signal_strength: float, volatility: float) -> int:
        """Calculate optimal position size based on risk."""
        try:
            # Get portfolio value
            balance = await self.broker.get_balance()
            portfolio_value = balance.get('total_margin', 1000000)
            
            # Base position size (2% of portfolio)
            base_position_value = portfolio_value * 0.02
            
            # Adjust for signal strength (0.5x to 2x)
            signal_multiplier = 0.5 + (signal_strength * 1.5)
            adjusted_position_value = base_position_value * signal_multiplier
            
            # Adjust for volatility (inverse relationship)
            volatility_multiplier = min(2.0, 0.02 / max(volatility, 0.01))
            final_position_value = adjusted_position_value * volatility_multiplier
            
            # Apply position size limit
            final_position_value = min(final_position_value, self.max_position_size)
            
            # Convert to quantity
            current_price = await self.broker.get_ltp(symbol)
            if current_price > 0:
                quantity = int(final_position_value / current_price)
                return max(1, quantity)  # Minimum 1 share
            
            return 1
            
        except Exception as e:
            logger.error(f"Error calculating position size for {symbol}: {e}")
            return 1
    
    async def calculate_stop_loss(self, symbol: str, entry_price: float, side: OrderSide) -> float:
        """Calculate stop loss price."""
        try:
            # Get volatility
            volatility = await self._get_symbol_volatility(symbol)
            
            # Base stop loss (2% or 2 * volatility, whichever is higher)
            stop_loss_percent = max(0.02, 2 * volatility)
            
            if side == OrderSide.BUY:
                # Long position - stop loss below entry
                stop_loss_price = entry_price * (1 - stop_loss_percent)
            else:
                # Short position - stop loss above entry
                stop_loss_price = entry_price * (1 + stop_loss_percent)
            
            return round(stop_loss_price, 2)
            
        except Exception as e:
            logger.error(f"Error calculating stop loss for {symbol}: {e}")
            return entry_price * 0.95 if side == OrderSide.BUY else entry_price * 1.05
    
    async def get_risk_violations(self) -> List[RiskViolation]:
        """Get current risk violations."""
        return self.risk_violations.copy()
    
    async def get_portfolio_risk(self) -> Optional[PortfolioRisk]:
        """Get portfolio risk metrics."""
        return self.portfolio_risk
    
    async def get_position_risks(self) -> Dict[str, PositionRisk]:
        """Get position risk metrics."""
        return self.position_risks.copy()
    
    async def activate_emergency_stop(self, reason: str):
        """Activate emergency stop."""
        self.emergency_stop = True
        
        # Cancel all pending orders
        all_orders = await order_manager.get_all_orders()
        for order in all_orders['active']:
            await order_manager.cancel_order(order.order_id)
        
        # Log emergency stop
        violation = RiskViolation(
            violation_type=RiskViolationType.DAILY_LOSS_LIMIT,
            severity=RiskLevel.CRITICAL,
            symbol="PORTFOLIO",
            current_value=0,
            limit_value=0,
            message=f"Emergency stop activated: {reason}",
            timestamp=datetime.now(),
            action_required="Manual intervention required"
        )
        self.risk_violations.append(violation)
        
        logger.critical(f"EMERGENCY STOP ACTIVATED: {reason}")
    
    async def deactivate_emergency_stop(self):
        """Deactivate emergency stop (manual intervention)."""
        self.emergency_stop = False
        self.trading_halted = False
        logger.info("Emergency stop deactivated")
    
    async def _monitor_risk(self):
        """Monitor risk continuously."""
        while True:
            try:
                await self._check_risk_violations()
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                logger.error(f"Error in risk monitoring: {e}")
                await asyncio.sleep(60)
    
    async def _update_risk_metrics(self):
        """Update risk metrics periodically."""
        while True:
            try:
                await self._calculate_portfolio_risk()
                await self._calculate_position_risks()
                await asyncio.sleep(300)  # Update every 5 minutes
                
            except Exception as e:
                logger.error(f"Error updating risk metrics: {e}")
                await asyncio.sleep(600)
    
    async def _check_risk_violations(self):
        """Check for risk violations."""
        try:
            current_violations = []
            
            # Check portfolio risk
            if self.portfolio_risk:
                # Daily loss limit
                if self.portfolio_risk.daily_pnl < -self.daily_loss_limit:
                    violation = RiskViolation(
                        violation_type=RiskViolationType.DAILY_LOSS_LIMIT,
                        severity=RiskLevel.CRITICAL,
                        symbol="PORTFOLIO",
                        current_value=abs(self.portfolio_risk.daily_pnl),
                        limit_value=self.daily_loss_limit,
                        message=f"Daily loss ₹{abs(self.portfolio_risk.daily_pnl):,.0f} exceeds limit ₹{self.daily_loss_limit:,.0f}",
                        timestamp=datetime.now(),
                        action_required="Stop trading, review positions"
                    )
                    current_violations.append(violation)
                
                # Drawdown limit
                if self.portfolio_risk.current_drawdown > self.max_drawdown_limit:
                    violation = RiskViolation(
                        violation_type=RiskViolationType.DRAWDOWN_LIMIT,
                        severity=RiskLevel.HIGH,
                        symbol="PORTFOLIO",
                        current_value=self.portfolio_risk.current_drawdown,
                        limit_value=self.max_drawdown_limit,
                        message=f"Drawdown {self.portfolio_risk.current_drawdown:.1%} exceeds limit {self.max_drawdown_limit:.1%}",
                        timestamp=datetime.now(),
                        action_required="Reduce position sizes"
                    )
                    current_violations.append(violation)
            
            # Check position risks
            for symbol, pos_risk in self.position_risks.items():
                # Position size
                if pos_risk.position_size > self.max_position_size:
                    violation = RiskViolation(
                        violation_type=RiskViolationType.POSITION_SIZE,
                        severity=RiskLevel.HIGH,
                        symbol=symbol,
                        current_value=pos_risk.position_size,
                        limit_value=self.max_position_size,
                        message=f"Position size ₹{pos_risk.position_size:,.0f} exceeds limit",
                        timestamp=datetime.now(),
                        action_required="Reduce position size"
                    )
                    current_violations.append(violation)
                
                # Portfolio concentration
                if pos_risk.portfolio_weight > self.max_portfolio_concentration:
                    violation = RiskViolation(
                        violation_type=RiskViolationType.PORTFOLIO_CONCENTRATION,
                        severity=RiskLevel.MEDIUM,
                        symbol=symbol,
                        current_value=pos_risk.portfolio_weight,
                        limit_value=self.max_portfolio_concentration,
                        message=f"Portfolio weight {pos_risk.portfolio_weight:.1%} exceeds limit",
                        timestamp=datetime.now(),
                        action_required="Diversify portfolio"
                    )
                    current_violations.append(violation)
            
            # Update violations list
            self.risk_violations = current_violations
            
            # Check for critical violations
            critical_violations = [v for v in current_violations if v.severity == RiskLevel.CRITICAL]
            if critical_violations and not self.emergency_stop:
                await self.activate_emergency_stop("Critical risk violations detected")
            
        except Exception as e:
            logger.error(f"Error checking risk violations: {e}")
    
    async def _calculate_portfolio_risk(self):
        """Calculate portfolio risk metrics."""
        try:
            # Get current positions and balance
            positions = await self.broker.get_positions()
            holdings = await self.broker.get_holdings()
            balance = await self.broker.get_balance()
            
            # Calculate portfolio value and P&L
            total_value = balance.get('total_margin', 0)
            total_pnl = 0
            
            for pos in positions:
                position_value = pos.current_price * abs(pos.quantity)
                total_value += position_value
                total_pnl += pos.pnl
            
            for holding in holdings:
                holding_value = holding.current_price * holding.quantity
                total_value += holding_value
                total_pnl += holding.pnl
            
            # Calculate daily P&L (simplified)
            daily_pnl = total_pnl  # Would need historical data for accurate calculation
            
            # Update portfolio history
            self.portfolio_history.append(total_value)
            if len(self.portfolio_history) > 252:  # Keep 1 year of data
                self.portfolio_history.pop(0)
            
            # Calculate drawdown
            if self.portfolio_history:
                peak_value = max(self.portfolio_history)
                current_drawdown = (peak_value - total_value) / peak_value if peak_value > 0 else 0
                max_drawdown = current_drawdown  # Simplified
            else:
                current_drawdown = 0
                max_drawdown = 0
            
            # Calculate volatility and other metrics (simplified)
            if len(self.portfolio_history) > 20:
                returns = np.diff(self.portfolio_history) / self.portfolio_history[:-1]
                portfolio_volatility = np.std(returns) * np.sqrt(252)  # Annualized
                sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252) if np.std(returns) > 0 else 0
            else:
                portfolio_volatility = 0
                sharpe_ratio = 0
            
            # Calculate VaR (95% confidence)
            portfolio_var = total_value * 0.05  # Simplified 5% VaR
            
            # Calculate concentration risk
            if total_value > 0:
                position_weights = []
                for pos in positions:
                    weight = (pos.current_price * abs(pos.quantity)) / total_value
                    position_weights.append(weight)
                
                concentration_risk = max(position_weights) if position_weights else 0
            else:
                concentration_risk = 0
            
            # Calculate sector concentration
            sector_concentration = await self._calculate_sector_concentration(positions, holdings, total_value)
            
            # Create portfolio risk object
            self.portfolio_risk = PortfolioRisk(
                total_value=total_value,
                total_pnl=total_pnl,
                daily_pnl=daily_pnl,
                max_drawdown=max_drawdown,
                current_drawdown=current_drawdown,
                portfolio_var=portfolio_var,
                portfolio_volatility=portfolio_volatility,
                sharpe_ratio=sharpe_ratio,
                beta=1.0,  # Would calculate against market index
                concentration_risk=concentration_risk,
                sector_concentration=sector_concentration
            )
            
        except Exception as e:
            logger.error(f"Error calculating portfolio risk: {e}")
    
    async def _calculate_position_risks(self):
        """Calculate individual position risks."""
        try:
            positions = await self.broker.get_positions()
            holdings = await self.broker.get_holdings()
            
            portfolio_value = self.portfolio_risk.total_value if self.portfolio_risk else 1000000
            
            position_risks = {}
            
            # Process positions
            for pos in positions:
                if abs(pos.quantity) > 0:
                    position_size = pos.current_price * abs(pos.quantity)
                    portfolio_weight = position_size / portfolio_value if portfolio_value > 0 else 0
                    
                    # Get volatility
                    volatility = await self._get_symbol_volatility(pos.symbol)
                    
                    # Calculate VaR
                    var_1d = position_size * volatility / np.sqrt(252)  # Daily VaR
                    
                    # Get sector
                    sector = await self._get_symbol_sector(pos.symbol)
                    
                    # Calculate risk score (0-100)
                    risk_score = min(100, (portfolio_weight * 100) + (volatility * 200))
                    
                    position_risks[pos.symbol] = PositionRisk(
                        symbol=pos.symbol,
                        position_size=position_size,
                        portfolio_weight=portfolio_weight,
                        var_1d=var_1d,
                        volatility=volatility,
                        beta=1.0,  # Would calculate
                        correlation_with_portfolio=0.5,  # Would calculate
                        sector=sector,
                        risk_score=risk_score
                    )
            
            # Process holdings
            for holding in holdings:
                if holding.quantity > 0:
                    position_size = holding.current_price * holding.quantity
                    portfolio_weight = position_size / portfolio_value if portfolio_value > 0 else 0
                    
                    volatility = await self._get_symbol_volatility(holding.symbol)
                    var_1d = position_size * volatility / np.sqrt(252)
                    sector = await self._get_symbol_sector(holding.symbol)
                    risk_score = min(100, (portfolio_weight * 100) + (volatility * 200))
                    
                    position_risks[holding.symbol] = PositionRisk(
                        symbol=holding.symbol,
                        position_size=position_size,
                        portfolio_weight=portfolio_weight,
                        var_1d=var_1d,
                        volatility=volatility,
                        beta=1.0,
                        correlation_with_portfolio=0.5,
                        sector=sector,
                        risk_score=risk_score
                    )
            
            self.position_risks = position_risks
            
        except Exception as e:
            logger.error(f"Error calculating position risks: {e}")
    
    async def _get_symbol_volatility(self, symbol: str) -> float:
        """Get symbol volatility."""
        try:
            with db_manager.get_session() as db:
                stock = StockCRUD.get_stock_by_symbol(db, symbol)
                if not stock:
                    return 0.25  # Default volatility
                
                # Get 30 days of price data
                end_date = date.today()
                start_date = end_date - timedelta(days=30)
                
                prices = PriceCRUD.get_price_history(db, stock.id, start_date, end_date)
                
                if len(prices) < 10:
                    return 0.25
                
                # Calculate returns
                close_prices = [float(p.close_price) for p in prices]
                returns = np.diff(close_prices) / close_prices[:-1]
                
                # Annualized volatility
                volatility = np.std(returns) * np.sqrt(252)
                return min(1.0, max(0.05, volatility))  # Cap between 5% and 100%
                
        except Exception as e:
            logger.error(f"Error getting volatility for {symbol}: {e}")
            return 0.25
    
    async def _get_symbol_sector(self, symbol: str) -> str:
        """Get symbol sector."""
        try:
            with db_manager.get_session() as db:
                stock = StockCRUD.get_stock_by_symbol(db, symbol)
                return stock.sector if stock else "Unknown"
        except Exception as e:
            logger.error(f"Error getting sector for {symbol}: {e}")
            return "Unknown"
    
    async def _check_sector_concentration(self, symbol: str, order_value: float, portfolio_value: float) -> Tuple[bool, Optional[str]]:
        """Check sector concentration limits."""
        try:
            if portfolio_value <= 0:
                return True, None
            
            sector = await self._get_symbol_sector(symbol)
            
            # Calculate current sector exposure
            positions = await self.broker.get_positions()
            holdings = await self.broker.get_holdings()
            
            sector_value = 0
            for pos in positions:
                pos_sector = await self._get_symbol_sector(pos.symbol)
                if pos_sector == sector:
                    sector_value += pos.current_price * abs(pos.quantity)
            
            for holding in holdings:
                holding_sector = await self._get_symbol_sector(holding.symbol)
                if holding_sector == sector:
                    sector_value += holding.current_price * holding.quantity
            
            # Add order value
            new_sector_value = sector_value + order_value
            sector_concentration = new_sector_value / portfolio_value
            
            if sector_concentration > self.max_sector_concentration:
                return False, f"Sector concentration would be {sector_concentration:.1%}, exceeds limit {self.max_sector_concentration:.1%}"
            
            return True, None
            
        except Exception as e:
            logger.error(f"Error checking sector concentration: {e}")
            return True, None
    
    async def _check_volatility_limits(self, symbol: str) -> Tuple[bool, Optional[str]]:
        """Check volatility limits."""
        try:
            volatility = await self._get_symbol_volatility(symbol)
            
            if volatility > self.max_position_volatility:
                return False, f"Symbol volatility {volatility:.1%} exceeds limit {self.max_position_volatility:.1%}"
            
            return True, None
            
        except Exception as e:
            logger.error(f"Error checking volatility limits: {e}")
            return True, None
    
    async def _calculate_sector_concentration(self, positions: List[Position], holdings: List[Holding], total_value: float) -> Dict[str, float]:
        """Calculate sector concentration."""
        try:
            sector_values = {}
            
            # Process positions
            for pos in positions:
                sector = await self._get_symbol_sector(pos.symbol)
                position_value = pos.current_price * abs(pos.quantity)
                sector_values[sector] = sector_values.get(sector, 0) + position_value
            
            # Process holdings
            for holding in holdings:
                sector = await self._get_symbol_sector(holding.symbol)
                holding_value = holding.current_price * holding.quantity
                sector_values[sector] = sector_values.get(sector, 0) + holding_value
            
            # Convert to percentages
            sector_concentration = {}
            if total_value > 0:
                for sector, value in sector_values.items():
                    sector_concentration[sector] = value / total_value
            
            return sector_concentration
            
        except Exception as e:
            logger.error(f"Error calculating sector concentration: {e}")
            return {}

# Global risk manager instance
risk_manager = RiskManager()

async def initialize_risk_manager() -> bool:
    """Initialize risk manager."""
    try:
        await risk_manager.start()
        return True
    except Exception as e:
        logger.error(f"Failed to initialize risk manager: {e}")
        return False
