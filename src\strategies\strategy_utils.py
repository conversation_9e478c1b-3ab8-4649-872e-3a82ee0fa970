"""
Utility functions and helpers for trading strategies.
Common calculations, pattern recognition, and signal processing.
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Any, Tuple, Optional
from scipy import stats
from sklearn.preprocessing import StandardScaler
import talib

from ..utils.logger import get_logger

logger = get_logger(__name__)

class TechnicalAnalysis:
    """Technical analysis utility functions."""
    
    @staticmethod
    def detect_trend(prices: pd.Series, window: int = 20) -> str:
        """Detect price trend direction.
        
        Args:
            prices: Price series
            window: Window for trend calculation
            
        Returns:
            'uptrend', 'downtrend', or 'sideways'
        """
        if len(prices) < window:
            return 'sideways'
        
        # Calculate linear regression slope
        recent_prices = prices.tail(window)
        x = np.arange(len(recent_prices))
        slope, _, r_value, _, _ = stats.linregress(x, recent_prices)
        
        # Determine trend based on slope and correlation
        if abs(r_value) < 0.5:  # Weak correlation
            return 'sideways'
        elif slope > 0:
            return 'uptrend'
        else:
            return 'downtrend'
    
    @staticmethod
    def calculate_volatility(prices: pd.Series, window: int = 20) -> pd.Series:
        """Calculate rolling volatility.
        
        Args:
            prices: Price series
            window: Rolling window
            
        Returns:
            Volatility series
        """
        returns = prices.pct_change()
        return returns.rolling(window=window).std() * np.sqrt(252)  # Annualized
    
    @staticmethod
    def detect_support_resistance(
        highs: pd.Series, 
        lows: pd.Series, 
        window: int = 20,
        min_touches: int = 2
    ) -> Dict[str, List[float]]:
        """Detect support and resistance levels.
        
        Args:
            highs: High prices
            lows: Low prices
            window: Window for level detection
            min_touches: Minimum touches to confirm level
            
        Returns:
            Dictionary with support and resistance levels
        """
        support_levels = []
        resistance_levels = []
        
        # Find local minima for support
        for i in range(window, len(lows) - window):
            if lows.iloc[i] == lows.iloc[i-window:i+window+1].min():
                level = lows.iloc[i]
                
                # Count touches within tolerance
                tolerance = level * 0.02  # 2% tolerance
                touches = sum(1 for price in lows if abs(price - level) <= tolerance)
                
                if touches >= min_touches:
                    support_levels.append(level)
        
        # Find local maxima for resistance
        for i in range(window, len(highs) - window):
            if highs.iloc[i] == highs.iloc[i-window:i+window+1].max():
                level = highs.iloc[i]
                
                # Count touches within tolerance
                tolerance = level * 0.02  # 2% tolerance
                touches = sum(1 for price in highs if abs(price - level) <= tolerance)
                
                if touches >= min_touches:
                    resistance_levels.append(level)
        
        return {
            'support': sorted(list(set(support_levels))),
            'resistance': sorted(list(set(resistance_levels)), reverse=True)
        }
    
    @staticmethod
    def calculate_atr(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """Calculate Average True Range.
        
        Args:
            high: High prices
            low: Low prices
            close: Close prices
            period: ATR period
            
        Returns:
            ATR series
        """
        try:
            return talib.ATR(high.values, low.values, close.values, timeperiod=period)
        except:
            # Fallback calculation
            tr1 = high - low
            tr2 = abs(high - close.shift(1))
            tr3 = abs(low - close.shift(1))
            
            true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            return true_range.rolling(window=period).mean()
    
    @staticmethod
    def calculate_adx(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """Calculate Average Directional Index.
        
        Args:
            high: High prices
            low: Low prices
            close: Close prices
            period: ADX period
            
        Returns:
            ADX series
        """
        try:
            return talib.ADX(high.values, low.values, close.values, timeperiod=period)
        except:
            logger.warning("TA-Lib not available, using simplified ADX calculation")
            # Simplified ADX calculation
            return pd.Series(50, index=close.index)  # Neutral value

class PatternRecognition:
    """Pattern recognition utilities."""
    
    @staticmethod
    def detect_divergence(
        prices: pd.Series, 
        indicator: pd.Series, 
        window: int = 5
    ) -> Dict[str, List[int]]:
        """Detect bullish and bearish divergences.
        
        Args:
            prices: Price series
            indicator: Technical indicator series (e.g., RSI, MACD)
            window: Window for peak/trough detection
            
        Returns:
            Dictionary with divergence indices
        """
        bullish_divergences = []
        bearish_divergences = []
        
        # Find price peaks and troughs
        price_peaks = []
        price_troughs = []
        indicator_peaks = []
        indicator_troughs = []
        
        for i in range(window, len(prices) - window):
            # Price peaks
            if prices.iloc[i] == prices.iloc[i-window:i+window+1].max():
                price_peaks.append((i, prices.iloc[i]))
                indicator_peaks.append((i, indicator.iloc[i]))
            
            # Price troughs
            if prices.iloc[i] == prices.iloc[i-window:i+window+1].min():
                price_troughs.append((i, prices.iloc[i]))
                indicator_troughs.append((i, indicator.iloc[i]))
        
        # Detect bullish divergence (price makes lower low, indicator makes higher low)
        for i in range(1, len(price_troughs)):
            curr_price_idx, curr_price = price_troughs[i]
            prev_price_idx, prev_price = price_troughs[i-1]
            
            # Find corresponding indicator values
            curr_indicator = next((ind for idx, ind in indicator_troughs if idx == curr_price_idx), None)
            prev_indicator = next((ind for idx, ind in indicator_troughs if idx == prev_price_idx), None)
            
            if (curr_indicator is not None and prev_indicator is not None and
                curr_price < prev_price and curr_indicator > prev_indicator):
                bullish_divergences.append(curr_price_idx)
        
        # Detect bearish divergence (price makes higher high, indicator makes lower high)
        for i in range(1, len(price_peaks)):
            curr_price_idx, curr_price = price_peaks[i]
            prev_price_idx, prev_price = price_peaks[i-1]
            
            # Find corresponding indicator values
            curr_indicator = next((ind for idx, ind in indicator_peaks if idx == curr_price_idx), None)
            prev_indicator = next((ind for idx, ind in indicator_peaks if idx == prev_price_idx), None)
            
            if (curr_indicator is not None and prev_indicator is not None and
                curr_price > prev_price and curr_indicator < prev_indicator):
                bearish_divergences.append(curr_price_idx)
        
        return {
            'bullish': bullish_divergences,
            'bearish': bearish_divergences
        }
    
    @staticmethod
    def detect_candlestick_patterns(
        open_prices: pd.Series,
        high_prices: pd.Series,
        low_prices: pd.Series,
        close_prices: pd.Series
    ) -> Dict[str, pd.Series]:
        """Detect common candlestick patterns.
        
        Args:
            open_prices: Open prices
            high_prices: High prices
            low_prices: Low prices
            close_prices: Close prices
            
        Returns:
            Dictionary with pattern detection results
        """
        patterns = {}
        
        try:
            # Convert to numpy arrays for TA-Lib
            open_arr = open_prices.values
            high_arr = high_prices.values
            low_arr = low_prices.values
            close_arr = close_prices.values
            
            # Bullish patterns
            patterns['hammer'] = pd.Series(
                talib.CDLHAMMER(open_arr, high_arr, low_arr, close_arr),
                index=close_prices.index
            )
            
            patterns['doji'] = pd.Series(
                talib.CDLDOJI(open_arr, high_arr, low_arr, close_arr),
                index=close_prices.index
            )
            
            patterns['engulfing_bullish'] = pd.Series(
                talib.CDLENGULFING(open_arr, high_arr, low_arr, close_arr),
                index=close_prices.index
            )
            
            patterns['morning_star'] = pd.Series(
                talib.CDLMORNINGSTAR(open_arr, high_arr, low_arr, close_arr),
                index=close_prices.index
            )
            
            # Bearish patterns
            patterns['hanging_man'] = pd.Series(
                talib.CDLHANGINGMAN(open_arr, high_arr, low_arr, close_arr),
                index=close_prices.index
            )
            
            patterns['shooting_star'] = pd.Series(
                talib.CDLSHOOTINGSTAR(open_arr, high_arr, low_arr, close_arr),
                index=close_prices.index
            )
            
            patterns['evening_star'] = pd.Series(
                talib.CDLEVENINGSTAR(open_arr, high_arr, low_arr, close_arr),
                index=close_prices.index
            )
            
        except Exception as e:
            logger.warning(f"Error detecting candlestick patterns: {e}")
            # Return empty series if TA-Lib is not available
            for pattern_name in ['hammer', 'doji', 'engulfing_bullish', 'morning_star', 
                               'hanging_man', 'shooting_star', 'evening_star']:
                patterns[pattern_name] = pd.Series(0, index=close_prices.index)
        
        return patterns

class SignalFilters:
    """Signal filtering and validation utilities."""
    
    @staticmethod
    def volume_confirmation(
        signal_price: float,
        current_volume: int,
        avg_volume: int,
        min_volume_ratio: float = 1.5
    ) -> bool:
        """Check if signal has volume confirmation.
        
        Args:
            signal_price: Price at signal generation
            current_volume: Current trading volume
            avg_volume: Average volume
            min_volume_ratio: Minimum volume ratio for confirmation
            
        Returns:
            True if volume confirms signal
        """
        if avg_volume == 0:
            return False
        
        volume_ratio = current_volume / avg_volume
        return volume_ratio >= min_volume_ratio
    
    @staticmethod
    def trend_alignment(
        signal_type: str,
        trend_direction: str,
        require_alignment: bool = True
    ) -> bool:
        """Check if signal aligns with trend.
        
        Args:
            signal_type: 'BUY' or 'SELL'
            trend_direction: 'uptrend', 'downtrend', or 'sideways'
            require_alignment: Whether to require trend alignment
            
        Returns:
            True if signal aligns with trend or alignment not required
        """
        if not require_alignment:
            return True
        
        if signal_type == 'BUY' and trend_direction == 'uptrend':
            return True
        elif signal_type == 'SELL' and trend_direction == 'downtrend':
            return True
        elif trend_direction == 'sideways':
            return True  # Allow signals in sideways markets
        
        return False
    
    @staticmethod
    def multiple_timeframe_confirmation(
        short_term_signal: bool,
        medium_term_signal: bool,
        long_term_signal: bool,
        min_confirmations: int = 2
    ) -> bool:
        """Check for multiple timeframe confirmation.
        
        Args:
            short_term_signal: Short-term signal confirmation
            medium_term_signal: Medium-term signal confirmation
            long_term_signal: Long-term signal confirmation
            min_confirmations: Minimum confirmations required
            
        Returns:
            True if minimum confirmations met
        """
        confirmations = sum([short_term_signal, medium_term_signal, long_term_signal])
        return confirmations >= min_confirmations
    
    @staticmethod
    def risk_reward_ratio(
        entry_price: float,
        target_price: float,
        stop_loss: float,
        min_ratio: float = 2.0
    ) -> bool:
        """Check if risk-reward ratio is acceptable.
        
        Args:
            entry_price: Entry price
            target_price: Target price
            stop_loss: Stop loss price
            min_ratio: Minimum risk-reward ratio
            
        Returns:
            True if ratio meets minimum requirement
        """
        if stop_loss == entry_price:
            return False
        
        potential_profit = abs(target_price - entry_price)
        potential_loss = abs(entry_price - stop_loss)
        
        if potential_loss == 0:
            return True  # No risk
        
        ratio = potential_profit / potential_loss
        return ratio >= min_ratio
