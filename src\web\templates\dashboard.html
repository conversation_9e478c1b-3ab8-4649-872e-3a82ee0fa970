<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stock Analysis System - Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div x-data="dashboard()" x-init="init()" class="min-h-screen">
        <!-- Navigation -->
        <nav class="bg-blue-900 text-white p-4">
            <div class="container mx-auto flex justify-between items-center">
                <h1 class="text-xl font-bold">
                    <i class="fas fa-chart-line mr-2"></i>
                    Stock Analysis System
                </h1>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 rounded-full" :class="systemStatus.database_connected ? 'bg-green-400' : 'bg-red-400'"></div>
                        <span class="text-sm">System Status</span>
                    </div>
                    <button @click="logout()" class="bg-blue-700 hover:bg-blue-600 px-3 py-1 rounded">
                        <i class="fas fa-sign-out-alt mr-1"></i>
                        Logout
                    </button>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="container mx-auto p-6">
            <!-- Dashboard Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- Portfolio Value -->
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-gray-600 text-sm">Portfolio Value</p>
                            <p class="text-2xl font-bold text-green-600" x-text="formatCurrency(portfolioSummary.total_value)"></p>
                        </div>
                        <i class="fas fa-wallet text-3xl text-green-500"></i>
                    </div>
                    <div class="mt-2">
                        <span class="text-sm" :class="portfolioSummary.day_pnl >= 0 ? 'text-green-600' : 'text-red-600'">
                            <i :class="portfolioSummary.day_pnl >= 0 ? 'fas fa-arrow-up' : 'fas fa-arrow-down'"></i>
                            <span x-text="portfolioSummary.day_pnl_percent + '%'"></span>
                            Today
                        </span>
                    </div>
                </div>

                <!-- Active Positions -->
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-gray-600 text-sm">Active Positions</p>
                            <p class="text-2xl font-bold text-blue-600" x-text="portfolioSummary.positions_count"></p>
                        </div>
                        <i class="fas fa-chart-pie text-3xl text-blue-500"></i>
                    </div>
                    <div class="mt-2">
                        <span class="text-sm text-gray-600">
                            <span x-text="portfolioSummary.active_strategies"></span> Strategies Active
                        </span>
                    </div>
                </div>

                <!-- Total P&L -->
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-gray-600 text-sm">Total P&L</p>
                            <p class="text-2xl font-bold" :class="portfolioSummary.total_pnl >= 0 ? 'text-green-600' : 'text-red-600'" x-text="formatCurrency(portfolioSummary.total_pnl)"></p>
                        </div>
                        <i class="fas fa-chart-line text-3xl" :class="portfolioSummary.total_pnl >= 0 ? 'text-green-500' : 'text-red-500'"></i>
                    </div>
                    <div class="mt-2">
                        <span class="text-sm" :class="portfolioSummary.total_pnl_percent >= 0 ? 'text-green-600' : 'text-red-600'">
                            <span x-text="portfolioSummary.total_pnl_percent + '%'"></span>
                            Overall
                        </span>
                    </div>
                </div>

                <!-- Recent Signals -->
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-gray-600 text-sm">Recent Signals</p>
                            <p class="text-2xl font-bold text-purple-600" x-text="recentSignals.length"></p>
                        </div>
                        <i class="fas fa-bell text-3xl text-purple-500"></i>
                    </div>
                    <div class="mt-2">
                        <span class="text-sm text-gray-600">Last 24 hours</span>
                    </div>
                </div>
            </div>

            <!-- Charts and Tables Row -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <!-- Portfolio Performance Chart -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold mb-4">Portfolio Performance</h3>
                    <canvas id="portfolioChart" width="400" height="200"></canvas>
                </div>

                <!-- Strategy Performance -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold mb-4">Strategy Performance</h3>
                    <div class="space-y-3">
                        <template x-for="strategy in strategyPerformance" :key="strategy.strategy_name">
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
                                <div>
                                    <p class="font-medium" x-text="strategy.strategy_name.replace('_', ' ').toUpperCase()"></p>
                                    <p class="text-sm text-gray-600">
                                        Win Rate: <span x-text="strategy.win_rate + '%'"></span>
                                    </p>
                                </div>
                                <div class="text-right">
                                    <p class="font-bold" :class="strategy.avg_return >= 0 ? 'text-green-600' : 'text-red-600'" x-text="strategy.avg_return + '%'"></p>
                                    <p class="text-sm text-gray-600" x-text="'Sharpe: ' + strategy.sharpe_ratio"></p>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>

            <!-- Recent Signals and Alerts -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Recent Signals -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold mb-4">Recent Trading Signals</h3>
                    <div class="space-y-3">
                        <template x-for="signal in recentSignals.slice(0, 5)" :key="signal.id">
                            <div class="flex items-center justify-between p-3 border-l-4" :class="signal.signal_type === 'BUY' ? 'border-green-500 bg-green-50' : 'border-red-500 bg-red-50'">
                                <div>
                                    <p class="font-medium" x-text="signal.symbol"></p>
                                    <p class="text-sm text-gray-600" x-text="signal.strategy_name.replace('_', ' ')"></p>
                                </div>
                                <div class="text-right">
                                    <span class="px-2 py-1 rounded text-sm font-medium" :class="signal.signal_type === 'BUY' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'" x-text="signal.signal_type"></span>
                                    <p class="text-sm text-gray-600" x-text="'₹' + signal.price"></p>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>

                <!-- System Alerts -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold mb-4">System Alerts</h3>
                    <div class="space-y-3">
                        <template x-for="alert in alerts.slice(0, 5)" :key="alert.id">
                            <div class="flex items-start space-x-3 p-3 rounded" :class="!alert.is_read ? 'bg-blue-50' : 'bg-gray-50'">
                                <i class="fas fa-info-circle text-blue-500 mt-1"></i>
                                <div class="flex-1">
                                    <p class="font-medium text-sm" x-text="alert.title"></p>
                                    <p class="text-xs text-gray-600" x-text="alert.message"></p>
                                    <p class="text-xs text-gray-500 mt-1" x-text="formatTime(alert.created_at)"></p>
                                </div>
                                <div class="w-2 h-2 rounded-full" :class="!alert.is_read ? 'bg-blue-500' : 'bg-gray-300'"></div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>

        <!-- WebSocket Status -->
        <div class="fixed bottom-4 right-4">
            <div class="bg-white rounded-lg shadow p-3 flex items-center space-x-2">
                <div class="w-3 h-3 rounded-full" :class="wsConnected ? 'bg-green-400' : 'bg-red-400'"></div>
                <span class="text-sm" x-text="wsConnected ? 'Connected' : 'Disconnected'"></span>
            </div>
        </div>
    </div>

    <script>
        function dashboard() {
            return {
                // Data
                systemStatus: {},
                portfolioSummary: {},
                recentSignals: [],
                strategyPerformance: [],
                alerts: [],
                wsConnected: false,
                ws: null,
                authToken: localStorage.getItem('authToken'),

                // Initialize
                async init() {
                    if (!this.authToken) {
                        window.location.href = '/login';
                        return;
                    }

                    await this.loadDashboardData();
                    this.connectWebSocket();
                    this.initCharts();
                    
                    // Refresh data every 30 seconds
                    setInterval(() => this.loadDashboardData(), 30000);
                },

                // Load dashboard data
                async loadDashboardData() {
                    try {
                        const response = await fetch('/api/v1/dashboard/', {
                            headers: {
                                'Authorization': `Bearer ${this.authToken}`
                            }
                        });

                        if (response.ok) {
                            const data = await response.json();
                            this.systemStatus = data.data.system_status;
                            this.portfolioSummary = data.data.portfolio_summary;
                            this.recentSignals = data.data.recent_signals;
                            this.strategyPerformance = data.data.strategy_performance;
                            this.alerts = data.data.alerts;
                        }
                    } catch (error) {
                        console.error('Error loading dashboard data:', error);
                    }
                },

                // WebSocket connection
                connectWebSocket() {
                    const wsUrl = `ws://localhost:8000/ws/connect?token=${this.authToken}`;
                    this.ws = new WebSocket(wsUrl);

                    this.ws.onopen = () => {
                        this.wsConnected = true;
                        console.log('WebSocket connected');
                        
                        // Subscribe to channels
                        this.ws.send(JSON.stringify({
                            type: 'subscribe',
                            data: {
                                channels: ['system_status', 'signals', 'portfolio', 'alerts']
                            }
                        }));
                    };

                    this.ws.onmessage = (event) => {
                        const message = JSON.parse(event.data);
                        this.handleWebSocketMessage(message);
                    };

                    this.ws.onclose = () => {
                        this.wsConnected = false;
                        console.log('WebSocket disconnected');
                        
                        // Reconnect after 5 seconds
                        setTimeout(() => this.connectWebSocket(), 5000);
                    };

                    this.ws.onerror = (error) => {
                        console.error('WebSocket error:', error);
                    };
                },

                // Handle WebSocket messages
                handleWebSocketMessage(message) {
                    switch (message.type) {
                        case 'system_status':
                            this.systemStatus = message.data;
                            break;
                        case 'signal_update':
                            this.recentSignals.unshift(message.data);
                            if (this.recentSignals.length > 10) {
                                this.recentSignals.pop();
                            }
                            break;
                        case 'portfolio_update':
                            this.portfolioSummary = { ...this.portfolioSummary, ...message.data };
                            break;
                        case 'alert':
                            this.alerts.unshift(message.data);
                            if (this.alerts.length > 10) {
                                this.alerts.pop();
                            }
                            break;
                    }
                },

                // Initialize charts
                initCharts() {
                    const ctx = document.getElementById('portfolioChart').getContext('2d');
                    new Chart(ctx, {
                        type: 'line',
                        data: {
                            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                            datasets: [{
                                label: 'Portfolio Value',
                                data: [1000000, 1050000, 1120000, 1080000, 1200000, 1250000],
                                borderColor: 'rgb(59, 130, 246)',
                                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                                tension: 0.4
                            }]
                        },
                        options: {
                            responsive: true,
                            scales: {
                                y: {
                                    beginAtZero: false,
                                    ticks: {
                                        callback: function(value) {
                                            return '₹' + (value / 100000).toFixed(1) + 'L';
                                        }
                                    }
                                }
                            }
                        }
                    });
                },

                // Utility functions
                formatCurrency(amount) {
                    if (amount >= 10000000) {
                        return '₹' + (amount / 10000000).toFixed(2) + 'Cr';
                    } else if (amount >= 100000) {
                        return '₹' + (amount / 100000).toFixed(2) + 'L';
                    } else {
                        return '₹' + amount.toLocaleString();
                    }
                },

                formatTime(timestamp) {
                    return new Date(timestamp).toLocaleTimeString();
                },

                logout() {
                    localStorage.removeItem('authToken');
                    if (this.ws) {
                        this.ws.close();
                    }
                    window.location.href = '/login';
                }
            }
        }
    </script>
</body>
</html>
