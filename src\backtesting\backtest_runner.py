"""
Backtesting Runner - High-level interface for running backtests.
Provides convenient methods for common backtesting scenarios.
"""

import asyncio
from datetime import datetime, date, timedelta
from typing import Dict, List, Any, Optional, Tuple
import pandas as pd

from .backtest_engine import BacktestEngine
from ..data.database import db_manager
from ..data.crud import StockCRUD
from ..strategies.strategy_registry import strategy_registry
from ..utils.logger import get_logger
from ..utils.config import config

logger = get_logger(__name__)

class BacktestRunner:
    """High-level backtesting runner with common scenarios."""
    
    def __init__(self):
        """Initialize backtest runner."""
        self.engine = BacktestEngine()
        self.default_start_date = date(2020, 1, 1)
        self.default_end_date = date.today() - timedelta(days=1)
    
    def run_all_strategies_backtest(
        self, 
        symbols: Optional[List[str]] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> Dict[str, Any]:
        """Run backtest for all enabled strategies.
        
        Args:
            symbols: List of symbols (default: Nifty 50)
            start_date: Start date (default: 2020-01-01)
            end_date: End date (default: yesterday)
            
        Returns:
            Comprehensive backtest results
        """
        logger.info("Running backtest for all enabled strategies")
        
        try:
            # Set defaults
            if start_date is None:
                start_date = self.default_start_date
            if end_date is None:
                end_date = self.default_end_date
            
            # Get symbols if not provided
            if symbols is None:
                symbols = self._get_nifty50_symbols()
            
            # Get enabled strategies
            enabled_strategies = self._get_enabled_strategies()
            
            if not enabled_strategies:
                return {'error': 'No enabled strategies found'}
            
            results = {
                'execution_time': datetime.now().isoformat(),
                'period': f"{start_date} to {end_date}",
                'strategies_tested': len(enabled_strategies),
                'symbols_tested': len(symbols),
                'strategy_results': {},
                'summary': {}
            }
            
            # Run backtest for each strategy
            for strategy_name in enabled_strategies:
                logger.info(f"Testing strategy: {strategy_name}")
                
                strategy_results = {}
                
                for symbol in symbols:
                    result = self.engine.run_single_strategy_backtest(
                        strategy_name, symbol, start_date, end_date
                    )
                    
                    if 'error' not in result:
                        strategy_results[symbol] = result
                    else:
                        logger.warning(f"Failed to backtest {strategy_name} on {symbol}: {result['error']}")
                
                results['strategy_results'][strategy_name] = strategy_results
            
            # Generate summary
            results['summary'] = self._generate_summary(results['strategy_results'])
            
            return results
            
        except Exception as e:
            logger.error(f"Error in all strategies backtest: {e}")
            return {'error': str(e)}
    
    def run_strategy_comparison(
        self, 
        strategy_names: List[str],
        symbol: str,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> Dict[str, Any]:
        """Compare multiple strategies on a single symbol.
        
        Args:
            strategy_names: List of strategy names to compare
            symbol: Stock symbol
            start_date: Start date
            end_date: End date
            
        Returns:
            Strategy comparison results
        """
        logger.info(f"Comparing {len(strategy_names)} strategies on {symbol}")
        
        try:
            if start_date is None:
                start_date = self.default_start_date
            if end_date is None:
                end_date = self.default_end_date
            
            comparison_results = {
                'symbol': symbol,
                'period': f"{start_date} to {end_date}",
                'strategies': strategy_names,
                'results': {},
                'ranking': {}
            }
            
            # Run backtest for each strategy
            for strategy_name in strategy_names:
                result = self.engine.run_single_strategy_backtest(
                    strategy_name, symbol, start_date, end_date
                )
                
                if 'error' not in result:
                    comparison_results['results'][strategy_name] = result
            
            # Rank strategies by different metrics
            if comparison_results['results']:
                comparison_results['ranking'] = self._rank_strategies(comparison_results['results'])
            
            return comparison_results
            
        except Exception as e:
            logger.error(f"Error in strategy comparison: {e}")
            return {'error': str(e)}
    
    def run_best_combinations_test(
        self, 
        symbols: Optional[List[str]] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> Dict[str, Any]:
        """Test best strategy combinations.
        
        Args:
            symbols: List of symbols
            start_date: Start date
            end_date: End date
            
        Returns:
            Best combinations test results
        """
        logger.info("Testing best strategy combinations")
        
        try:
            if start_date is None:
                start_date = self.default_start_date
            if end_date is None:
                end_date = self.default_end_date
            if symbols is None:
                symbols = self._get_nifty50_symbols()[:10]  # Test on top 10 for speed
            
            # Define promising combinations
            combinations = [
                ('adaptive_rsi', 'ml_enhanced_macd'),
                ('enhanced_pivot', 'volume_confirmed_breakout'),
                ('dynamic_moving_averages', 'adaptive_rsi'),
                ('ml_enhanced_macd', 'volume_confirmed_breakout'),
                ('enhanced_pivot', 'dynamic_moving_averages')
            ]
            
            return self.engine.run_strategy_combination_backtest(
                combinations, symbols, start_date, end_date
            )
            
        except Exception as e:
            logger.error(f"Error in combinations test: {e}")
            return {'error': str(e)}
    
    def run_walk_forward_validation(
        self, 
        strategy_name: str,
        symbols: Optional[List[str]] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> Dict[str, Any]:
        """Run walk-forward validation for a strategy.
        
        Args:
            strategy_name: Strategy to validate
            symbols: List of symbols
            start_date: Start date
            end_date: End date
            
        Returns:
            Walk-forward validation results
        """
        logger.info(f"Running walk-forward validation for {strategy_name}")
        
        try:
            if start_date is None:
                start_date = self.default_start_date
            if end_date is None:
                end_date = self.default_end_date
            if symbols is None:
                symbols = self._get_nifty50_symbols()[:5]  # Test on top 5 for speed
            
            wf_results = {
                'strategy_name': strategy_name,
                'period': f"{start_date} to {end_date}",
                'symbols_tested': len(symbols),
                'symbol_results': {}
            }
            
            # Run walk-forward for each symbol
            for symbol in symbols:
                logger.info(f"Walk-forward validation: {strategy_name} on {symbol}")
                
                result = self.engine.run_walk_forward_analysis(
                    strategy_name, symbol, start_date, end_date
                )
                
                if 'error' not in result:
                    wf_results['symbol_results'][symbol] = result
            
            # Generate validation summary
            wf_results['validation_summary'] = self._generate_wf_summary(wf_results['symbol_results'])
            
            return wf_results
            
        except Exception as e:
            logger.error(f"Error in walk-forward validation: {e}")
            return {'error': str(e)}
    
    def generate_comprehensive_report(
        self, 
        results: Dict[str, Any],
        include_plots: bool = True
    ) -> Dict[str, Any]:
        """Generate comprehensive backtesting report.
        
        Args:
            results: Backtest results
            include_plots: Whether to include plots
            
        Returns:
            Comprehensive report
        """
        try:
            report = {
                'executive_summary': self._create_executive_summary(results),
                'detailed_analysis': self._create_detailed_analysis(results),
                'recommendations': self._create_recommendations(results)
            }
            
            if include_plots:
                report['visualizations'] = self._generate_report_plots(results)
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating comprehensive report: {e}")
            return {'error': str(e)}
    
    def _get_nifty50_symbols(self) -> List[str]:
        """Get Nifty 50 stock symbols."""
        try:
            with db_manager.get_session() as db:
                nifty50_stocks = StockCRUD.get_nifty50_stocks(db)
                return [stock.symbol for stock in nifty50_stocks]
        except Exception as e:
            logger.error(f"Error getting Nifty 50 symbols: {e}")
            return []
    
    def _get_enabled_strategies(self) -> List[str]:
        """Get list of enabled strategies."""
        try:
            strategy_configs = config.get('strategies', {})
            enabled = []
            
            strategy_map = {
                'rsi': 'adaptive_rsi',
                'pivot_points': 'enhanced_pivot',
                'macd': 'ml_enhanced_macd',
                'moving_averages': 'dynamic_moving_averages',
                'breakout': 'volume_confirmed_breakout'
            }
            
            for config_name, strategy_config in strategy_configs.items():
                if strategy_config.get('enabled', False):
                    strategy_name = strategy_map.get(config_name, config_name)
                    enabled.append(strategy_name)
            
            return enabled
            
        except Exception as e:
            logger.error(f"Error getting enabled strategies: {e}")
            return []
    
    def _generate_summary(self, strategy_results: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """Generate summary of backtest results."""
        try:
            summary = {
                'best_strategy': None,
                'best_return': -float('inf'),
                'average_returns': {},
                'win_rates': {},
                'sharpe_ratios': {}
            }
            
            for strategy_name, symbol_results in strategy_results.items():
                if not symbol_results:
                    continue
                
                returns = [result['total_return'] for result in symbol_results.values()]
                win_rates = [result['win_rate'] for result in symbol_results.values()]
                sharpe_ratios = [result['sharpe_ratio'] for result in symbol_results.values()]
                
                avg_return = sum(returns) / len(returns) if returns else 0
                avg_win_rate = sum(win_rates) / len(win_rates) if win_rates else 0
                avg_sharpe = sum(sharpe_ratios) / len(sharpe_ratios) if sharpe_ratios else 0
                
                summary['average_returns'][strategy_name] = avg_return
                summary['win_rates'][strategy_name] = avg_win_rate
                summary['sharpe_ratios'][strategy_name] = avg_sharpe
                
                if avg_return > summary['best_return']:
                    summary['best_return'] = avg_return
                    summary['best_strategy'] = strategy_name
            
            return summary
            
        except Exception as e:
            logger.error(f"Error generating summary: {e}")
            return {}
    
    def _rank_strategies(self, results: Dict[str, Any]) -> Dict[str, List[str]]:
        """Rank strategies by different metrics."""
        try:
            rankings = {}
            
            # Rank by total return
            by_return = sorted(results.items(), key=lambda x: x[1]['total_return'], reverse=True)
            rankings['by_return'] = [name for name, _ in by_return]
            
            # Rank by Sharpe ratio
            by_sharpe = sorted(results.items(), key=lambda x: x[1]['sharpe_ratio'], reverse=True)
            rankings['by_sharpe'] = [name for name, _ in by_sharpe]
            
            # Rank by win rate
            by_win_rate = sorted(results.items(), key=lambda x: x[1]['win_rate'], reverse=True)
            rankings['by_win_rate'] = [name for name, _ in by_win_rate]
            
            # Rank by max drawdown (lower is better)
            by_drawdown = sorted(results.items(), key=lambda x: abs(x[1]['max_drawdown']))
            rankings['by_drawdown'] = [name for name, _ in by_drawdown]
            
            return rankings
            
        except Exception as e:
            logger.error(f"Error ranking strategies: {e}")
            return {}
    
    def _generate_wf_summary(self, symbol_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate walk-forward validation summary."""
        try:
            all_test_returns = []
            all_consistencies = []
            
            for symbol, wf_result in symbol_results.items():
                if 'summary' in wf_result:
                    summary = wf_result['summary']
                    if 'average_return' in summary:
                        all_test_returns.append(summary['average_return'])
                    if 'consistency_ratio' in summary:
                        all_consistencies.append(summary['consistency_ratio'])
            
            return {
                'overall_average_return': sum(all_test_returns) / len(all_test_returns) if all_test_returns else 0,
                'overall_consistency': sum(all_consistencies) / len(all_consistencies) if all_consistencies else 0,
                'symbols_validated': len(symbol_results),
                'validation_passed': sum(all_consistencies) / len(all_consistencies) > 0.6 if all_consistencies else False
            }
            
        except Exception as e:
            logger.error(f"Error generating WF summary: {e}")
            return {}
    
    def _create_executive_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Create executive summary."""
        # Implementation would create high-level summary
        return {'status': 'Executive summary generated'}
    
    def _create_detailed_analysis(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Create detailed analysis."""
        # Implementation would create detailed analysis
        return {'status': 'Detailed analysis generated'}
    
    def _create_recommendations(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Create recommendations based on results."""
        # Implementation would create actionable recommendations
        return {'status': 'Recommendations generated'}
    
    def _generate_report_plots(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate plots for comprehensive report."""
        # Implementation would generate various plots
        return {'status': 'Report plots generated'}

# Global backtest runner instance
backtest_runner = BacktestRunner()

async def run_comprehensive_backtest():
    """Run comprehensive backtesting suite."""
    logger.info("Starting comprehensive backtesting suite")
    
    try:
        # Run all strategies backtest
        all_strategies_results = backtest_runner.run_all_strategies_backtest()
        
        # Run best combinations test
        combinations_results = backtest_runner.run_best_combinations_test()
        
        # Run walk-forward validation for best strategy
        if all_strategies_results.get('summary', {}).get('best_strategy'):
            best_strategy = all_strategies_results['summary']['best_strategy']
            wf_results = backtest_runner.run_walk_forward_validation(best_strategy)
        else:
            wf_results = {}
        
        # Compile comprehensive results
        comprehensive_results = {
            'execution_time': datetime.now().isoformat(),
            'all_strategies': all_strategies_results,
            'combinations': combinations_results,
            'walk_forward_validation': wf_results
        }
        
        logger.info("Comprehensive backtesting suite completed")
        return comprehensive_results
        
    except Exception as e:
        logger.error(f"Error in comprehensive backtest: {e}")
        return {'error': str(e)}

if __name__ == "__main__":
    # Run comprehensive backtest
    results = asyncio.run(run_comprehensive_backtest())
    print("Backtesting completed. Check logs for details.")
