"""
Adaptive RSI Strategy Implementation.
Features dynamic thresholds based on volatility and RSI divergence detection.
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

from .base_strategy import BaseStrategy, TradingSignal, SignalType, SignalStrength
from .strategy_utils import TechnicalAnalysis, PatternRecognition, SignalFilters
from ..utils.logger import get_logger
from ..utils.config import config

logger = get_logger(__name__)

class AdaptiveRSIStrategy(BaseStrategy):
    """
    Adaptive RSI Strategy with dynamic thresholds and divergence detection.
    
    Features:
    - Dynamic RSI thresholds based on market volatility
    - RSI divergence detection for early signals
    - Volume confirmation for signal validation
    - Trend alignment filtering
    - Multiple timeframe analysis
    """
    
    def __init__(self, parameters: Optional[Dict[str, Any]] = None):
        """Initialize Adaptive RSI Strategy.
        
        Args:
            parameters: Strategy parameters override
        """
        default_parameters = {
            'rsi_period': 14,
            'base_oversold': 30,
            'base_overbought': 70,
            'volatility_window': 20,
            'volatility_multiplier': 0.5,
            'min_volume_ratio': 1.2,
            'divergence_lookback': 10,
            'trend_alignment': True,
            'min_signal_strength': 0.6,
            'use_multiple_timeframes': True
        }
        
        if parameters:
            default_parameters.update(parameters)
        
        super().__init__("adaptive_rsi", default_parameters)
        
        # Strategy-specific attributes
        self.rsi_period = self.parameters['rsi_period']
        self.base_oversold = self.parameters['base_oversold']
        self.base_overbought = self.parameters['base_overbought']
        self.volatility_window = self.parameters['volatility_window']
        self.volatility_multiplier = self.parameters['volatility_multiplier']
        self.min_volume_ratio = self.parameters['min_volume_ratio']
        self.divergence_lookback = self.parameters['divergence_lookback']
        self.trend_alignment = self.parameters['trend_alignment']
        self.use_multiple_timeframes = self.parameters['use_multiple_timeframes']
    
    def get_required_indicators(self) -> List[str]:
        """Get required technical indicators."""
        return [
            'rsi_14', 'sma_20', 'sma_50', 'sma_200', 
            'volume_ratio', 'bb_upper', 'bb_lower'
        ]
    
    def calculate_dynamic_thresholds(self, data: pd.DataFrame) -> Tuple[pd.Series, pd.Series]:
        """Calculate dynamic RSI thresholds based on volatility.
        
        Args:
            data: DataFrame with price and indicator data
            
        Returns:
            Tuple of (oversold_threshold, overbought_threshold) series
        """
        # Calculate volatility
        volatility = TechnicalAnalysis.calculate_volatility(
            data['close_price'], 
            self.volatility_window
        )
        
        # Normalize volatility (0-1 scale)
        volatility_normalized = (volatility - volatility.min()) / (volatility.max() - volatility.min())
        volatility_normalized = volatility_normalized.fillna(0.5)  # Default to medium volatility
        
        # Adjust thresholds based on volatility
        # High volatility -> wider thresholds (25-35 for oversold, 65-75 for overbought)
        # Low volatility -> tighter thresholds (closer to 30-70)
        
        volatility_adjustment = volatility_normalized * self.volatility_multiplier * 10
        
        oversold_threshold = self.base_oversold - volatility_adjustment
        overbought_threshold = self.base_overbought + volatility_adjustment
        
        # Ensure thresholds stay within reasonable bounds
        oversold_threshold = oversold_threshold.clip(lower=20, upper=35)
        overbought_threshold = overbought_threshold.clip(lower=65, upper=80)
        
        return oversold_threshold, overbought_threshold
    
    def detect_rsi_divergence(self, data: pd.DataFrame) -> Dict[str, List[int]]:
        """Detect RSI divergences with price.
        
        Args:
            data: DataFrame with price and RSI data
            
        Returns:
            Dictionary with bullish and bearish divergence indices
        """
        if 'rsi_14' not in data.columns or len(data) < self.divergence_lookback * 2:
            return {'bullish': [], 'bearish': []}
        
        return PatternRecognition.detect_divergence(
            data['close_price'],
            data['rsi_14'],
            window=self.divergence_lookback
        )
    
    def calculate_signal_strength(
        self, 
        rsi_value: float, 
        threshold: float, 
        signal_type: SignalType,
        has_divergence: bool = False,
        volume_confirmed: bool = False,
        trend_aligned: bool = False
    ) -> float:
        """Calculate signal strength based on multiple factors.
        
        Args:
            rsi_value: Current RSI value
            threshold: Dynamic threshold
            signal_type: Signal type (BUY/SELL)
            has_divergence: Whether divergence is present
            volume_confirmed: Whether volume confirms signal
            trend_aligned: Whether signal aligns with trend
            
        Returns:
            Signal strength (0.0 to 1.0)
        """
        base_strength = 0.5
        
        # RSI distance from threshold
        if signal_type == SignalType.BUY:
            rsi_distance = max(0, threshold - rsi_value) / threshold
        else:  # SELL
            rsi_distance = max(0, rsi_value - threshold) / (100 - threshold)
        
        base_strength += rsi_distance * 0.3
        
        # Bonus for divergence
        if has_divergence:
            base_strength += 0.2
        
        # Bonus for volume confirmation
        if volume_confirmed:
            base_strength += 0.15
        
        # Bonus for trend alignment
        if trend_aligned:
            base_strength += 0.1
        
        return min(1.0, base_strength)
    
    def analyze_multiple_timeframes(self, data: pd.DataFrame, index: int) -> Dict[str, bool]:
        """Analyze RSI across multiple timeframes.
        
        Args:
            data: DataFrame with RSI data
            index: Current index
            
        Returns:
            Dictionary with timeframe analysis results
        """
        if not self.use_multiple_timeframes or index < 50:
            return {
                'short_term_bullish': False,
                'medium_term_bullish': False,
                'long_term_bullish': False,
                'short_term_bearish': False,
                'medium_term_bearish': False,
                'long_term_bearish': False
            }
        
        current_rsi = data['rsi_14'].iloc[index]
        
        # Short-term (5-day average)
        short_term_rsi = data['rsi_14'].iloc[index-4:index+1].mean()
        
        # Medium-term (10-day average)
        medium_term_rsi = data['rsi_14'].iloc[index-9:index+1].mean()
        
        # Long-term (20-day average)
        long_term_rsi = data['rsi_14'].iloc[index-19:index+1].mean()
        
        return {
            'short_term_bullish': short_term_rsi < 35,
            'medium_term_bullish': medium_term_rsi < 40,
            'long_term_bullish': long_term_rsi < 45,
            'short_term_bearish': short_term_rsi > 65,
            'medium_term_bearish': medium_term_rsi > 60,
            'long_term_bearish': long_term_rsi > 55
        }
    
    def calculate_signals(self, data: pd.DataFrame) -> List[TradingSignal]:
        """Calculate RSI trading signals.
        
        Args:
            data: DataFrame with OHLCV and technical indicator data
            
        Returns:
            List of trading signals
        """
        signals = []
        
        if len(data) < max(self.rsi_period, self.volatility_window) + 10:
            logger.warning("Insufficient data for RSI strategy")
            return signals
        
        # Calculate dynamic thresholds
        oversold_threshold, overbought_threshold = self.calculate_dynamic_thresholds(data)
        
        # Detect divergences
        divergences = self.detect_rsi_divergence(data)
        
        # Detect trend
        trend_direction = TechnicalAnalysis.detect_trend(data['close_price'])
        
        # Iterate through data to find signals
        for i in range(self.volatility_window, len(data)):
            current_date = data['date'].iloc[i]
            current_rsi = data['rsi_14'].iloc[i]
            current_price = data['close_price'].iloc[i]
            current_volume_ratio = data.get('volume_ratio', pd.Series([1.0] * len(data))).iloc[i]
            
            if pd.isna(current_rsi) or pd.isna(current_price):
                continue
            
            # Get dynamic thresholds for current period
            oversold = oversold_threshold.iloc[i]
            overbought = overbought_threshold.iloc[i]
            
            # Check for divergences at current index
            has_bullish_divergence = i in divergences['bullish']
            has_bearish_divergence = i in divergences['bearish']
            
            # Volume confirmation
            volume_confirmed = SignalFilters.volume_confirmation(
                current_price, 
                data['volume'].iloc[i], 
                data['volume'].iloc[max(0, i-20):i].mean(),
                self.min_volume_ratio
            )
            
            # Multiple timeframe analysis
            timeframe_analysis = self.analyze_multiple_timeframes(data, i)
            
            # BUY Signal (Oversold)
            if current_rsi <= oversold or has_bullish_divergence:
                # Trend alignment check
                trend_aligned = SignalFilters.trend_alignment(
                    'BUY', trend_direction, self.trend_alignment
                )
                
                # Multiple timeframe confirmation
                mtf_confirmation = SignalFilters.multiple_timeframe_confirmation(
                    timeframe_analysis['short_term_bullish'],
                    timeframe_analysis['medium_term_bullish'],
                    timeframe_analysis['long_term_bullish'],
                    min_confirmations=1 if has_bullish_divergence else 2
                )
                
                # Calculate signal strength
                signal_strength = self.calculate_signal_strength(
                    current_rsi, oversold, SignalType.BUY,
                    has_bullish_divergence, volume_confirmed, trend_aligned
                )
                
                # Create signal if strength is sufficient
                if signal_strength >= self.parameters['min_signal_strength']:
                    # Calculate stop loss and target
                    atr = TechnicalAnalysis.calculate_atr(
                        data['high_price'], data['low_price'], data['close_price']
                    ).iloc[i]
                    
                    if pd.isna(atr):
                        atr = current_price * 0.02  # 2% fallback
                    
                    stop_loss = current_price - (2 * atr)
                    target_price = current_price + (3 * atr)  # 1.5:1 risk-reward
                    
                    signal = TradingSignal(
                        symbol="",  # Will be set by base class
                        signal_type=SignalType.BUY,
                        signal_strength=signal_strength,
                        price=current_price,
                        target_price=target_price,
                        stop_loss=stop_loss,
                        confidence_score=signal_strength,
                        parameters={
                            'rsi_value': current_rsi,
                            'oversold_threshold': oversold,
                            'has_divergence': has_bullish_divergence,
                            'volume_confirmed': volume_confirmed,
                            'trend_aligned': trend_aligned,
                            'mtf_confirmation': mtf_confirmation
                        },
                        timestamp=datetime.combine(current_date, datetime.min.time())
                    )
                    
                    signals.append(signal)
            
            # SELL Signal (Overbought)
            elif current_rsi >= overbought or has_bearish_divergence:
                # Trend alignment check
                trend_aligned = SignalFilters.trend_alignment(
                    'SELL', trend_direction, self.trend_alignment
                )
                
                # Multiple timeframe confirmation
                mtf_confirmation = SignalFilters.multiple_timeframe_confirmation(
                    timeframe_analysis['short_term_bearish'],
                    timeframe_analysis['medium_term_bearish'],
                    timeframe_analysis['long_term_bearish'],
                    min_confirmations=1 if has_bearish_divergence else 2
                )
                
                # Calculate signal strength
                signal_strength = self.calculate_signal_strength(
                    current_rsi, overbought, SignalType.SELL,
                    has_bearish_divergence, volume_confirmed, trend_aligned
                )
                
                # Create signal if strength is sufficient
                if signal_strength >= self.parameters['min_signal_strength']:
                    # Calculate stop loss and target
                    atr = TechnicalAnalysis.calculate_atr(
                        data['high_price'], data['low_price'], data['close_price']
                    ).iloc[i]
                    
                    if pd.isna(atr):
                        atr = current_price * 0.02  # 2% fallback
                    
                    stop_loss = current_price + (2 * atr)
                    target_price = current_price - (3 * atr)  # 1.5:1 risk-reward
                    
                    signal = TradingSignal(
                        symbol="",  # Will be set by base class
                        signal_type=SignalType.SELL,
                        signal_strength=signal_strength,
                        price=current_price,
                        target_price=target_price,
                        stop_loss=stop_loss,
                        confidence_score=signal_strength,
                        parameters={
                            'rsi_value': current_rsi,
                            'overbought_threshold': overbought,
                            'has_divergence': has_bearish_divergence,
                            'volume_confirmed': volume_confirmed,
                            'trend_aligned': trend_aligned,
                            'mtf_confirmation': mtf_confirmation
                        },
                        timestamp=datetime.combine(current_date, datetime.min.time())
                    )
                    
                    signals.append(signal)
        
        logger.info(f"Generated {len(signals)} RSI signals")
        return signals
