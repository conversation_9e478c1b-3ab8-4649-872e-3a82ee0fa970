"""
Data Pipeline for fetching and processing NSE stock data.
Handles data fetching, validation, corporate actions, and incremental updates.
"""

import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
from typing import List, Dict, Any, Optional, Tuple
import asyncio
import aiohttp
from concurrent.futures import ThreadPoolExecutor
import time

from nsepy import get_history
from nsepy.commons import ParseTables
import yfinance as yf

from .database import db_manager
from .models import StockMetadata, DailyPrice, CorporateAction
from .crud import StockCRUD, PriceCRUD, CorporateActionCRUD
from ..utils.logger import get_logger
from ..utils.config import config

logger = get_logger(__name__)

class DataValidator:
    """Validates stock price data for consistency and accuracy."""
    
    @staticmethod
    def validate_ohlc_data(data: Dict[str, Any]) -> bool:
        """Validate OHLC data consistency."""
        try:
            open_price = float(data.get('open_price', 0))
            high_price = float(data.get('high_price', 0))
            low_price = float(data.get('low_price', 0))
            close_price = float(data.get('close_price', 0))
            volume = int(data.get('volume', 0))
            
            # Basic validations
            if any(price <= 0 for price in [open_price, high_price, low_price, close_price]):
                return False
            
            if volume < 0:
                return False
            
            # OHLC consistency
            if not (low_price <= open_price <= high_price):
                return False
            
            if not (low_price <= close_price <= high_price):
                return False
            
            # Price change validation (max 20% in a day)
            price_change = abs(close_price - open_price) / open_price
            if price_change > 0.20:
                logger.warning(f"Large price change detected: {price_change:.2%}")
            
            return True
            
        except (ValueError, TypeError) as e:
            logger.error(f"Data validation error: {e}")
            return False
    
    @staticmethod
    def validate_volume_spike(current_volume: int, avg_volume: int) -> bool:
        """Check for unusual volume spikes."""
        if avg_volume == 0:
            return True
        
        volume_ratio = current_volume / avg_volume
        if volume_ratio > 10:  # 10x average volume
            logger.warning(f"Unusual volume spike detected: {volume_ratio:.2f}x average")
        
        return volume_ratio < 50  # Reject if more than 50x average

class CorporateActionHandler:
    """Handles corporate actions and price adjustments."""
    
    def __init__(self):
        self.action_multipliers = {}
    
    def fetch_corporate_actions(self, symbol: str, start_date: date, end_date: date) -> List[Dict[str, Any]]:
        """Fetch corporate actions for a symbol."""
        try:
            # Use nsepy to get corporate actions
            # Note: This is a simplified implementation
            # In production, you might need to use NSE's official API or web scraping
            
            actions = []
            
            # Try to get dividend data
            try:
                # This is a placeholder - nsepy doesn't have direct corporate action API
                # You would need to implement web scraping or use other data sources
                pass
            except Exception as e:
                logger.debug(f"No corporate actions found for {symbol}: {e}")
            
            return actions
            
        except Exception as e:
            logger.error(f"Error fetching corporate actions for {symbol}: {e}")
            return []
    
    def adjust_prices_for_actions(
        self, 
        price_data: pd.DataFrame, 
        actions: List[Dict[str, Any]]
    ) -> pd.DataFrame:
        """Adjust historical prices for corporate actions."""
        try:
            adjusted_data = price_data.copy()
            
            for action in sorted(actions, key=lambda x: x['ex_date'], reverse=True):
                ex_date = action['ex_date']
                action_type = action['action_type']
                
                # Apply adjustments to prices before ex-date
                mask = adjusted_data.index < ex_date
                
                if action_type == 'SPLIT':
                    ratio = action['numerator'] / action['denominator']
                    adjusted_data.loc[mask, ['Open', 'High', 'Low', 'Close']] *= ratio
                    adjusted_data.loc[mask, 'Volume'] /= ratio
                
                elif action_type == 'DIVIDEND':
                    dividend_amount = action['dividend_amount']
                    adjusted_data.loc[mask, ['Open', 'High', 'Low', 'Close']] -= dividend_amount
                
                elif action_type == 'BONUS':
                    ratio = (action['numerator'] + action['denominator']) / action['denominator']
                    adjusted_data.loc[mask, ['Open', 'High', 'Low', 'Close']] /= ratio
                    adjusted_data.loc[mask, 'Volume'] *= ratio
            
            return adjusted_data
            
        except Exception as e:
            logger.error(f"Error adjusting prices for corporate actions: {e}")
            return price_data

class NSEDataFetcher:
    """Fetches data from NSE using nsepy and other sources."""
    
    def __init__(self):
        self.rate_limit_delay = 1.0  # seconds between requests
        self.max_retries = 3
        self.validator = DataValidator()
        self.corporate_handler = CorporateActionHandler()
    
    def fetch_stock_data(
        self, 
        symbol: str, 
        start_date: date, 
        end_date: date
    ) -> Optional[pd.DataFrame]:
        """Fetch stock data for a symbol and date range."""
        try:
            logger.info(f"Fetching data for {symbol} from {start_date} to {end_date}")
            
            # Add rate limiting
            time.sleep(self.rate_limit_delay)
            
            # Fetch data using nsepy
            data = get_history(
                symbol=symbol,
                start=start_date,
                end=end_date,
                index=False
            )
            
            if data.empty:
                logger.warning(f"No data found for {symbol}")
                return None
            
            # Rename columns to match our schema
            data = data.rename(columns={
                'Open': 'open_price',
                'High': 'high_price',
                'Low': 'low_price',
                'Close': 'close_price',
                'Volume': 'volume',
                'Turnover': 'turnover'
            })
            
            # Add date column
            data['date'] = data.index.date
            
            # Validate data
            valid_data = []
            for _, row in data.iterrows():
                if self.validator.validate_ohlc_data(row.to_dict()):
                    valid_data.append(row)
                else:
                    logger.warning(f"Invalid data point for {symbol} on {row.name}")
            
            if not valid_data:
                logger.error(f"No valid data found for {symbol}")
                return None
            
            return pd.DataFrame(valid_data)
            
        except Exception as e:
            logger.error(f"Error fetching data for {symbol}: {e}")
            return None
    
    def fetch_with_retry(
        self, 
        symbol: str, 
        start_date: date, 
        end_date: date
    ) -> Optional[pd.DataFrame]:
        """Fetch data with retry logic."""
        for attempt in range(self.max_retries):
            try:
                data = self.fetch_stock_data(symbol, start_date, end_date)
                if data is not None:
                    return data
                
            except Exception as e:
                logger.warning(f"Attempt {attempt + 1} failed for {symbol}: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff
        
        logger.error(f"Failed to fetch data for {symbol} after {self.max_retries} attempts")
        return None

class TechnicalIndicatorCalculator:
    """Calculates technical indicators for stock data."""
    
    @staticmethod
    def calculate_sma(prices: pd.Series, period: int) -> pd.Series:
        """Calculate Simple Moving Average."""
        return prices.rolling(window=period, min_periods=period).mean()
    
    @staticmethod
    def calculate_ema(prices: pd.Series, period: int) -> pd.Series:
        """Calculate Exponential Moving Average."""
        return prices.ewm(span=period, adjust=False).mean()
    
    @staticmethod
    def calculate_rsi(prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate Relative Strength Index."""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    @staticmethod
    def calculate_macd(
        prices: pd.Series, 
        fast: int = 12, 
        slow: int = 26, 
        signal: int = 9
    ) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """Calculate MACD, Signal line, and Histogram."""
        ema_fast = TechnicalIndicatorCalculator.calculate_ema(prices, fast)
        ema_slow = TechnicalIndicatorCalculator.calculate_ema(prices, slow)
        
        macd = ema_fast - ema_slow
        signal_line = TechnicalIndicatorCalculator.calculate_ema(macd, signal)
        histogram = macd - signal_line
        
        return macd, signal_line, histogram
    
    @staticmethod
    def calculate_bollinger_bands(
        prices: pd.Series, 
        period: int = 20, 
        std_dev: float = 2
    ) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """Calculate Bollinger Bands."""
        sma = TechnicalIndicatorCalculator.calculate_sma(prices, period)
        std = prices.rolling(window=period).std()
        
        upper_band = sma + (std * std_dev)
        lower_band = sma - (std * std_dev)
        
        return upper_band, sma, lower_band
    
    @staticmethod
    def calculate_pivot_points(
        high: float, 
        low: float, 
        close: float
    ) -> Dict[str, float]:
        """Calculate pivot points and support/resistance levels."""
        pivot = (high + low + close) / 3
        
        r1 = (2 * pivot) - low
        r2 = pivot + (high - low)
        s1 = (2 * pivot) - high
        s2 = pivot - (high - low)
        
        return {
            'pivot_point': pivot,
            'resistance_1': r1,
            'resistance_2': r2,
            'support_1': s1,
            'support_2': s2
        }
    
    def add_technical_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add all technical indicators to the dataframe."""
        try:
            # Sort by date to ensure proper calculation
            data = data.sort_values('date')
            
            # Moving averages
            data['sma_20'] = self.calculate_sma(data['close_price'], 20)
            data['sma_50'] = self.calculate_sma(data['close_price'], 50)
            data['sma_200'] = self.calculate_sma(data['close_price'], 200)
            
            data['ema_12'] = self.calculate_ema(data['close_price'], 12)
            data['ema_26'] = self.calculate_ema(data['close_price'], 26)
            
            # RSI
            data['rsi_14'] = self.calculate_rsi(data['close_price'], 14)
            
            # MACD
            macd, signal, histogram = self.calculate_macd(data['close_price'])
            data['macd'] = macd
            data['macd_signal'] = signal
            data['macd_histogram'] = histogram
            
            # Bollinger Bands
            bb_upper, bb_middle, bb_lower = self.calculate_bollinger_bands(data['close_price'])
            data['bb_upper'] = bb_upper
            data['bb_middle'] = bb_middle
            data['bb_lower'] = bb_lower
            
            # Volume indicators
            data['volume_sma_20'] = self.calculate_sma(data['volume'], 20)
            data['volume_ratio'] = data['volume'] / data['volume_sma_20']
            
            # Pivot points (calculated for each day)
            pivot_data = []
            for _, row in data.iterrows():
                pivots = self.calculate_pivot_points(
                    row['high_price'], 
                    row['low_price'], 
                    row['close_price']
                )
                pivot_data.append(pivots)
            
            pivot_df = pd.DataFrame(pivot_data)
            data = pd.concat([data, pivot_df], axis=1)
            
            return data

        except Exception as e:
            logger.error(f"Error calculating technical indicators: {e}")
            return data

class DataPipeline:
    """Main data pipeline for fetching, processing, and storing stock data."""

    def __init__(self):
        self.fetcher = NSEDataFetcher()
        self.indicator_calculator = TechnicalIndicatorCalculator()
        self.corporate_handler = CorporateActionHandler()
        self.validator = DataValidator()

    async def update_stock_data(
        self,
        symbol: str,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> bool:
        """Update data for a single stock."""
        try:
            with db_manager.get_session() as db:
                # Get stock metadata
                stock = StockCRUD.get_stock_by_symbol(db, symbol)
                if not stock:
                    logger.error(f"Stock {symbol} not found in database")
                    return False

                # Determine date range
                if start_date is None:
                    latest_price = PriceCRUD.get_latest_price(db, stock.id)
                    if latest_price:
                        start_date = latest_price.date + timedelta(days=1)
                    else:
                        start_date = stock.listing_date

                if end_date is None:
                    end_date = date.today()

                # Skip if no new data needed
                if start_date > end_date:
                    logger.info(f"No new data needed for {symbol}")
                    return True

                # Fetch data
                data = self.fetcher.fetch_with_retry(symbol, start_date, end_date)
                if data is None or data.empty:
                    logger.warning(f"No data fetched for {symbol}")
                    return False

                # Add technical indicators
                data = self.indicator_calculator.add_technical_indicators(data)

                # Prepare records for database
                records = []
                for _, row in data.iterrows():
                    record = {
                        'stock_id': stock.id,
                        'date': row['date'],
                        'open_price': row['open_price'],
                        'high_price': row['high_price'],
                        'low_price': row['low_price'],
                        'close_price': row['close_price'],
                        'volume': row['volume'],
                        'turnover': row.get('turnover'),

                        # Technical indicators
                        'sma_20': row.get('sma_20'),
                        'sma_50': row.get('sma_50'),
                        'sma_200': row.get('sma_200'),
                        'ema_12': row.get('ema_12'),
                        'ema_26': row.get('ema_26'),
                        'rsi_14': row.get('rsi_14'),
                        'macd': row.get('macd'),
                        'macd_signal': row.get('macd_signal'),
                        'macd_histogram': row.get('macd_histogram'),
                        'bb_upper': row.get('bb_upper'),
                        'bb_middle': row.get('bb_middle'),
                        'bb_lower': row.get('bb_lower'),
                        'volume_sma_20': row.get('volume_sma_20'),
                        'volume_ratio': row.get('volume_ratio'),
                        'pivot_point': row.get('pivot_point'),
                        'resistance_1': row.get('resistance_1'),
                        'resistance_2': row.get('resistance_2'),
                        'support_1': row.get('support_1'),
                        'support_2': row.get('support_2'),
                    }
                    records.append(record)

                # Bulk insert/update
                count = PriceCRUD.bulk_upsert_prices(db, records)
                logger.info(f"Updated {count} price records for {symbol}")

                return True

        except Exception as e:
            logger.error(f"Error updating data for {symbol}: {e}")
            return False

    async def update_nifty50_data(self) -> Dict[str, Any]:
        """Update data for all Nifty 50 stocks."""
        try:
            with db_manager.get_session() as db:
                nifty50_stocks = StockCRUD.get_nifty50_stocks(db)

            logger.info(f"Starting data update for {len(nifty50_stocks)} Nifty 50 stocks")

            results = {
                'total_stocks': len(nifty50_stocks),
                'successful_updates': 0,
                'failed_updates': 0,
                'failed_symbols': []
            }

            # Process stocks with concurrency control
            semaphore = asyncio.Semaphore(5)  # Limit concurrent requests

            async def update_with_semaphore(stock):
                async with semaphore:
                    success = await self.update_stock_data(stock.symbol)
                    if success:
                        results['successful_updates'] += 1
                    else:
                        results['failed_updates'] += 1
                        results['failed_symbols'].append(stock.symbol)

            # Run updates concurrently
            tasks = [update_with_semaphore(stock) for stock in nifty50_stocks]
            await asyncio.gather(*tasks, return_exceptions=True)

            logger.info(f"Data update completed: {results}")
            return results

        except Exception as e:
            logger.error(f"Error updating Nifty 50 data: {e}")
            return {'error': str(e)}

    async def update_corporate_actions(self, symbol: str) -> bool:
        """Update corporate actions for a stock."""
        try:
            with db_manager.get_session() as db:
                stock = StockCRUD.get_stock_by_symbol(db, symbol)
                if not stock:
                    return False

                # Get date range for corporate actions
                end_date = date.today()
                start_date = end_date - timedelta(days=365)  # Last 1 year

                # Fetch corporate actions
                actions = self.corporate_handler.fetch_corporate_actions(
                    symbol, start_date, end_date
                )

                # Store in database
                for action_data in actions:
                    action_data['stock_id'] = stock.id
                    CorporateActionCRUD.create_corporate_action(db, action_data)

                logger.info(f"Updated {len(actions)} corporate actions for {symbol}")
                return True

        except Exception as e:
            logger.error(f"Error updating corporate actions for {symbol}: {e}")
            return False

    def get_data_quality_report(self) -> Dict[str, Any]:
        """Generate a data quality report."""
        try:
            with db_manager.get_session() as db:
                # Get basic statistics
                total_stocks = db.query(StockMetadata).filter(StockMetadata.is_active == True).count()

                # Get data coverage
                latest_dates = db.query(
                    StockMetadata.symbol,
                    func.max(DailyPrice.date).label('latest_date')
                ).join(DailyPrice).filter(
                    StockMetadata.is_active == True
                ).group_by(StockMetadata.symbol).all()

                today = date.today()
                up_to_date_count = sum(1 for _, latest_date in latest_dates
                                     if latest_date and (today - latest_date).days <= 1)

                # Calculate data quality metrics
                report = {
                    'total_active_stocks': total_stocks,
                    'stocks_with_data': len(latest_dates),
                    'up_to_date_stocks': up_to_date_count,
                    'data_coverage_percentage': (len(latest_dates) / total_stocks * 100) if total_stocks > 0 else 0,
                    'up_to_date_percentage': (up_to_date_count / total_stocks * 100) if total_stocks > 0 else 0,
                    'report_date': today.isoformat()
                }

                return report

        except Exception as e:
            logger.error(f"Error generating data quality report: {e}")
            return {'error': str(e)}

    async def run_daily_update(self) -> Dict[str, Any]:
        """Run the daily data update process."""
        logger.info("Starting daily data update process")

        start_time = datetime.now()

        try:
            # Update Nifty 50 data
            nifty_results = await self.update_nifty50_data()

            # Generate data quality report
            quality_report = self.get_data_quality_report()

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            results = {
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'duration_seconds': duration,
                'nifty50_update': nifty_results,
                'data_quality': quality_report,
                'status': 'completed'
            }

            logger.info(f"Daily update completed in {duration:.2f} seconds")
            return results

        except Exception as e:
            logger.error(f"Daily update failed: {e}")
            return {
                'status': 'failed',
                'error': str(e),
                'start_time': start_time.isoformat()
            }

# Global pipeline instance
data_pipeline = DataPipeline()

async def run_data_update():
    """Run data update - can be called from command line or scheduler."""
    return await data_pipeline.run_daily_update()

if __name__ == "__main__":
    asyncio.run(run_data_update())
