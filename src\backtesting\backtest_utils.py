"""
Backtesting Utilities - Helper functions for backtesting operations.
"""

import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
from typing import Dict, List, Any, Optional, Tuple
import json
from pathlib import Path

from ..utils.logger import get_logger
from ..utils.config import config

logger = get_logger(__name__)

class PerformanceMetrics:
    """Calculate various performance metrics for backtesting."""
    
    @staticmethod
    def calculate_sharpe_ratio(returns: pd.Series, risk_free_rate: float = 0.06) -> float:
        """Calculate Sharpe ratio.
        
        Args:
            returns: Series of returns
            risk_free_rate: Risk-free rate (default 6% for India)
            
        Returns:
            Sharpe ratio
        """
        try:
            if len(returns) == 0 or returns.std() == 0:
                return 0.0
            
            excess_returns = returns - (risk_free_rate / 252)  # Daily risk-free rate
            return excess_returns.mean() / returns.std() * np.sqrt(252)  # Annualized
            
        except Exception as e:
            logger.error(f"Error calculating Sharpe ratio: {e}")
            return 0.0
    
    @staticmethod
    def calculate_sortino_ratio(returns: pd.Series, risk_free_rate: float = 0.06) -> float:
        """Calculate Sortino ratio (downside deviation).
        
        Args:
            returns: Series of returns
            risk_free_rate: Risk-free rate
            
        Returns:
            Sortino ratio
        """
        try:
            if len(returns) == 0:
                return 0.0
            
            excess_returns = returns - (risk_free_rate / 252)
            downside_returns = returns[returns < 0]
            
            if len(downside_returns) == 0 or downside_returns.std() == 0:
                return float('inf') if excess_returns.mean() > 0 else 0.0
            
            return excess_returns.mean() / downside_returns.std() * np.sqrt(252)
            
        except Exception as e:
            logger.error(f"Error calculating Sortino ratio: {e}")
            return 0.0
    
    @staticmethod
    def calculate_calmar_ratio(returns: pd.Series, max_drawdown: float) -> float:
        """Calculate Calmar ratio.
        
        Args:
            returns: Series of returns
            max_drawdown: Maximum drawdown percentage
            
        Returns:
            Calmar ratio
        """
        try:
            if max_drawdown == 0:
                return float('inf') if returns.mean() > 0 else 0.0
            
            annual_return = (1 + returns.mean()) ** 252 - 1
            return annual_return / abs(max_drawdown)
            
        except Exception as e:
            logger.error(f"Error calculating Calmar ratio: {e}")
            return 0.0
    
    @staticmethod
    def calculate_max_drawdown(equity_curve: pd.Series) -> Tuple[float, int, int]:
        """Calculate maximum drawdown.
        
        Args:
            equity_curve: Series of portfolio values
            
        Returns:
            Tuple of (max_drawdown_pct, start_idx, end_idx)
        """
        try:
            if len(equity_curve) == 0:
                return 0.0, 0, 0
            
            # Calculate running maximum
            peak = equity_curve.expanding().max()
            
            # Calculate drawdown
            drawdown = (equity_curve - peak) / peak
            
            # Find maximum drawdown
            max_dd = drawdown.min()
            max_dd_idx = drawdown.idxmin()
            
            # Find start of drawdown period
            start_idx = peak[:max_dd_idx].idxmax()
            
            return abs(max_dd) * 100, start_idx, max_dd_idx
            
        except Exception as e:
            logger.error(f"Error calculating max drawdown: {e}")
            return 0.0, 0, 0
    
    @staticmethod
    def calculate_var(returns: pd.Series, confidence_level: float = 0.05) -> float:
        """Calculate Value at Risk.
        
        Args:
            returns: Series of returns
            confidence_level: Confidence level (default 5%)
            
        Returns:
            VaR value
        """
        try:
            if len(returns) == 0:
                return 0.0
            
            return np.percentile(returns, confidence_level * 100)
            
        except Exception as e:
            logger.error(f"Error calculating VaR: {e}")
            return 0.0
    
    @staticmethod
    def calculate_cvar(returns: pd.Series, confidence_level: float = 0.05) -> float:
        """Calculate Conditional Value at Risk (Expected Shortfall).
        
        Args:
            returns: Series of returns
            confidence_level: Confidence level
            
        Returns:
            CVaR value
        """
        try:
            if len(returns) == 0:
                return 0.0
            
            var = PerformanceMetrics.calculate_var(returns, confidence_level)
            return returns[returns <= var].mean()
            
        except Exception as e:
            logger.error(f"Error calculating CVaR: {e}")
            return 0.0

class BacktestValidator:
    """Validate backtesting results and detect common issues."""
    
    @staticmethod
    def validate_results(results: Dict[str, Any]) -> Dict[str, Any]:
        """Validate backtesting results.
        
        Args:
            results: Backtest results dictionary
            
        Returns:
            Validation report
        """
        validation_report = {
            'is_valid': True,
            'warnings': [],
            'errors': [],
            'recommendations': []
        }
        
        try:
            # Check for basic required fields
            required_fields = ['total_return', 'sharpe_ratio', 'max_drawdown', 'total_trades']
            for field in required_fields:
                if field not in results:
                    validation_report['errors'].append(f"Missing required field: {field}")
                    validation_report['is_valid'] = False
            
            if not validation_report['is_valid']:
                return validation_report
            
            # Check for unrealistic returns
            if results['total_return'] > 1000:  # 1000% return
                validation_report['warnings'].append("Extremely high returns detected - check for data issues")
            
            if results['total_return'] < -90:  # 90% loss
                validation_report['warnings'].append("Extremely high losses detected - review risk management")
            
            # Check Sharpe ratio
            if results['sharpe_ratio'] > 3:
                validation_report['warnings'].append("Very high Sharpe ratio - may indicate overfitting")
            
            if results['sharpe_ratio'] < 0:
                validation_report['warnings'].append("Negative Sharpe ratio - strategy underperforms risk-free rate")
            
            # Check drawdown
            if results['max_drawdown'] > 50:
                validation_report['warnings'].append("High maximum drawdown - consider risk management improvements")
            
            # Check trade count
            if results['total_trades'] < 10:
                validation_report['warnings'].append("Low number of trades - results may not be statistically significant")
            
            if results['total_trades'] > 1000:
                validation_report['warnings'].append("Very high number of trades - check for overtrading")
            
            # Check win rate
            if 'win_rate' in results:
                if results['win_rate'] > 90:
                    validation_report['warnings'].append("Extremely high win rate - may indicate look-ahead bias")
                
                if results['win_rate'] < 30:
                    validation_report['warnings'].append("Low win rate - review strategy logic")
            
            # Generate recommendations
            if results['sharpe_ratio'] < 1:
                validation_report['recommendations'].append("Consider improving risk-adjusted returns")
            
            if results['max_drawdown'] > 20:
                validation_report['recommendations'].append("Implement better risk management and position sizing")
            
            if results.get('win_rate', 0) < 50 and results.get('profit_factor', 0) < 1.5:
                validation_report['recommendations'].append("Strategy may need refinement - low win rate and profit factor")
            
        except Exception as e:
            validation_report['errors'].append(f"Validation error: {str(e)}")
            validation_report['is_valid'] = False
        
        return validation_report
    
    @staticmethod
    def detect_overfitting(results: Dict[str, Any]) -> Dict[str, Any]:
        """Detect potential overfitting in backtest results.
        
        Args:
            results: Backtest results
            
        Returns:
            Overfitting analysis
        """
        overfitting_signals = []
        
        try:
            # Check for perfect or near-perfect metrics
            if results.get('win_rate', 0) > 95:
                overfitting_signals.append("Win rate too high (>95%)")
            
            if results.get('sharpe_ratio', 0) > 4:
                overfitting_signals.append("Sharpe ratio too high (>4)")
            
            if results.get('max_drawdown', 100) < 2:
                overfitting_signals.append("Maximum drawdown too low (<2%)")
            
            # Check for unrealistic profit factors
            if results.get('profit_factor', 0) > 5:
                overfitting_signals.append("Profit factor too high (>5)")
            
            # Check trade distribution
            if results.get('total_trades', 0) > 0:
                avg_trade_return = results.get('total_return', 0) / results['total_trades']
                if avg_trade_return > 5:  # 5% per trade
                    overfitting_signals.append("Average trade return too high")
            
            return {
                'overfitting_detected': len(overfitting_signals) > 0,
                'signals': overfitting_signals,
                'confidence': min(len(overfitting_signals) * 0.3, 1.0)
            }
            
        except Exception as e:
            logger.error(f"Error detecting overfitting: {e}")
            return {'overfitting_detected': False, 'error': str(e)}

class ReportGenerator:
    """Generate formatted reports from backtesting results."""
    
    def __init__(self, output_dir: str = "reports/backtesting"):
        """Initialize report generator.
        
        Args:
            output_dir: Output directory for reports
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def generate_html_report(self, results: Dict[str, Any]) -> str:
        """Generate HTML report from results.
        
        Args:
            results: Backtest results
            
        Returns:
            Path to generated HTML file
        """
        try:
            html_content = self._create_html_template(results)
            
            # Save HTML file
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"backtest_report_{results.get('strategy_name', 'unknown')}_{timestamp}.html"
            filepath = self.output_dir / filename
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            logger.info(f"HTML report generated: {filepath}")
            return str(filepath)
            
        except Exception as e:
            logger.error(f"Error generating HTML report: {e}")
            return ""
    
    def _create_html_template(self, results: Dict[str, Any]) -> str:
        """Create HTML template for report."""
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Backtest Report - {results.get('strategy_name', 'Unknown Strategy')}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
                .metrics {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }}
                .metric-card {{ background-color: #f9f9f9; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff; }}
                .metric-value {{ font-size: 24px; font-weight: bold; color: #007bff; }}
                .metric-label {{ font-size: 14px; color: #666; }}
                table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                th, td {{ padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }}
                th {{ background-color: #f0f0f0; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Backtest Report</h1>
                <h2>{results.get('strategy_name', 'Unknown Strategy')} - {results.get('symbol', 'Unknown Symbol')}</h2>
                <p>Period: {results.get('start_date', 'N/A')} to {results.get('end_date', 'N/A')}</p>
            </div>
            
            <div class="metrics">
                <div class="metric-card">
                    <div class="metric-value">{results.get('total_return', 0):.2f}%</div>
                    <div class="metric-label">Total Return</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{results.get('annual_return', 0):.2f}%</div>
                    <div class="metric-label">Annual Return</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{results.get('sharpe_ratio', 0):.3f}</div>
                    <div class="metric-label">Sharpe Ratio</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{results.get('max_drawdown', 0):.2f}%</div>
                    <div class="metric-label">Max Drawdown</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{results.get('win_rate', 0):.1f}%</div>
                    <div class="metric-label">Win Rate</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{results.get('total_trades', 0)}</div>
                    <div class="metric-label">Total Trades</div>
                </div>
            </div>
            
            <h3>Trade Statistics</h3>
            <table>
                <tr>
                    <th>Metric</th>
                    <th>Value</th>
                </tr>
                <tr>
                    <td>Winning Trades</td>
                    <td>{results.get('winning_trades', 0)}</td>
                </tr>
                <tr>
                    <td>Losing Trades</td>
                    <td>{results.get('losing_trades', 0)}</td>
                </tr>
                <tr>
                    <td>Average Win</td>
                    <td>₹{results.get('avg_win', 0):.2f}</td>
                </tr>
                <tr>
                    <td>Average Loss</td>
                    <td>₹{results.get('avg_loss', 0):.2f}</td>
                </tr>
                <tr>
                    <td>Profit Factor</td>
                    <td>{results.get('profit_factor', 0):.2f}</td>
                </tr>
            </table>
            
            <p><em>Report generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</em></p>
        </body>
        </html>
        """
    
    def generate_csv_report(self, results: Dict[str, Any]) -> str:
        """Generate CSV report from results.
        
        Args:
            results: Backtest results
            
        Returns:
            Path to generated CSV file
        """
        try:
            # Create DataFrame from results
            data = {
                'Strategy': [results.get('strategy_name', 'Unknown')],
                'Symbol': [results.get('symbol', 'Unknown')],
                'Start Date': [results.get('start_date', 'N/A')],
                'End Date': [results.get('end_date', 'N/A')],
                'Total Return (%)': [results.get('total_return', 0)],
                'Annual Return (%)': [results.get('annual_return', 0)],
                'Sharpe Ratio': [results.get('sharpe_ratio', 0)],
                'Max Drawdown (%)': [results.get('max_drawdown', 0)],
                'Win Rate (%)': [results.get('win_rate', 0)],
                'Total Trades': [results.get('total_trades', 0)],
                'Winning Trades': [results.get('winning_trades', 0)],
                'Losing Trades': [results.get('losing_trades', 0)],
                'Profit Factor': [results.get('profit_factor', 0)]
            }
            
            df = pd.DataFrame(data)
            
            # Save CSV file
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"backtest_results_{results.get('strategy_name', 'unknown')}_{timestamp}.csv"
            filepath = self.output_dir / filename
            
            df.to_csv(filepath, index=False)
            
            logger.info(f"CSV report generated: {filepath}")
            return str(filepath)
            
        except Exception as e:
            logger.error(f"Error generating CSV report: {e}")
            return ""

# Utility functions
def compare_strategies(results_list: List[Dict[str, Any]]) -> pd.DataFrame:
    """Compare multiple strategy results.
    
    Args:
        results_list: List of backtest results
        
    Returns:
        Comparison DataFrame
    """
    try:
        comparison_data = []
        
        for result in results_list:
            comparison_data.append({
                'Strategy': result.get('strategy_name', 'Unknown'),
                'Symbol': result.get('symbol', 'Unknown'),
                'Total Return (%)': result.get('total_return', 0),
                'Annual Return (%)': result.get('annual_return', 0),
                'Sharpe Ratio': result.get('sharpe_ratio', 0),
                'Max Drawdown (%)': result.get('max_drawdown', 0),
                'Win Rate (%)': result.get('win_rate', 0),
                'Total Trades': result.get('total_trades', 0),
                'Profit Factor': result.get('profit_factor', 0)
            })
        
        df = pd.DataFrame(comparison_data)
        return df.sort_values('Sharpe Ratio', ascending=False)
        
    except Exception as e:
        logger.error(f"Error comparing strategies: {e}")
        return pd.DataFrame()

def calculate_portfolio_metrics(individual_results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Calculate portfolio-level metrics from individual results.
    
    Args:
        individual_results: List of individual strategy results
        
    Returns:
        Portfolio metrics
    """
    try:
        if not individual_results:
            return {}
        
        returns = [result.get('total_return', 0) for result in individual_results]
        sharpe_ratios = [result.get('sharpe_ratio', 0) for result in individual_results]
        drawdowns = [result.get('max_drawdown', 0) for result in individual_results]
        
        return {
            'portfolio_return': np.mean(returns),
            'portfolio_volatility': np.std(returns),
            'portfolio_sharpe': np.mean(sharpe_ratios),
            'portfolio_max_drawdown': np.max(drawdowns),
            'diversification_ratio': len(individual_results),
            'best_performer': max(returns),
            'worst_performer': min(returns)
        }
        
    except Exception as e:
        logger.error(f"Error calculating portfolio metrics: {e}")
        return {}
