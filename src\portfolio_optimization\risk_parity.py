"""
Risk Parity Portfolio Optimization.
Implements Equal Risk Contribution (ERC) and Hierarchical Risk Parity (HRP) approaches.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from scipy.optimize import minimize
from scipy.cluster.hierarchy import linkage, dendrogram, fcluster
from scipy.spatial.distance import squareform
import warnings
warnings.filterwarnings('ignore')

from .mpt_optimizer import MPTOptimizer, OptimizationResult, PortfolioConstraints
from ..utils.logger import get_logger

logger = get_logger(__name__)

class RiskParityOptimizer:
    """Risk Parity portfolio optimizer."""
    
    def __init__(self):
        """Initialize Risk Parity optimizer."""
        self.mpt_optimizer = MPTOptimizer()
        
        logger.info("Risk Parity Optimizer initialized")
    
    def calculate_risk_contributions(
        self, 
        weights: np.ndarray, 
        covariance_matrix: np.ndarray
    ) -> np.ndarray:
        """Calculate risk contributions for each asset."""
        try:
            portfolio_variance = np.dot(weights.T, np.dot(covariance_matrix, weights))
            
            if portfolio_variance == 0:
                return np.zeros(len(weights))
            
            # Marginal risk contributions
            marginal_contributions = np.dot(covariance_matrix, weights)
            
            # Risk contributions = weight * marginal contribution / portfolio variance
            risk_contributions = weights * marginal_contributions / portfolio_variance
            
            return risk_contributions
            
        except Exception as e:
            logger.error(f"Error calculating risk contributions: {e}")
            return np.zeros(len(weights))
    
    def optimize_equal_risk_contribution(
        self, 
        covariance_matrix: np.ndarray,
        constraints: PortfolioConstraints
    ) -> np.ndarray:
        """Optimize for Equal Risk Contribution (ERC)."""
        try:
            n_assets = len(covariance_matrix)
            
            # Objective function: minimize sum of squared deviations from equal risk
            def objective(weights):
                risk_contributions = self.calculate_risk_contributions(weights, covariance_matrix)
                target_risk = 1.0 / n_assets  # Equal risk target
                
                # Sum of squared deviations from target
                deviations = risk_contributions - target_risk
                return np.sum(deviations ** 2)
            
            # Constraints
            constraints_list = [
                {'type': 'eq', 'fun': lambda x: np.sum(x) - 1.0}  # Weights sum to 1
            ]
            
            # Bounds
            bounds = [(constraints.min_weight, min(constraints.max_weight, constraints.max_concentration)) 
                     for _ in range(n_assets)]
            
            # Initial guess (equal weights)
            initial_guess = np.array([1.0 / n_assets] * n_assets)
            
            # Optimize
            result = minimize(
                objective,
                initial_guess,
                method='SLSQP',
                bounds=bounds,
                constraints=constraints_list,
                options={'maxiter': 1000, 'ftol': 1e-9}
            )
            
            if not result.success:
                logger.warning(f"ERC optimization failed: {result.message}")
                return initial_guess
            
            return result.x
            
        except Exception as e:
            logger.error(f"Error in ERC optimization: {e}")
            return np.array([1.0 / len(covariance_matrix)] * len(covariance_matrix))
    
    def calculate_hierarchical_clustering(
        self, 
        correlation_matrix: np.ndarray
    ) -> Tuple[np.ndarray, List[int]]:
        """Perform hierarchical clustering on correlation matrix."""
        try:
            # Convert correlation to distance
            distance_matrix = np.sqrt(0.5 * (1 - correlation_matrix))
            
            # Perform hierarchical clustering
            condensed_distances = squareform(distance_matrix, checks=False)
            linkage_matrix = linkage(condensed_distances, method='ward')
            
            # Get cluster order
            n_assets = len(correlation_matrix)
            cluster_order = self._get_cluster_order(linkage_matrix, n_assets)
            
            return linkage_matrix, cluster_order
            
        except Exception as e:
            logger.error(f"Error in hierarchical clustering: {e}")
            return np.array([]), list(range(len(correlation_matrix)))
    
    def _get_cluster_order(self, linkage_matrix: np.ndarray, n_assets: int) -> List[int]:
        """Get the order of assets from hierarchical clustering."""
        try:
            # Get dendrogram order
            dendro = dendrogram(linkage_matrix, no_plot=True)
            cluster_order = dendro['leaves']
            
            return cluster_order
            
        except Exception as e:
            logger.error(f"Error getting cluster order: {e}")
            return list(range(n_assets))
    
    def optimize_hierarchical_risk_parity(
        self, 
        expected_returns: np.ndarray,
        covariance_matrix: np.ndarray,
        constraints: PortfolioConstraints
    ) -> np.ndarray:
        """Optimize using Hierarchical Risk Parity (HRP)."""
        try:
            n_assets = len(expected_returns)
            
            # Calculate correlation matrix
            volatilities = np.sqrt(np.diag(covariance_matrix))
            correlation_matrix = covariance_matrix / np.outer(volatilities, volatilities)
            
            # Perform hierarchical clustering
            linkage_matrix, cluster_order = self.calculate_hierarchical_clustering(correlation_matrix)
            
            # Reorder matrices according to clustering
            ordered_cov = covariance_matrix[np.ix_(cluster_order, cluster_order)]
            ordered_returns = expected_returns[cluster_order]
            
            # Calculate HRP weights
            hrp_weights = self._calculate_hrp_weights(ordered_cov, ordered_returns)
            
            # Reorder weights back to original order
            weights = np.zeros(n_assets)
            for i, original_idx in enumerate(cluster_order):
                weights[original_idx] = hrp_weights[i]
            
            # Apply constraints
            weights = self._apply_weight_constraints(weights, constraints)
            
            return weights
            
        except Exception as e:
            logger.error(f"Error in HRP optimization: {e}")
            return np.array([1.0 / n_assets] * n_assets)
    
    def _calculate_hrp_weights(
        self, 
        covariance_matrix: np.ndarray, 
        expected_returns: np.ndarray
    ) -> np.ndarray:
        """Calculate HRP weights using recursive bisection."""
        try:
            n_assets = len(expected_returns)
            
            if n_assets == 1:
                return np.array([1.0])
            
            if n_assets == 2:
                # For two assets, use inverse volatility weighting
                volatilities = np.sqrt(np.diag(covariance_matrix))
                inv_vol_weights = (1.0 / volatilities) / np.sum(1.0 / volatilities)
                return inv_vol_weights
            
            # Recursive bisection
            mid_point = n_assets // 2
            
            # Left cluster
            left_cov = covariance_matrix[:mid_point, :mid_point]
            left_returns = expected_returns[:mid_point]
            left_weights = self._calculate_hrp_weights(left_cov, left_returns)
            
            # Right cluster
            right_cov = covariance_matrix[mid_point:, mid_point:]
            right_returns = expected_returns[mid_point:]
            right_weights = self._calculate_hrp_weights(right_cov, right_returns)
            
            # Calculate cluster variances
            left_cluster_var = np.dot(left_weights.T, np.dot(left_cov, left_weights))
            right_cluster_var = np.dot(right_weights.T, np.dot(right_cov, right_weights))
            
            # Allocate between clusters using inverse variance
            total_inv_var = (1.0 / left_cluster_var) + (1.0 / right_cluster_var)
            left_allocation = (1.0 / left_cluster_var) / total_inv_var
            right_allocation = (1.0 / right_cluster_var) / total_inv_var
            
            # Combine weights
            final_weights = np.concatenate([
                left_weights * left_allocation,
                right_weights * right_allocation
            ])
            
            return final_weights
            
        except Exception as e:
            logger.error(f"Error calculating HRP weights: {e}")
            n_assets = len(expected_returns)
            return np.array([1.0 / n_assets] * n_assets)
    
    def _apply_weight_constraints(
        self, 
        weights: np.ndarray, 
        constraints: PortfolioConstraints
    ) -> np.ndarray:
        """Apply weight constraints to portfolio."""
        try:
            # Apply min/max weight constraints
            weights = np.clip(weights, constraints.min_weight, constraints.max_concentration)
            
            # Renormalize to sum to 1
            weights = weights / np.sum(weights)
            
            return weights
            
        except Exception as e:
            logger.error(f"Error applying weight constraints: {e}")
            return weights
    
    def optimize_risk_parity(
        self, 
        symbols: List[str],
        method: str = "erc",
        constraints: Optional[PortfolioConstraints] = None,
        lookback_days: int = 252
    ) -> OptimizationResult:
        """Main risk parity optimization method."""
        try:
            if constraints is None:
                constraints = PortfolioConstraints()
            
            # Calculate returns and covariance
            expected_returns, covariance_matrix = self.mpt_optimizer.calculate_returns_covariance(
                symbols, lookback_days
            )
            
            # Optimize based on method
            if method == "erc":
                optimal_weights = self.optimize_equal_risk_contribution(covariance_matrix, constraints)
            elif method == "hrp":
                optimal_weights = self.optimize_hierarchical_risk_parity(
                    expected_returns, covariance_matrix, constraints
                )
            else:
                raise ValueError(f"Unknown risk parity method: {method}")
            
            # Calculate portfolio metrics
            portfolio_return = np.dot(optimal_weights, expected_returns)
            portfolio_variance = np.dot(optimal_weights.T, np.dot(covariance_matrix, optimal_weights))
            portfolio_volatility = np.sqrt(portfolio_variance)
            sharpe_ratio = (portfolio_return - self.mpt_optimizer.risk_free_rate) / portfolio_volatility
            
            # Calculate risk contributions
            risk_contributions = self.calculate_risk_contributions(optimal_weights, covariance_matrix)
            
            # Additional metrics
            additional_metrics = {
                'max_weight': np.max(optimal_weights),
                'min_weight': np.min(optimal_weights),
                'effective_positions': np.sum(optimal_weights > 0.01),
                'concentration_hhi': np.sum(optimal_weights ** 2),
                'risk_contribution_std': np.std(risk_contributions),
                'max_risk_contribution': np.max(risk_contributions),
                'min_risk_contribution': np.min(risk_contributions)
            }
            
            result = OptimizationResult(
                weights=optimal_weights,
                expected_return=portfolio_return,
                volatility=portfolio_volatility,
                sharpe_ratio=sharpe_ratio,
                symbols=symbols,
                optimization_method=f"risk_parity_{method}",
                constraints_satisfied=True,
                optimization_success=True,
                additional_metrics=additional_metrics
            )
            
            logger.info(f"Risk Parity ({method}) optimization completed")
            logger.info(f"Expected return: {portfolio_return:.4f}")
            logger.info(f"Volatility: {portfolio_volatility:.4f}")
            logger.info(f"Sharpe ratio: {sharpe_ratio:.4f}")
            logger.info(f"Risk contribution std: {additional_metrics['risk_contribution_std']:.4f}")
            
            return result
            
        except Exception as e:
            logger.error(f"Error in risk parity optimization: {e}")
            raise

# Global risk parity optimizer instance
risk_parity_optimizer = RiskParityOptimizer()
