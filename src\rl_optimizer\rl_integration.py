"""
RL Integration - Integrates RL optimizer with main stock analysis system.
Provides real-time parameter adaptation and continuous learning.
"""

import asyncio
import schedule
from datetime import datetime, date, timedelta
from typing import Dict, List, Any, Optional
import json

from .multi_agent_manager import multi_agent_manager
from ..strategies.strategy_registry import strategy_registry
from ..backtesting.backtest_runner import backtest_runner
from ..data.database import db_manager
from ..data.crud import StockCRUD, RLModelCRUD, RLParameterAdaptationCRUD
from ..utils.logger import get_logger
from ..utils.config import config

logger = get_logger(__name__)

class RLIntegration:
    """Integration layer for RL optimization with main system."""
    
    def __init__(self):
        """Initialize RL integration."""
        self.manager = multi_agent_manager
        self.config = config.get_rl_config()
        
        # Integration settings
        self.auto_training_enabled = self.config.get('auto_training_enabled', True)
        self.training_schedule = self.config.get('training_schedule', 'weekly')
        self.adaptation_enabled = self.config.get('adaptation_enabled', True)
        self.validation_threshold = self.config.get('validation_threshold', 0.1)  # 10% improvement
        
        # Setup scheduled tasks
        if self.auto_training_enabled:
            self._setup_training_schedule()
        
        if self.adaptation_enabled:
            self._setup_adaptation_schedule()
    
    def _setup_training_schedule(self):
        """Setup scheduled RL training."""
        try:
            if self.training_schedule == 'daily':
                schedule.every().day.at("02:00").do(self._scheduled_training)
            elif self.training_schedule == 'weekly':
                schedule.every().sunday.at("01:00").do(self._scheduled_training)
            elif self.training_schedule == 'monthly':
                schedule.every().month.do(self._scheduled_training)
            
            logger.info(f"RL training scheduled: {self.training_schedule}")
            
        except Exception as e:
            logger.error(f"Error setting up training schedule: {e}")
    
    def _setup_adaptation_schedule(self):
        """Setup scheduled parameter adaptation."""
        try:
            # Run adaptation every 4 hours during market hours
            schedule.every(4).hours.do(self._scheduled_adaptation)
            logger.info("RL parameter adaptation scheduled every 4 hours")
            
        except Exception as e:
            logger.error(f"Error setting up adaptation schedule: {e}")
    
    async def initialize_rl_system(self):
        """Initialize the RL system with existing data."""
        logger.info("Initializing RL system")
        
        try:
            # Check if models exist in database
            with db_manager.get_session() as db:
                existing_models = RLModelCRUD.get_all_active_models(db)
                
                if not existing_models:
                    logger.info("No existing RL models found. Starting initial training...")
                    await self.run_initial_training()
                else:
                    logger.info(f"Found {len(existing_models)} existing RL models")
                    
                    # Load models into agents
                    for model in existing_models:
                        agent = self.manager.agents.get(model.strategy_name)
                        if agent:
                            success = agent.load_model(model.model_path)
                            if success:
                                logger.info(f"Loaded model for {model.strategy_name}")
                            else:
                                logger.warning(f"Failed to load model for {model.strategy_name}")
            
            logger.info("RL system initialization completed")
            
        except Exception as e:
            logger.error(f"Error initializing RL system: {e}")
    
    async def run_initial_training(self, num_iterations: int = 30):
        """Run initial training for all RL agents.
        
        Args:
            num_iterations: Number of training iterations
        """
        logger.info("Starting initial RL training")
        
        try:
            # Run training with fewer iterations for initial setup
            await self.manager.train_all_agents(
                num_iterations=num_iterations,
                episodes_per_iteration=3
            )
            
            # Validate trained models
            validation_results = await self._validate_trained_models()
            
            logger.info(f"Initial training completed. Validation results: {validation_results}")
            
        except Exception as e:
            logger.error(f"Error in initial training: {e}")
    
    async def _validate_trained_models(self) -> Dict[str, Any]:
        """Validate trained RL models against baseline performance.
        
        Returns:
            Validation results for all models
        """
        logger.info("Validating trained RL models")
        
        validation_results = {
            'total_strategies': len(self.manager.agents),
            'validated_strategies': 0,
            'failed_validations': 0,
            'strategy_results': {}
        }
        
        try:
            # Test symbols for validation
            test_symbols = ['RELIANCE', 'TCS', 'INFY', 'HDFCBANK']
            
            for strategy_name, agent in self.manager.agents.items():
                logger.info(f"Validating {strategy_name}")
                
                strategy_validation = {
                    'symbol_results': {},
                    'avg_improvement': 0,
                    'validation_passed': False
                }
                
                improvements = []
                
                for symbol in test_symbols:
                    try:
                        # Get RL-optimized parameters
                        rl_params = agent.get_optimal_parameters(symbol)
                        
                        # Run backtest with RL parameters
                        rl_result = backtest_runner.engine.run_single_strategy_backtest(
                            strategy_name, symbol, 
                            date.today() - timedelta(days=180),
                            date.today() - timedelta(days=1),
                            rl_params
                        )
                        
                        # Run backtest with default parameters
                        default_result = backtest_runner.engine.run_single_strategy_backtest(
                            strategy_name, symbol,
                            date.today() - timedelta(days=180),
                            date.today() - timedelta(days=1),
                            {}  # Default parameters
                        )
                        
                        if 'error' not in rl_result and 'error' not in default_result:
                            # Calculate improvement
                            rl_sharpe = rl_result.get('sharpe_ratio', 0)
                            default_sharpe = default_result.get('sharpe_ratio', 0)
                            
                            if default_sharpe > 0:
                                improvement = (rl_sharpe - default_sharpe) / default_sharpe * 100
                                improvements.append(improvement)
                                
                                strategy_validation['symbol_results'][symbol] = {
                                    'rl_sharpe': rl_sharpe,
                                    'default_sharpe': default_sharpe,
                                    'improvement_pct': improvement
                                }
                    
                    except Exception as e:
                        logger.error(f"Error validating {strategy_name} on {symbol}: {e}")
                
                # Calculate average improvement
                if improvements:
                    avg_improvement = sum(improvements) / len(improvements)
                    strategy_validation['avg_improvement'] = avg_improvement
                    strategy_validation['validation_passed'] = avg_improvement > self.validation_threshold
                    
                    if strategy_validation['validation_passed']:
                        validation_results['validated_strategies'] += 1
                    else:
                        validation_results['failed_validations'] += 1
                
                validation_results['strategy_results'][strategy_name] = strategy_validation
            
            return validation_results
            
        except Exception as e:
            logger.error(f"Error in model validation: {e}")
            return validation_results
    
    def get_optimized_parameters_for_strategy(
        self, 
        strategy_name: str, 
        symbol: str
    ) -> Dict[str, Any]:
        """Get RL-optimized parameters for a specific strategy and symbol.
        
        Args:
            strategy_name: Strategy name
            symbol: Stock symbol
            
        Returns:
            Optimized parameters dictionary
        """
        try:
            agent = self.manager.agents.get(strategy_name)
            if not agent:
                logger.warning(f"No RL agent found for strategy {strategy_name}")
                return {}
            
            # Get optimized parameters
            optimized_params = agent.get_optimal_parameters(symbol)
            
            # Store adaptation record
            self._store_parameter_adaptation(strategy_name, symbol, optimized_params)
            
            return optimized_params
            
        except Exception as e:
            logger.error(f"Error getting optimized parameters: {e}")
            return {}
    
    def _store_parameter_adaptation(
        self, 
        strategy_name: str, 
        symbol: str, 
        adapted_params: Dict[str, Any]
    ):
        """Store parameter adaptation record in database.
        
        Args:
            strategy_name: Strategy name
            symbol: Stock symbol
            adapted_params: Adapted parameters
        """
        try:
            with db_manager.get_session() as db:
                # Get stock ID
                stock = StockCRUD.get_stock_by_symbol(db, symbol)
                if not stock:
                    return
                
                # Get model ID
                model = RLModelCRUD.get_active_model(db, strategy_name)
                if not model:
                    return
                
                # Get current market conditions
                market_conditions = self.manager._get_current_market_conditions(symbol)
                
                # Create adaptation record
                adaptation_data = {
                    'model_id': model.id,
                    'stock_id': stock.id,
                    'adaptation_date': date.today(),
                    'strategy_name': strategy_name,
                    'market_conditions': json.dumps(market_conditions),
                    'original_parameters': json.dumps({}),  # Would store original params
                    'adapted_parameters': json.dumps(adapted_params),
                    'adaptation_reason': 'rl_optimization'
                }
                
                RLParameterAdaptationCRUD.create_adaptation_record(db, adaptation_data)
                
        except Exception as e:
            logger.error(f"Error storing parameter adaptation: {e}")
    
    async def run_continuous_optimization(self):
        """Run continuous optimization loop."""
        logger.info("Starting continuous RL optimization")
        
        try:
            # Start continuous adaptation
            await self.manager.continuous_adaptation()
            
        except Exception as e:
            logger.error(f"Error in continuous optimization: {e}")
    
    def _scheduled_training(self):
        """Scheduled training task."""
        logger.info("Running scheduled RL training")
        
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # Run training
            loop.run_until_complete(
                self.manager.train_all_agents(num_iterations=20, episodes_per_iteration=2)
            )
            
            loop.close()
            logger.info("Scheduled RL training completed")
            
        except Exception as e:
            logger.error(f"Error in scheduled training: {e}")
    
    def _scheduled_adaptation(self):
        """Scheduled parameter adaptation task."""
        logger.info("Running scheduled parameter adaptation")
        
        try:
            # Get top 10 Nifty 50 stocks for adaptation
            with db_manager.get_session() as db:
                nifty50_stocks = StockCRUD.get_nifty50_stocks(db)
                symbols = [stock.symbol for stock in nifty50_stocks[:10]]
            
            # Adapt parameters for each symbol
            for symbol in symbols:
                market_conditions = self.manager._get_current_market_conditions(symbol)
                adapted_params = self.manager.adapt_parameters_realtime(symbol, market_conditions)
                
                # Store adaptations
                for strategy_name, params in adapted_params.items():
                    if params:  # Only store if parameters were adapted
                        self._store_parameter_adaptation(strategy_name, symbol, params)
            
            logger.info("Scheduled parameter adaptation completed")
            
        except Exception as e:
            logger.error(f"Error in scheduled adaptation: {e}")
    
    def get_rl_system_status(self) -> Dict[str, Any]:
        """Get comprehensive RL system status.
        
        Returns:
            RL system status information
        """
        try:
            # Get agent status
            agent_status = self.manager.get_agent_status()
            
            # Get database statistics
            with db_manager.get_session() as db:
                active_models = RLModelCRUD.get_all_active_models(db)
                
                # Get recent training performance
                recent_training = []
                for model in active_models:
                    from ..data.crud import RLTrainingHistoryCRUD
                    history = RLTrainingHistoryCRUD.get_training_history(
                        db, model.strategy_name, limit=5
                    )
                    if history:
                        recent_training.extend(history)
                
                # Get recent adaptations
                recent_adaptations = []
                for model in active_models:
                    adaptations = RLParameterAdaptationCRUD.get_recent_adaptations(
                        db, model.strategy_name, days=7
                    )
                    recent_adaptations.extend(adaptations)
            
            return {
                'system_status': 'operational',
                'auto_training_enabled': self.auto_training_enabled,
                'adaptation_enabled': self.adaptation_enabled,
                'training_schedule': self.training_schedule,
                'agent_status': agent_status,
                'active_models': len(active_models),
                'recent_training_sessions': len(recent_training),
                'recent_adaptations': len(recent_adaptations),
                'last_updated': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting RL system status: {e}")
            return {
                'system_status': 'error',
                'error': str(e),
                'last_updated': datetime.now().isoformat()
            }
    
    async def retrain_strategy(self, strategy_name: str, num_iterations: int = 50) -> Dict[str, Any]:
        """Retrain a specific strategy.
        
        Args:
            strategy_name: Strategy to retrain
            num_iterations: Number of training iterations
            
        Returns:
            Retraining results
        """
        logger.info(f"Retraining strategy: {strategy_name}")
        
        try:
            agent = self.manager.agents.get(strategy_name)
            if not agent:
                return {'error': f'Strategy {strategy_name} not found'}
            
            # Run training
            await self.manager._train_agent_async(
                agent, strategy_name, num_iterations, 3
            )
            
            # Validate retrained model
            validation_result = await self._validate_single_strategy(strategy_name)
            
            return {
                'strategy_name': strategy_name,
                'training_completed': True,
                'validation_result': validation_result,
                'retrain_date': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error retraining strategy {strategy_name}: {e}")
            return {'error': str(e)}
    
    async def _validate_single_strategy(self, strategy_name: str) -> Dict[str, Any]:
        """Validate a single strategy after retraining.
        
        Args:
            strategy_name: Strategy name
            
        Returns:
            Validation results
        """
        try:
            agent = self.manager.agents.get(strategy_name)
            if not agent:
                return {'error': 'Agent not found'}
            
            # Test on RELIANCE
            symbol = 'RELIANCE'
            
            # Get optimized parameters
            rl_params = agent.get_optimal_parameters(symbol)
            
            # Run backtest
            result = backtest_runner.engine.run_single_strategy_backtest(
                strategy_name, symbol,
                date.today() - timedelta(days=90),
                date.today() - timedelta(days=1),
                rl_params
            )
            
            if 'error' not in result:
                return {
                    'validation_passed': result.get('sharpe_ratio', 0) > 0.5,
                    'sharpe_ratio': result.get('sharpe_ratio', 0),
                    'total_return': result.get('total_return', 0),
                    'optimized_parameters': rl_params
                }
            else:
                return {'error': result['error']}
                
        except Exception as e:
            logger.error(f"Error validating strategy {strategy_name}: {e}")
            return {'error': str(e)}

# Global RL integration instance
rl_integration = RLIntegration()

async def initialize_rl_system():
    """Initialize RL system - main entry point."""
    return await rl_integration.initialize_rl_system()

def get_optimized_parameters(strategy_name: str, symbol: str) -> Dict[str, Any]:
    """Get RL-optimized parameters for strategy and symbol."""
    return rl_integration.get_optimized_parameters_for_strategy(strategy_name, symbol)

async def run_rl_training():
    """Run RL training for all strategies."""
    return await rl_integration.manager.train_all_agents()

if __name__ == "__main__":
    # Initialize and run RL system
    asyncio.run(initialize_rl_system())
