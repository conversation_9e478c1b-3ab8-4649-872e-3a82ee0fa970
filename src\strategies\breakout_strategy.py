"""
Volume-Confirmed Breakout Strategy Implementation.
Features resistance identification and false breakout filtering using price action.
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

from .base_strategy import BaseStrategy, TradingSignal, SignalType, SignalStrength
from .strategy_utils import TechnicalAnalysis, SignalFilters
from ..utils.logger import get_logger
from ..utils.config import config

logger = get_logger(__name__)

class VolumeConfirmedBreakoutStrategy(BaseStrategy):
    """
    Volume-Confirmed Breakout Strategy with false signal filtering.
    
    Features:
    - Resistance level identification using 20-day rolling highs
    - Support level identification using 20-day rolling lows
    - Volume confirmation (2x average volume)
    - False breakout filter using price action confirmation
    - Breakout strength analysis
    - Multiple timeframe confirmation
    - Volatility-adjusted position sizing
    """
    
    def __init__(self, parameters: Optional[Dict[str, Any]] = None):
        """Initialize Volume-Confirmed Breakout Strategy.
        
        Args:
            parameters: Strategy parameters override
        """
        default_parameters = {
            'lookback_period': 20,
            'volume_multiplier': 2.0,
            'min_breakout_volume': 1.5,
            'false_breakout_filter': True,
            'confirmation_bars': 3,
            'min_breakout_percentage': 0.5,  # 0.5% minimum breakout
            'max_breakout_percentage': 10.0,  # 10% maximum breakout (avoid gaps)
            'support_resistance_strength': 3,  # Minimum touches for strong level
            'volatility_adjustment': True,
            'trend_alignment': True,
            'min_signal_strength': 0.6
        }
        
        if parameters:
            default_parameters.update(parameters)
        
        super().__init__("volume_confirmed_breakout", default_parameters)
        
        # Strategy-specific attributes
        self.lookback_period = self.parameters['lookback_period']
        self.volume_multiplier = self.parameters['volume_multiplier']
        self.min_breakout_volume = self.parameters['min_breakout_volume']
        self.false_breakout_filter = self.parameters['false_breakout_filter']
        self.confirmation_bars = self.parameters['confirmation_bars']
        self.min_breakout_percentage = self.parameters['min_breakout_percentage'] / 100
        self.max_breakout_percentage = self.parameters['max_breakout_percentage'] / 100
        self.support_resistance_strength = self.parameters['support_resistance_strength']
        self.volatility_adjustment = self.parameters['volatility_adjustment']
        self.trend_alignment = self.parameters['trend_alignment']
    
    def get_required_indicators(self) -> List[str]:
        """Get required technical indicators."""
        return [
            'volume_ratio', 'sma_20', 'sma_50', 'bb_upper', 'bb_lower', 'atr'
        ]
    
    def identify_resistance_levels(
        self, 
        data: pd.DataFrame, 
        index: int
    ) -> List[Dict[str, Any]]:
        """Identify resistance levels using rolling highs.
        
        Args:
            data: DataFrame with price data
            index: Current index
            
        Returns:
            List of resistance level dictionaries
        """
        if index < self.lookback_period * 2:
            return []
        
        resistance_levels = []
        
        # Look back for resistance levels
        lookback_data = data.iloc[max(0, index - self.lookback_period * 3):index]
        
        # Find local maxima (resistance levels)
        for i in range(self.lookback_period, len(lookback_data) - self.lookback_period):
            current_high = lookback_data['high_price'].iloc[i]
            
            # Check if this is a local maximum
            window_data = lookback_data['high_price'].iloc[i-self.lookback_period:i+self.lookback_period+1]
            
            if current_high == window_data.max():
                # Count how many times price touched this level
                tolerance = current_high * 0.01  # 1% tolerance
                touches = 0
                
                for j in range(len(lookback_data)):
                    if abs(lookback_data['high_price'].iloc[j] - current_high) <= tolerance:
                        touches += 1
                
                # Only consider strong resistance levels
                if touches >= self.support_resistance_strength:
                    resistance_levels.append({
                        'level': current_high,
                        'touches': touches,
                        'strength': min(touches / 10, 1.0),  # Normalize strength
                        'last_touch_index': i,
                        'type': 'resistance'
                    })
        
        # Sort by strength and return top levels
        resistance_levels.sort(key=lambda x: x['strength'], reverse=True)
        return resistance_levels[:5]  # Top 5 resistance levels
    
    def identify_support_levels(
        self, 
        data: pd.DataFrame, 
        index: int
    ) -> List[Dict[str, Any]]:
        """Identify support levels using rolling lows.
        
        Args:
            data: DataFrame with price data
            index: Current index
            
        Returns:
            List of support level dictionaries
        """
        if index < self.lookback_period * 2:
            return []
        
        support_levels = []
        
        # Look back for support levels
        lookback_data = data.iloc[max(0, index - self.lookback_period * 3):index]
        
        # Find local minima (support levels)
        for i in range(self.lookback_period, len(lookback_data) - self.lookback_period):
            current_low = lookback_data['low_price'].iloc[i]
            
            # Check if this is a local minimum
            window_data = lookback_data['low_price'].iloc[i-self.lookback_period:i+self.lookback_period+1]
            
            if current_low == window_data.min():
                # Count how many times price touched this level
                tolerance = current_low * 0.01  # 1% tolerance
                touches = 0
                
                for j in range(len(lookback_data)):
                    if abs(lookback_data['low_price'].iloc[j] - current_low) <= tolerance:
                        touches += 1
                
                # Only consider strong support levels
                if touches >= self.support_resistance_strength:
                    support_levels.append({
                        'level': current_low,
                        'touches': touches,
                        'strength': min(touches / 10, 1.0),  # Normalize strength
                        'last_touch_index': i,
                        'type': 'support'
                    })
        
        # Sort by strength and return top levels
        support_levels.sort(key=lambda x: x['strength'], reverse=True)
        return support_levels[:5]  # Top 5 support levels
    
    def detect_breakout(
        self, 
        data: pd.DataFrame, 
        index: int, 
        level: float, 
        breakout_type: str
    ) -> Dict[str, Any]:
        """Detect breakout from support/resistance level.
        
        Args:
            data: DataFrame with price data
            index: Current index
            level: Support/resistance level
            breakout_type: 'bullish' (resistance break) or 'bearish' (support break)
            
        Returns:
            Breakout analysis results
        """
        current_price = data['close_price'].iloc[index]
        current_high = data['high_price'].iloc[index]
        current_low = data['low_price'].iloc[index]
        current_volume = data['volume'].iloc[index]
        
        # Calculate breakout percentage
        if breakout_type == 'bullish':
            breakout_percentage = (current_price - level) / level
            price_breakout = current_price > level and current_high > level
        else:  # bearish
            breakout_percentage = (level - current_price) / level
            price_breakout = current_price < level and current_low < level
        
        # Check breakout percentage bounds
        if breakout_percentage < self.min_breakout_percentage or breakout_percentage > self.max_breakout_percentage:
            return {'is_breakout': False, 'reason': 'breakout_percentage_out_of_bounds'}
        
        if not price_breakout:
            return {'is_breakout': False, 'reason': 'no_price_breakout'}
        
        # Volume confirmation
        avg_volume = data['volume'].iloc[max(0, index-20):index].mean()
        volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1.0
        volume_confirmed = volume_ratio >= self.volume_multiplier
        
        # Minimum volume check
        min_volume_confirmed = volume_ratio >= self.min_breakout_volume
        
        if not min_volume_confirmed:
            return {'is_breakout': False, 'reason': 'insufficient_volume'}
        
        return {
            'is_breakout': True,
            'breakout_percentage': breakout_percentage,
            'volume_ratio': volume_ratio,
            'volume_confirmed': volume_confirmed,
            'breakout_strength': min(1.0, breakout_percentage * 10 + (volume_ratio / 5))
        }
    
    def filter_false_breakout(
        self, 
        data: pd.DataFrame, 
        index: int, 
        level: float, 
        breakout_type: str
    ) -> bool:
        """Filter false breakouts using price action confirmation.
        
        Args:
            data: DataFrame with price data
            index: Current index
            level: Breakout level
            breakout_type: 'bullish' or 'bearish'
            
        Returns:
            True if breakout is likely genuine
        """
        if not self.false_breakout_filter or index < self.confirmation_bars:
            return True
        
        # Check confirmation bars after breakout
        confirmation_count = 0
        
        for i in range(1, min(self.confirmation_bars + 1, len(data) - index)):
            future_index = index + i
            if future_index >= len(data):
                break
            
            future_close = data['close_price'].iloc[future_index]
            
            if breakout_type == 'bullish' and future_close > level:
                confirmation_count += 1
            elif breakout_type == 'bearish' and future_close < level:
                confirmation_count += 1
        
        # Require at least 2/3 of confirmation bars to confirm
        required_confirmations = max(1, int(self.confirmation_bars * 0.67))
        return confirmation_count >= required_confirmations
    
    def calculate_breakout_strength(
        self, 
        breakout_info: Dict[str, Any],
        level_strength: float,
        trend_aligned: bool = False,
        volatility_factor: float = 1.0
    ) -> float:
        """Calculate overall breakout signal strength.
        
        Args:
            breakout_info: Breakout detection results
            level_strength: Strength of the support/resistance level
            trend_aligned: Whether breakout aligns with trend
            volatility_factor: Volatility adjustment factor
            
        Returns:
            Signal strength (0.0 to 1.0)
        """
        base_strength = 0.5
        
        # Breakout strength component
        base_strength += breakout_info.get('breakout_strength', 0) * 0.3
        
        # Level strength component
        base_strength += level_strength * 0.2
        
        # Volume confirmation bonus
        if breakout_info.get('volume_confirmed', False):
            base_strength += 0.15
        
        # Trend alignment bonus
        if trend_aligned:
            base_strength += 0.1
        
        # Volatility adjustment
        if self.volatility_adjustment:
            base_strength *= volatility_factor
        
        return min(1.0, base_strength)
    
    def calculate_volatility_factor(self, data: pd.DataFrame, index: int) -> float:
        """Calculate volatility adjustment factor.
        
        Args:
            data: DataFrame with price data
            index: Current index
            
        Returns:
            Volatility factor (0.5 to 1.5)
        """
        if index < 20:
            return 1.0
        
        # Calculate recent volatility
        recent_prices = data['close_price'].iloc[index-20:index+1]
        returns = recent_prices.pct_change().dropna()
        
        if len(returns) < 10:
            return 1.0
        
        volatility = returns.std()
        
        # Normalize volatility (typical daily volatility is 1-3%)
        normalized_volatility = volatility / 0.02  # 2% reference
        
        # Higher volatility reduces signal strength, lower volatility increases it
        volatility_factor = 1.0 / (1.0 + normalized_volatility * 0.5)
        
        return max(0.5, min(1.5, volatility_factor))
    
    def calculate_signals(self, data: pd.DataFrame) -> List[TradingSignal]:
        """Calculate breakout trading signals.
        
        Args:
            data: DataFrame with OHLCV and technical indicator data
            
        Returns:
            List of trading signals
        """
        signals = []
        
        if len(data) < self.lookback_period * 3:
            logger.warning("Insufficient data for breakout strategy")
            return signals
        
        # Detect overall trend
        trend_direction = TechnicalAnalysis.detect_trend(data['close_price'])
        
        # Iterate through data to find breakout signals
        for i in range(self.lookback_period * 2, len(data) - self.confirmation_bars):
            current_date = data['date'].iloc[i]
            current_price = data['close_price'].iloc[i]
            
            if pd.isna(current_price):
                continue
            
            # Identify resistance levels for bullish breakouts
            resistance_levels = self.identify_resistance_levels(data, i)
            
            for resistance in resistance_levels:
                level = resistance['level']
                level_strength = resistance['strength']
                
                # Check if current price is near resistance level
                if abs(current_price - level) / level > 0.02:  # More than 2% away
                    continue
                
                # Detect breakout
                breakout_info = self.detect_breakout(data, i, level, 'bullish')
                
                if not breakout_info['is_breakout']:
                    continue
                
                # Filter false breakouts (if we have future data)
                if i < len(data) - self.confirmation_bars:
                    if not self.filter_false_breakout(data, i, level, 'bullish'):
                        continue
                
                # Trend alignment check
                trend_aligned = SignalFilters.trend_alignment(
                    'BUY', trend_direction, self.trend_alignment
                )
                
                # Calculate volatility factor
                volatility_factor = self.calculate_volatility_factor(data, i)
                
                # Calculate signal strength
                signal_strength = self.calculate_breakout_strength(
                    breakout_info, level_strength, trend_aligned, volatility_factor
                )
                
                if signal_strength >= self.parameters['min_signal_strength']:
                    # Calculate stop loss and target
                    atr = data.get('atr', pd.Series([current_price * 0.02] * len(data))).iloc[i]
                    if pd.isna(atr):
                        atr = current_price * 0.02
                    
                    stop_loss = level - (0.5 * atr)  # Just below breakout level
                    target_price = current_price + (4 * atr)  # 8:1 risk-reward
                    
                    signal = TradingSignal(
                        symbol="",  # Will be set by base class
                        signal_type=SignalType.BUY,
                        signal_strength=signal_strength,
                        price=current_price,
                        target_price=target_price,
                        stop_loss=stop_loss,
                        confidence_score=level_strength,
                        parameters={
                            'breakout_type': 'resistance_breakout',
                            'resistance_level': level,
                            'level_strength': level_strength,
                            'level_touches': resistance['touches'],
                            'breakout_percentage': breakout_info['breakout_percentage'],
                            'volume_ratio': breakout_info['volume_ratio'],
                            'volume_confirmed': breakout_info['volume_confirmed'],
                            'trend_aligned': trend_aligned,
                            'volatility_factor': volatility_factor
                        },
                        timestamp=datetime.combine(current_date, datetime.min.time())
                    )
                    
                    signals.append(signal)
            
            # Identify support levels for bearish breakouts
            support_levels = self.identify_support_levels(data, i)
            
            for support in support_levels:
                level = support['level']
                level_strength = support['strength']
                
                # Check if current price is near support level
                if abs(current_price - level) / level > 0.02:  # More than 2% away
                    continue
                
                # Detect breakout
                breakout_info = self.detect_breakout(data, i, level, 'bearish')
                
                if not breakout_info['is_breakout']:
                    continue
                
                # Filter false breakouts (if we have future data)
                if i < len(data) - self.confirmation_bars:
                    if not self.filter_false_breakout(data, i, level, 'bearish'):
                        continue
                
                # Trend alignment check
                trend_aligned = SignalFilters.trend_alignment(
                    'SELL', trend_direction, self.trend_alignment
                )
                
                # Calculate volatility factor
                volatility_factor = self.calculate_volatility_factor(data, i)
                
                # Calculate signal strength
                signal_strength = self.calculate_breakout_strength(
                    breakout_info, level_strength, trend_aligned, volatility_factor
                )
                
                if signal_strength >= self.parameters['min_signal_strength']:
                    # Calculate stop loss and target
                    atr = data.get('atr', pd.Series([current_price * 0.02] * len(data))).iloc[i]
                    if pd.isna(atr):
                        atr = current_price * 0.02
                    
                    stop_loss = level + (0.5 * atr)  # Just above breakout level
                    target_price = current_price - (4 * atr)  # 8:1 risk-reward
                    
                    signal = TradingSignal(
                        symbol="",  # Will be set by base class
                        signal_type=SignalType.SELL,
                        signal_strength=signal_strength,
                        price=current_price,
                        target_price=target_price,
                        stop_loss=stop_loss,
                        confidence_score=level_strength,
                        parameters={
                            'breakout_type': 'support_breakdown',
                            'support_level': level,
                            'level_strength': level_strength,
                            'level_touches': support['touches'],
                            'breakout_percentage': breakout_info['breakout_percentage'],
                            'volume_ratio': breakout_info['volume_ratio'],
                            'volume_confirmed': breakout_info['volume_confirmed'],
                            'trend_aligned': trend_aligned,
                            'volatility_factor': volatility_factor
                        },
                        timestamp=datetime.combine(current_date, datetime.min.time())
                    )
                    
                    signals.append(signal)
        
        logger.info(f"Generated {len(signals)} breakout signals")
        return signals
