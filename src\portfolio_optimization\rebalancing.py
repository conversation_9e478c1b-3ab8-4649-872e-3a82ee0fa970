"""
Portfolio Rebalancing System.
Implements various rebalancing strategies and transaction cost optimization.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime, date, timedelta
from dataclasses import dataclass
from enum import Enum

from .mpt_optimizer import mpt_optimizer, PortfolioConstraints
from .performance_analytics import performance_analytics
from ..data.database import db_manager
from ..data.crud import PriceCRUD, StockCRUD
from ..utils.logger import get_logger

logger = get_logger(__name__)

class RebalancingTrigger(Enum):
    """Rebalancing trigger types."""
    TIME_BASED = "time_based"
    THRESHOLD_BASED = "threshold_based"
    VOLATILITY_BASED = "volatility_based"
    PERFORMANCE_BASED = "performance_based"

@dataclass
class RebalancingConfig:
    """Rebalancing configuration."""
    trigger_type: RebalancingTrigger
    frequency_days: Optional[int] = None  # For time-based
    threshold_percent: Optional[float] = None  # For threshold-based
    volatility_threshold: Optional[float] = None  # For volatility-based
    performance_threshold: Optional[float] = None  # For performance-based
    min_trade_size: float = 0.01  # Minimum 1% trade
    transaction_cost: float = 0.001  # 0.1% transaction cost
    max_turnover: float = 0.5  # Maximum 50% turnover per rebalancing

@dataclass
class RebalancingAction:
    """Rebalancing action details."""
    symbol: str
    current_weight: float
    target_weight: float
    weight_change: float
    trade_amount: float
    trade_direction: str  # 'BUY' or 'SELL'
    transaction_cost: float

@dataclass
class RebalancingResult:
    """Rebalancing result."""
    rebalancing_date: date
    trigger_reason: str
    actions: List[RebalancingAction]
    total_turnover: float
    total_transaction_cost: float
    expected_improvement: float
    current_portfolio: Dict[str, float]
    target_portfolio: Dict[str, float]

class PortfolioRebalancer:
    """Portfolio rebalancing system."""
    
    def __init__(self):
        """Initialize portfolio rebalancer."""
        self.default_config = RebalancingConfig(
            trigger_type=RebalancingTrigger.THRESHOLD_BASED,
            threshold_percent=0.05  # 5% threshold
        )
        
        logger.info("Portfolio Rebalancer initialized")
    
    def check_rebalancing_trigger(
        self,
        current_weights: Dict[str, float],
        target_weights: Dict[str, float],
        config: RebalancingConfig,
        portfolio_returns: Optional[np.ndarray] = None,
        last_rebalance_date: Optional[date] = None
    ) -> Tuple[bool, str]:
        """Check if rebalancing should be triggered."""
        try:
            current_date = date.today()
            
            if config.trigger_type == RebalancingTrigger.TIME_BASED:
                if last_rebalance_date and config.frequency_days:
                    days_since_rebalance = (current_date - last_rebalance_date).days
                    if days_since_rebalance >= config.frequency_days:
                        return True, f"Time-based trigger: {days_since_rebalance} days since last rebalance"
                
            elif config.trigger_type == RebalancingTrigger.THRESHOLD_BASED:
                if config.threshold_percent:
                    max_deviation = 0.0
                    for symbol in target_weights:
                        current_weight = current_weights.get(symbol, 0.0)
                        target_weight = target_weights[symbol]
                        deviation = abs(current_weight - target_weight)
                        max_deviation = max(max_deviation, deviation)
                    
                    if max_deviation > config.threshold_percent:
                        return True, f"Threshold trigger: max deviation {max_deviation:.3f} > {config.threshold_percent:.3f}"
                
            elif config.trigger_type == RebalancingTrigger.VOLATILITY_BASED:
                if portfolio_returns is not None and config.volatility_threshold:
                    recent_volatility = np.std(portfolio_returns[-20:]) * np.sqrt(252)  # 20-day volatility
                    if recent_volatility > config.volatility_threshold:
                        return True, f"Volatility trigger: recent volatility {recent_volatility:.3f} > {config.volatility_threshold:.3f}"
                
            elif config.trigger_type == RebalancingTrigger.PERFORMANCE_BASED:
                if portfolio_returns is not None and config.performance_threshold:
                    recent_return = np.prod(1 + portfolio_returns[-20:]) - 1  # 20-day return
                    if recent_return < -config.performance_threshold:
                        return True, f"Performance trigger: recent return {recent_return:.3f} < -{config.performance_threshold:.3f}"
            
            return False, "No rebalancing trigger activated"
            
        except Exception as e:
            logger.error(f"Error checking rebalancing trigger: {e}")
            return False, f"Error: {str(e)}"
    
    def calculate_optimal_trades(
        self,
        current_weights: Dict[str, float],
        target_weights: Dict[str, float],
        portfolio_value: float,
        config: RebalancingConfig
    ) -> List[RebalancingAction]:
        """Calculate optimal trades for rebalancing."""
        try:
            actions = []
            total_turnover = 0.0
            
            # Calculate required trades
            for symbol in target_weights:
                current_weight = current_weights.get(symbol, 0.0)
                target_weight = target_weights[symbol]
                weight_change = target_weight - current_weight
                
                # Skip small trades
                if abs(weight_change) < config.min_trade_size:
                    continue
                
                trade_amount = weight_change * portfolio_value
                trade_direction = "BUY" if weight_change > 0 else "SELL"
                transaction_cost = abs(trade_amount) * config.transaction_cost
                
                action = RebalancingAction(
                    symbol=symbol,
                    current_weight=current_weight,
                    target_weight=target_weight,
                    weight_change=weight_change,
                    trade_amount=trade_amount,
                    trade_direction=trade_direction,
                    transaction_cost=transaction_cost
                )
                
                actions.append(action)
                total_turnover += abs(weight_change)
            
            # Check turnover constraint
            if total_turnover > config.max_turnover:
                # Scale down trades proportionally
                scale_factor = config.max_turnover / total_turnover
                
                for action in actions:
                    action.weight_change *= scale_factor
                    action.trade_amount *= scale_factor
                    action.transaction_cost *= scale_factor
                    action.target_weight = action.current_weight + action.weight_change
            
            return actions
            
        except Exception as e:
            logger.error(f"Error calculating optimal trades: {e}")
            return []
    
    def execute_rebalancing(
        self,
        current_weights: Dict[str, float],
        target_weights: Dict[str, float],
        portfolio_value: float,
        config: Optional[RebalancingConfig] = None,
        portfolio_returns: Optional[np.ndarray] = None,
        last_rebalance_date: Optional[date] = None
    ) -> Optional[RebalancingResult]:
        """Execute portfolio rebalancing if triggered."""
        try:
            if config is None:
                config = self.default_config
            
            # Check if rebalancing is needed
            should_rebalance, trigger_reason = self.check_rebalancing_trigger(
                current_weights, target_weights, config, portfolio_returns, last_rebalance_date
            )
            
            if not should_rebalance:
                logger.info(f"No rebalancing needed: {trigger_reason}")
                return None
            
            # Calculate optimal trades
            actions = self.calculate_optimal_trades(
                current_weights, target_weights, portfolio_value, config
            )
            
            if not actions:
                logger.info("No significant trades required for rebalancing")
                return None
            
            # Calculate metrics
            total_turnover = sum(abs(action.weight_change) for action in actions)
            total_transaction_cost = sum(action.transaction_cost for action in actions)
            
            # Estimate expected improvement (simplified)
            expected_improvement = self._estimate_rebalancing_benefit(
                current_weights, target_weights, actions
            )
            
            # Create new portfolio weights after rebalancing
            new_portfolio = current_weights.copy()
            for action in actions:
                new_portfolio[action.symbol] = action.target_weight
            
            result = RebalancingResult(
                rebalancing_date=date.today(),
                trigger_reason=trigger_reason,
                actions=actions,
                total_turnover=total_turnover,
                total_transaction_cost=total_transaction_cost,
                expected_improvement=expected_improvement,
                current_portfolio=current_weights,
                target_portfolio=new_portfolio
            )
            
            logger.info(f"Rebalancing executed: {trigger_reason}")
            logger.info(f"Total turnover: {total_turnover:.3f}")
            logger.info(f"Transaction cost: ₹{total_transaction_cost:,.0f}")
            logger.info(f"Number of trades: {len(actions)}")
            
            return result
            
        except Exception as e:
            logger.error(f"Error executing rebalancing: {e}")
            return None
    
    def _estimate_rebalancing_benefit(
        self,
        current_weights: Dict[str, float],
        target_weights: Dict[str, float],
        actions: List[RebalancingAction]
    ) -> float:
        """Estimate the benefit of rebalancing (simplified)."""
        try:
            # Calculate weight deviations
            total_deviation = 0.0
            for symbol in target_weights:
                current_weight = current_weights.get(symbol, 0.0)
                target_weight = target_weights[symbol]
                total_deviation += abs(current_weight - target_weight)
            
            # Estimate benefit as reduction in tracking error
            # This is a simplified estimate - would need more sophisticated modeling
            estimated_benefit = total_deviation * 0.1  # 10% of deviation as benefit
            
            return estimated_benefit
            
        except Exception as e:
            logger.error(f"Error estimating rebalancing benefit: {e}")
            return 0.0
    
    def create_rebalancing_schedule(
        self,
        symbols: List[str],
        optimization_method: str = "max_sharpe",
        rebalancing_frequency: int = 30,  # days
        lookback_period: int = 252,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> List[RebalancingResult]:
        """Create a rebalancing schedule with historical simulation."""
        try:
            if start_date is None:
                start_date = date.today() - timedelta(days=365)
            if end_date is None:
                end_date = date.today()
            
            rebalancing_results = []
            current_date = start_date
            last_rebalance_date = None
            current_weights = {symbol: 1.0/len(symbols) for symbol in symbols}  # Start with equal weights
            
            config = RebalancingConfig(
                trigger_type=RebalancingTrigger.TIME_BASED,
                frequency_days=rebalancing_frequency
            )
            
            while current_date <= end_date:
                try:
                    # Calculate target weights using optimization
                    constraints = PortfolioConstraints()
                    optimization_result = mpt_optimizer.optimize_portfolio(
                        symbols, optimization_method, constraints, lookback_days=lookback_period
                    )
                    
                    if optimization_result.optimization_success:
                        target_weights = {
                            symbol: weight for symbol, weight in 
                            zip(optimization_result.symbols, optimization_result.weights)
                        }
                        
                        # Simulate portfolio value (simplified)
                        portfolio_value = 1000000  # ₹10L portfolio
                        
                        # Execute rebalancing
                        rebalancing_result = self.execute_rebalancing(
                            current_weights,
                            target_weights,
                            portfolio_value,
                            config,
                            last_rebalance_date=last_rebalance_date
                        )
                        
                        if rebalancing_result:
                            rebalancing_results.append(rebalancing_result)
                            current_weights = rebalancing_result.target_portfolio
                            last_rebalance_date = current_date
                
                except Exception as e:
                    logger.error(f"Error in rebalancing simulation for {current_date}: {e}")
                
                current_date += timedelta(days=rebalancing_frequency)
            
            logger.info(f"Created rebalancing schedule with {len(rebalancing_results)} rebalancing events")
            
            return rebalancing_results
            
        except Exception as e:
            logger.error(f"Error creating rebalancing schedule: {e}")
            return []
    
    def analyze_rebalancing_performance(
        self,
        rebalancing_results: List[RebalancingResult]
    ) -> Dict[str, Any]:
        """Analyze the performance of rebalancing strategy."""
        try:
            if not rebalancing_results:
                return {}
            
            # Calculate metrics
            total_rebalances = len(rebalancing_results)
            total_transaction_costs = sum(result.total_transaction_cost for result in rebalancing_results)
            average_turnover = np.mean([result.total_turnover for result in rebalancing_results])
            total_expected_benefit = sum(result.expected_improvement for result in rebalancing_results)
            
            # Analyze frequency
            dates = [result.rebalancing_date for result in rebalancing_results]
            if len(dates) > 1:
                intervals = [(dates[i] - dates[i-1]).days for i in range(1, len(dates))]
                average_interval = np.mean(intervals)
            else:
                average_interval = 0
            
            # Analyze triggers
            trigger_counts = {}
            for result in rebalancing_results:
                trigger = result.trigger_reason.split(':')[0]
                trigger_counts[trigger] = trigger_counts.get(trigger, 0) + 1
            
            # Calculate cost-benefit ratio
            cost_benefit_ratio = total_transaction_costs / total_expected_benefit if total_expected_benefit > 0 else np.inf
            
            analysis = {
                'summary': {
                    'total_rebalances': total_rebalances,
                    'total_transaction_costs': total_transaction_costs,
                    'average_turnover': average_turnover,
                    'total_expected_benefit': total_expected_benefit,
                    'cost_benefit_ratio': cost_benefit_ratio,
                    'average_interval_days': average_interval
                },
                'trigger_analysis': trigger_counts,
                'performance_metrics': {
                    'rebalancing_frequency': total_rebalances / len(dates) * 365 if dates else 0,
                    'average_transaction_cost_per_rebalance': total_transaction_costs / total_rebalances if total_rebalances > 0 else 0,
                    'efficiency_score': total_expected_benefit / total_transaction_costs if total_transaction_costs > 0 else 0
                },
                'recommendations': self._generate_rebalancing_recommendations(rebalancing_results)
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing rebalancing performance: {e}")
            return {}
    
    def _generate_rebalancing_recommendations(
        self,
        rebalancing_results: List[RebalancingResult]
    ) -> List[str]:
        """Generate recommendations based on rebalancing analysis."""
        try:
            recommendations = []
            
            if not rebalancing_results:
                return ["No rebalancing data available for analysis"]
            
            # Analyze transaction costs
            avg_cost = np.mean([result.total_transaction_cost for result in rebalancing_results])
            if avg_cost > 5000:  # ₹5K threshold
                recommendations.append("Consider reducing rebalancing frequency to lower transaction costs")
            
            # Analyze turnover
            avg_turnover = np.mean([result.total_turnover for result in rebalancing_results])
            if avg_turnover > 0.3:  # 30% threshold
                recommendations.append("High portfolio turnover detected - consider wider rebalancing thresholds")
            
            # Analyze frequency
            if len(rebalancing_results) > 12:  # More than monthly
                recommendations.append("Frequent rebalancing detected - evaluate if benefits justify costs")
            
            # Cost-benefit analysis
            total_costs = sum(result.total_transaction_cost for result in rebalancing_results)
            total_benefits = sum(result.expected_improvement for result in rebalancing_results)
            
            if total_costs > total_benefits:
                recommendations.append("Transaction costs exceed expected benefits - optimize rebalancing strategy")
            
            if not recommendations:
                recommendations.append("Rebalancing strategy appears well-optimized")
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")
            return ["Error generating recommendations"]

# Global portfolio rebalancer instance
portfolio_rebalancer = PortfolioRebalancer()
