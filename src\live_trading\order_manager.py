"""
Order Management System - Handles order lifecycle, execution, and tracking.
Provides intelligent order routing, partial fill handling, and order state management.
"""

import asyncio
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime, timedelta
from enum import Enum
import uuid
from dataclasses import dataclass, field

from .broker_integration import (
    BrokerInterface, get_broker, Order, OrderType, OrderSide, 
    OrderStatus, ProductType
)
from ..data.database import db_manager
from ..data.crud import StockCRUD
from ..utils.logger import get_logger
from ..utils.config import config

logger = get_logger(__name__)

class OrderPriority(Enum):
    """Order priority levels."""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4

@dataclass
class OrderRequest:
    """Order request with additional metadata."""
    symbol: str
    side: OrderSide
    quantity: int
    order_type: OrderType
    price: Optional[float] = None
    stop_price: Optional[float] = None
    product_type: ProductType = ProductType.CNC
    validity: str = "DAY"
    priority: OrderPriority = OrderPriority.NORMAL
    strategy_name: Optional[str] = None
    signal_id: Optional[str] = None
    parent_order_id: Optional[str] = None
    child_orders: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # Internal fields
    request_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    created_at: datetime = field(default_factory=datetime.now)
    attempts: int = 0
    max_attempts: int = 3

@dataclass
class OrderExecution:
    """Order execution details."""
    order_id: str
    broker_order_id: str
    execution_time: datetime
    executed_quantity: int
    execution_price: float
    commission: float = 0.0
    taxes: float = 0.0
    net_amount: float = 0.0

class OrderManager:
    """Manages order lifecycle and execution."""
    
    def __init__(self):
        """Initialize order manager."""
        self.broker = get_broker()
        self.pending_orders: Dict[str, OrderRequest] = {}
        self.active_orders: Dict[str, Order] = {}
        self.completed_orders: Dict[str, Order] = {}
        self.order_executions: Dict[str, List[OrderExecution]] = {}
        
        # Order callbacks
        self.order_callbacks: Dict[str, List[Callable]] = {
            'on_order_placed': [],
            'on_order_filled': [],
            'on_order_partial_fill': [],
            'on_order_cancelled': [],
            'on_order_rejected': []
        }
        
        # Configuration
        self.config = config.get_trading_config()
        self.max_order_value = self.config.get('max_order_value', 1000000)
        self.max_position_size = self.config.get('max_position_size', 500000)
        self.order_timeout = self.config.get('order_timeout_minutes', 60)
        
        # Order processing
        self.is_running = False
        self.order_queue = asyncio.Queue()
        
        logger.info("Order Manager initialized")
    
    async def start(self):
        """Start order manager."""
        if self.is_running:
            return
        
        self.is_running = True
        
        # Start order processing task
        asyncio.create_task(self._process_orders())
        asyncio.create_task(self._monitor_orders())
        
        logger.info("Order Manager started")
    
    async def stop(self):
        """Stop order manager."""
        self.is_running = False
        logger.info("Order Manager stopped")
    
    def register_callback(self, event: str, callback: Callable):
        """Register order event callback."""
        if event in self.order_callbacks:
            self.order_callbacks[event].append(callback)
        else:
            logger.warning(f"Unknown callback event: {event}")
    
    async def submit_order(self, order_request: OrderRequest) -> str:
        """Submit order for execution."""
        try:
            # Validate order request
            validation_result = await self._validate_order_request(order_request)
            if not validation_result['valid']:
                raise Exception(f"Order validation failed: {validation_result['reason']}")
            
            # Add to pending orders
            self.pending_orders[order_request.request_id] = order_request
            
            # Add to processing queue
            await self.order_queue.put(order_request)
            
            logger.info(f"Order submitted: {order_request.request_id} - {order_request.symbol} {order_request.side.value} {order_request.quantity}")
            
            return order_request.request_id
            
        except Exception as e:
            logger.error(f"Error submitting order: {e}")
            raise
    
    async def cancel_order(self, request_id: str) -> bool:
        """Cancel order."""
        try:
            # Check if order is in pending state
            if request_id in self.pending_orders:
                del self.pending_orders[request_id]
                logger.info(f"Pending order cancelled: {request_id}")
                return True
            
            # Check if order is active
            if request_id in self.active_orders:
                order = self.active_orders[request_id]
                
                if order.broker_order_id:
                    success = await self.broker.cancel_order(order.broker_order_id)
                    
                    if success:
                        order.status = OrderStatus.CANCELLED
                        self.completed_orders[request_id] = order
                        del self.active_orders[request_id]
                        
                        # Trigger callbacks
                        await self._trigger_callbacks('on_order_cancelled', order)
                        
                        logger.info(f"Order cancelled: {request_id}")
                        return True
            
            logger.warning(f"Order not found for cancellation: {request_id}")
            return False
            
        except Exception as e:
            logger.error(f"Error cancelling order {request_id}: {e}")
            return False
    
    async def modify_order(self, request_id: str, **kwargs) -> bool:
        """Modify order."""
        try:
            if request_id not in self.active_orders:
                logger.warning(f"Order not found for modification: {request_id}")
                return False
            
            order = self.active_orders[request_id]
            
            if order.broker_order_id:
                modified_order = await self.broker.modify_order(order.broker_order_id, **kwargs)
                self.active_orders[request_id] = modified_order
                
                logger.info(f"Order modified: {request_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error modifying order {request_id}: {e}")
            return False
    
    async def get_order_status(self, request_id: str) -> Optional[Order]:
        """Get order status."""
        # Check active orders
        if request_id in self.active_orders:
            return self.active_orders[request_id]
        
        # Check completed orders
        if request_id in self.completed_orders:
            return self.completed_orders[request_id]
        
        # Check pending orders
        if request_id in self.pending_orders:
            # Create a temporary order object for pending status
            order_request = self.pending_orders[request_id]
            return Order(
                symbol=order_request.symbol,
                side=order_request.side,
                quantity=order_request.quantity,
                order_type=order_request.order_type,
                price=order_request.price,
                stop_price=order_request.stop_price,
                product_type=order_request.product_type,
                validity=order_request.validity,
                status=OrderStatus.PENDING
            )
        
        return None
    
    async def get_all_orders(self) -> Dict[str, List[Order]]:
        """Get all orders categorized by status."""
        return {
            'pending': [await self.get_order_status(req_id) for req_id in self.pending_orders.keys()],
            'active': list(self.active_orders.values()),
            'completed': list(self.completed_orders.values())
        }
    
    async def get_order_executions(self, request_id: str) -> List[OrderExecution]:
        """Get order execution details."""
        return self.order_executions.get(request_id, [])
    
    async def _process_orders(self):
        """Process orders from queue."""
        while self.is_running:
            try:
                # Get order from queue with timeout
                order_request = await asyncio.wait_for(
                    self.order_queue.get(), 
                    timeout=1.0
                )
                
                await self._execute_order(order_request)
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"Error processing order: {e}")
    
    async def _execute_order(self, order_request: OrderRequest):
        """Execute order with broker."""
        try:
            # Remove from pending orders
            if order_request.request_id in self.pending_orders:
                del self.pending_orders[order_request.request_id]
            
            # Create broker order
            broker_order = Order(
                symbol=order_request.symbol,
                side=order_request.side,
                quantity=order_request.quantity,
                order_type=order_request.order_type,
                price=order_request.price,
                stop_price=order_request.stop_price,
                product_type=order_request.product_type,
                validity=order_request.validity
            )
            
            # Execute with broker
            executed_order = await self.broker.place_order(broker_order)
            
            # Store order
            self.active_orders[order_request.request_id] = executed_order
            
            # Handle order result
            if executed_order.status == OrderStatus.COMPLETE:
                # Order fully filled
                await self._handle_order_fill(order_request.request_id, executed_order)
            elif executed_order.status == OrderStatus.OPEN:
                # Order placed successfully
                await self._trigger_callbacks('on_order_placed', executed_order)
            elif executed_order.status == OrderStatus.REJECTED:
                # Order rejected
                self.completed_orders[order_request.request_id] = executed_order
                del self.active_orders[order_request.request_id]
                await self._trigger_callbacks('on_order_rejected', executed_order)
            
            logger.info(f"Order executed: {order_request.request_id} - Status: {executed_order.status.value}")
            
        except Exception as e:
            logger.error(f"Error executing order {order_request.request_id}: {e}")
            
            # Retry logic
            order_request.attempts += 1
            if order_request.attempts < order_request.max_attempts:
                logger.info(f"Retrying order {order_request.request_id} (attempt {order_request.attempts})")
                await asyncio.sleep(5)  # Wait before retry
                await self.order_queue.put(order_request)
            else:
                logger.error(f"Order failed after {order_request.max_attempts} attempts: {order_request.request_id}")
    
    async def _monitor_orders(self):
        """Monitor active orders for status updates."""
        while self.is_running:
            try:
                # Check active orders
                for request_id, order in list(self.active_orders.items()):
                    if order.broker_order_id:
                        # Get updated status from broker
                        updated_order = await self.broker.get_order_status(order.broker_order_id)
                        
                        # Check for status changes
                        if updated_order.status != order.status:
                            self.active_orders[request_id] = updated_order
                            
                            if updated_order.status == OrderStatus.COMPLETE:
                                await self._handle_order_fill(request_id, updated_order)
                            elif updated_order.status == OrderStatus.PARTIAL:
                                await self._handle_partial_fill(request_id, updated_order)
                            elif updated_order.status in [OrderStatus.CANCELLED, OrderStatus.REJECTED]:
                                self.completed_orders[request_id] = updated_order
                                del self.active_orders[request_id]
                                
                                if updated_order.status == OrderStatus.CANCELLED:
                                    await self._trigger_callbacks('on_order_cancelled', updated_order)
                                else:
                                    await self._trigger_callbacks('on_order_rejected', updated_order)
                
                # Check for timed out orders
                await self._check_order_timeouts()
                
                # Wait before next check
                await asyncio.sleep(5)
                
            except Exception as e:
                logger.error(f"Error monitoring orders: {e}")
                await asyncio.sleep(10)
    
    async def _handle_order_fill(self, request_id: str, order: Order):
        """Handle complete order fill."""
        try:
            # Move to completed orders
            self.completed_orders[request_id] = order
            if request_id in self.active_orders:
                del self.active_orders[request_id]
            
            # Create execution record
            execution = OrderExecution(
                order_id=request_id,
                broker_order_id=order.broker_order_id,
                execution_time=datetime.now(),
                executed_quantity=order.filled_quantity,
                execution_price=order.average_price,
                commission=0.0,  # Would calculate based on broker
                taxes=0.0,
                net_amount=order.average_price * order.filled_quantity
            )
            
            if request_id not in self.order_executions:
                self.order_executions[request_id] = []
            self.order_executions[request_id].append(execution)
            
            # Trigger callbacks
            await self._trigger_callbacks('on_order_filled', order)
            
            logger.info(f"Order filled: {request_id} - {order.filled_quantity} @ {order.average_price}")
            
        except Exception as e:
            logger.error(f"Error handling order fill: {e}")
    
    async def _handle_partial_fill(self, request_id: str, order: Order):
        """Handle partial order fill."""
        try:
            # Create execution record for partial fill
            execution = OrderExecution(
                order_id=request_id,
                broker_order_id=order.broker_order_id,
                execution_time=datetime.now(),
                executed_quantity=order.filled_quantity,
                execution_price=order.average_price,
                commission=0.0,
                taxes=0.0,
                net_amount=order.average_price * order.filled_quantity
            )
            
            if request_id not in self.order_executions:
                self.order_executions[request_id] = []
            self.order_executions[request_id].append(execution)
            
            # Trigger callbacks
            await self._trigger_callbacks('on_order_partial_fill', order)
            
            logger.info(f"Partial fill: {request_id} - {order.filled_quantity}/{order.quantity} @ {order.average_price}")
            
        except Exception as e:
            logger.error(f"Error handling partial fill: {e}")
    
    async def _check_order_timeouts(self):
        """Check for timed out orders."""
        try:
            timeout_threshold = datetime.now() - timedelta(minutes=self.order_timeout)
            
            for request_id, order in list(self.active_orders.items()):
                if order.timestamp and order.timestamp < timeout_threshold:
                    logger.warning(f"Order timeout: {request_id}")
                    
                    # Try to cancel timed out order
                    await self.cancel_order(request_id)
                    
        except Exception as e:
            logger.error(f"Error checking order timeouts: {e}")
    
    async def _validate_order_request(self, order_request: OrderRequest) -> Dict[str, Any]:
        """Validate order request."""
        try:
            # Check symbol validity
            with db_manager.get_session() as db:
                stock = StockCRUD.get_stock_by_symbol(db, order_request.symbol)
                if not stock:
                    return {'valid': False, 'reason': f'Invalid symbol: {order_request.symbol}'}
                
                if not stock.is_active:
                    return {'valid': False, 'reason': f'Stock not active: {order_request.symbol}'}
            
            # Check quantity
            if order_request.quantity <= 0:
                return {'valid': False, 'reason': 'Quantity must be positive'}
            
            # Check order value
            if order_request.price:
                order_value = order_request.price * order_request.quantity
                if order_value > self.max_order_value:
                    return {'valid': False, 'reason': f'Order value exceeds limit: {order_value}'}
            
            # Check order type specific validations
            if order_request.order_type in [OrderType.LIMIT, OrderType.STOP_LOSS] and not order_request.price:
                return {'valid': False, 'reason': f'{order_request.order_type.value} order requires price'}
            
            if order_request.order_type in [OrderType.STOP_LOSS, OrderType.STOP_LOSS_MARKET] and not order_request.stop_price:
                return {'valid': False, 'reason': f'{order_request.order_type.value} order requires stop price'}
            
            return {'valid': True, 'reason': 'Order is valid'}
            
        except Exception as e:
            logger.error(f"Error validating order: {e}")
            return {'valid': False, 'reason': f'Validation error: {str(e)}'}
    
    async def _trigger_callbacks(self, event: str, order: Order):
        """Trigger order event callbacks."""
        try:
            callbacks = self.order_callbacks.get(event, [])
            for callback in callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(order)
                    else:
                        callback(order)
                except Exception as e:
                    logger.error(f"Error in callback {callback.__name__}: {e}")
                    
        except Exception as e:
            logger.error(f"Error triggering callbacks for {event}: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get order manager statistics."""
        return {
            'pending_orders': len(self.pending_orders),
            'active_orders': len(self.active_orders),
            'completed_orders': len(self.completed_orders),
            'total_executions': sum(len(execs) for execs in self.order_executions.values()),
            'is_running': self.is_running
        }

# Global order manager instance
order_manager = OrderManager()

async def initialize_order_manager() -> bool:
    """Initialize order manager."""
    try:
        await order_manager.start()
        return True
    except Exception as e:
        logger.error(f"Failed to initialize order manager: {e}")
        return False
