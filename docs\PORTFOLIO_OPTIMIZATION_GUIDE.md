# Portfolio Optimization & Analytics Guide

## Overview

The Portfolio Optimization & Analytics system provides sophisticated portfolio construction, optimization algorithms, and comprehensive performance analytics. It implements multiple optimization approaches including Modern Portfolio Theory, Black-Litterman model, Risk Parity, and advanced performance attribution analysis.

## Architecture

### Core Components

1. **Modern Portfolio Theory (MPT) Optimizer** (`mpt_optimizer.py`)
   - Maximum Sharpe ratio optimization
   - Minimum variance optimization
   - Target return optimization
   - Efficient frontier generation

2. **Black-Litterman Model** (`black_litterman.py`)
   - Incorporates investor views with market equilibrium
   - Bayesian approach to expected returns
   - View uncertainty modeling

3. **Risk Parity Optimizer** (`risk_parity.py`)
   - Equal Risk Contribution (ERC)
   - Hierarchical Risk Parity (HRP)
   - Risk-based allocation strategies

4. **Performance Analytics** (`performance_analytics.py`)
   - Comprehensive performance metrics
   - Attribution analysis
   - Risk decomposition
   - Rolling performance analysis

5. **Rebalancing System** (`rebalancing.py`)
   - Multiple rebalancing triggers
   - Transaction cost optimization
   - Turnover analysis

6. **Main Optimizer** (`optimizer.py`)
   - Unified optimization interface
   - Method comparison capabilities
   - Integrated analytics

## Optimization Methods

### 1. Maximum Sharpe Ratio
Optimizes portfolio for maximum risk-adjusted returns.

```python
from src.portfolio_optimization.optimizer import (
    portfolio_optimizer, PortfolioOptimizationRequest, OptimizationMethod
)

request = PortfolioOptimizationRequest(
    symbols=['RELIANCE', 'TCS', 'HDFCBANK', 'INFY', 'ICICIBANK'],
    method=OptimizationMethod.MAX_SHARPE,
    lookback_days=252
)

response = portfolio_optimizer.optimize_portfolio(request)
```

### 2. Minimum Variance
Minimizes portfolio volatility while maintaining diversification.

```python
request = PortfolioOptimizationRequest(
    symbols=['RELIANCE', 'TCS', 'HDFCBANK'],
    method=OptimizationMethod.MIN_VARIANCE,
    constraints=PortfolioConstraints(max_concentration=0.4)
)

response = portfolio_optimizer.optimize_portfolio(request)
```

### 3. Target Return
Achieves specific return target with minimum risk.

```python
request = PortfolioOptimizationRequest(
    symbols=['RELIANCE', 'TCS', 'HDFCBANK'],
    method=OptimizationMethod.TARGET_RETURN,
    target_return=0.15  # 15% target return
)

response = portfolio_optimizer.optimize_portfolio(request)
```

### 4. Risk Parity
Equal risk contribution from all portfolio components.

```python
# Equal Risk Contribution
request = PortfolioOptimizationRequest(
    symbols=['RELIANCE', 'TCS', 'HDFCBANK'],
    method=OptimizationMethod.RISK_PARITY_ERC
)

# Hierarchical Risk Parity
request = PortfolioOptimizationRequest(
    symbols=['RELIANCE', 'TCS', 'HDFCBANK'],
    method=OptimizationMethod.RISK_PARITY_HRP
)
```

### 5. Black-Litterman
Incorporates investor views with market equilibrium.

```python
from src.portfolio_optimization.black_litterman import InvestorView

# Define investor views
views = [
    InvestorView(
        assets=['RELIANCE'],
        view_return=0.15,
        confidence=0.8,
        view_type='absolute',
        description='Bullish on Reliance'
    ),
    InvestorView(
        assets=['TCS', 'INFY'],
        view_return=0.05,  # TCS outperforms INFY by 5%
        confidence=0.6,
        view_type='relative',
        description='TCS vs INFY'
    )
]

request = PortfolioOptimizationRequest(
    symbols=['RELIANCE', 'TCS', 'INFY'],
    method=OptimizationMethod.BLACK_LITTERMAN,
    investor_views=views
)
```

## Portfolio Constraints

### Basic Constraints
```python
from src.portfolio_optimization.mpt_optimizer import PortfolioConstraints

constraints = PortfolioConstraints(
    min_weight=0.02,        # Minimum 2% allocation
    max_weight=0.25,        # Maximum 25% allocation
    max_concentration=0.20, # Maximum 20% in any stock
    min_positions=5,        # Minimum 5 positions
    max_positions=20        # Maximum 20 positions
)
```

### Advanced Constraints
```python
constraints = PortfolioConstraints(
    min_weight=0.01,
    max_weight=0.30,
    max_concentration=0.15,
    sector_limits={
        'Technology': 0.40,
        'Banking': 0.30,
        'Energy': 0.20
    },
    turnover_limit=0.50,
    tracking_error_limit=0.05
)
```

## Performance Analytics

### Basic Performance Metrics
```python
from src.portfolio_optimization.performance_analytics import performance_analytics

# Calculate comprehensive metrics
performance_metrics = performance_analytics.calculate_performance_metrics(
    portfolio_returns,
    benchmark_returns
)

print(f"Sharpe Ratio: {performance_metrics.sharpe_ratio:.4f}")
print(f"Max Drawdown: {performance_metrics.max_drawdown:.4f}")
print(f"Volatility: {performance_metrics.volatility:.4f}")
```

### Attribution Analysis
```python
attribution = performance_analytics.calculate_attribution_analysis(
    portfolio_weights={'RELIANCE': 0.3, 'TCS': 0.3, 'HDFCBANK': 0.4},
    benchmark_weights={'RELIANCE': 0.25, 'TCS': 0.25, 'HDFCBANK': 0.5},
    asset_returns=asset_returns_dict,
    benchmark_returns=benchmark_returns
)

print(f"Allocation Effect: {attribution.allocation_effect}")
print(f"Selection Effect: {attribution.selection_effect}")
```

### Risk Decomposition
```python
risk_decomposition = performance_analytics.calculate_risk_decomposition(
    weights=portfolio_weights,
    covariance_matrix=covariance_matrix,
    symbols=symbols
)

print(f"Total Risk: {risk_decomposition.total_risk:.4f}")
print(f"Risk Contributions: {risk_decomposition.risk_contributions}")
```

## Rebalancing System

### Time-Based Rebalancing
```python
from src.portfolio_optimization.rebalancing import (
    portfolio_rebalancer, RebalancingConfig, RebalancingTrigger
)

config = RebalancingConfig(
    trigger_type=RebalancingTrigger.TIME_BASED,
    frequency_days=30,  # Monthly rebalancing
    transaction_cost=0.001
)

result = portfolio_rebalancer.execute_rebalancing(
    current_weights,
    target_weights,
    portfolio_value=1000000,
    config=config
)
```

### Threshold-Based Rebalancing
```python
config = RebalancingConfig(
    trigger_type=RebalancingTrigger.THRESHOLD_BASED,
    threshold_percent=0.05,  # 5% deviation threshold
    min_trade_size=0.02,     # Minimum 2% trade
    max_turnover=0.50        # Maximum 50% turnover
)
```

### Volatility-Based Rebalancing
```python
config = RebalancingConfig(
    trigger_type=RebalancingTrigger.VOLATILITY_BASED,
    volatility_threshold=0.25,  # 25% volatility threshold
    transaction_cost=0.001
)
```

## API Endpoints

### Portfolio Optimization
```bash
# Optimize portfolio
POST /api/v1/portfolio-optimization/optimize
{
    "symbols": ["RELIANCE", "TCS", "HDFCBANK"],
    "method": "max_sharpe",
    "constraints": {
        "min_weight": 0.02,
        "max_weight": 0.40,
        "max_concentration": 0.25
    },
    "lookback_days": 252
}

# Compare optimization methods
POST /api/v1/portfolio-optimization/compare-methods
{
    "symbols": ["RELIANCE", "TCS", "HDFCBANK"],
    "methods": ["max_sharpe", "min_variance", "risk_parity_erc"],
    "lookback_days": 252
}

# Generate efficient frontier
GET /api/v1/portfolio-optimization/efficient-frontier?symbols=RELIANCE,TCS,HDFCBANK&n_points=50
```

### Performance Analysis
```bash
# Analyze portfolio performance
GET /api/v1/portfolio-optimization/performance/analyze?symbols=RELIANCE,TCS&weights=0.5,0.5&lookback_days=252

# Rebalancing analysis
POST /api/v1/portfolio-optimization/rebalancing/analyze
{
    "current_weights": {"RELIANCE": 0.4, "TCS": 0.6},
    "target_weights": {"RELIANCE": 0.5, "TCS": 0.5},
    "portfolio_value": 1000000,
    "config": {
        "trigger_type": "threshold_based",
        "threshold_percent": 0.05
    }
}
```

## Advanced Features

### Efficient Frontier Generation
```python
from src.portfolio_optimization.mpt_optimizer import mpt_optimizer

expected_returns, covariance_matrix = mpt_optimizer.calculate_returns_covariance(
    symbols, lookback_days=252
)

efficient_portfolios = mpt_optimizer.generate_efficient_frontier(
    expected_returns, covariance_matrix, constraints, n_points=50
)

# Find optimal portfolios
max_sharpe_portfolio = max(efficient_portfolios, key=lambda p: p.sharpe_ratio)
min_variance_portfolio = min(efficient_portfolios, key=lambda p: p.volatility)
```

### Method Comparison
```python
methods = [
    OptimizationMethod.MAX_SHARPE,
    OptimizationMethod.MIN_VARIANCE,
    OptimizationMethod.RISK_PARITY_ERC,
    OptimizationMethod.EQUAL_WEIGHT
]

comparison_results = portfolio_optimizer.compare_optimization_methods(
    symbols, methods, constraints, lookback_days=252
)

# Analyze results
for method, response in comparison_results.items():
    print(f"{method}: Sharpe={response.optimization_result.sharpe_ratio:.4f}")
```

### Rolling Performance Analysis
```python
rolling_metrics = performance_analytics.calculate_rolling_metrics(
    portfolio_returns,
    window=60,  # 60-day rolling window
    metrics=['return', 'volatility', 'sharpe', 'max_drawdown']
)

# Plot rolling metrics
import matplotlib.pyplot as plt

plt.figure(figsize=(12, 8))
plt.subplot(2, 2, 1)
plt.plot(rolling_metrics['return'])
plt.title('Rolling Returns')

plt.subplot(2, 2, 2)
plt.plot(rolling_metrics['volatility'])
plt.title('Rolling Volatility')
```

## Best Practices

### 1. Data Quality
- Ensure sufficient historical data (minimum 252 days)
- Handle corporate actions and stock splits
- Use adjusted prices for accurate returns

### 2. Constraint Setting
- Set realistic weight constraints
- Consider liquidity constraints
- Account for transaction costs

### 3. Risk Management
- Monitor concentration risk
- Set appropriate sector limits
- Regular rebalancing analysis

### 4. Performance Monitoring
- Track multiple performance metrics
- Compare against benchmarks
- Analyze attribution regularly

### 5. Optimization Frequency
- Balance optimization benefits with costs
- Consider market regime changes
- Monitor model stability

## Integration with Live Trading

### Signal-Based Views
```python
# Convert strategy signals to Black-Litterman views
from src.portfolio_optimization.black_litterman import bl_optimizer

strategy_signals = {
    'RELIANCE': 0.8,   # Strong buy signal
    'TCS': -0.6,       # Moderate sell signal
    'HDFCBANK': 0.4    # Weak buy signal
}

views = bl_optimizer.create_strategy_views(strategy_signals)

# Use in optimization
request = PortfolioOptimizationRequest(
    symbols=list(strategy_signals.keys()),
    method=OptimizationMethod.BLACK_LITTERMAN,
    investor_views=views
)
```

### Automated Rebalancing
```python
# Create rebalancing schedule
rebalancing_schedule = portfolio_rebalancer.create_rebalancing_schedule(
    symbols=symbols,
    optimization_method="max_sharpe",
    rebalancing_frequency=30,  # Monthly
    start_date=date(2023, 1, 1),
    end_date=date(2024, 1, 1)
)

# Analyze rebalancing performance
analysis = portfolio_rebalancer.analyze_rebalancing_performance(
    rebalancing_schedule
)
```

## Troubleshooting

### Common Issues

1. **Optimization Failure**
   - Check constraint feasibility
   - Ensure sufficient data quality
   - Verify covariance matrix conditioning

2. **Poor Performance**
   - Review lookback period
   - Check for regime changes
   - Validate input data

3. **High Transaction Costs**
   - Increase rebalancing thresholds
   - Reduce rebalancing frequency
   - Optimize trade sizing

### Error Handling
```python
try:
    response = portfolio_optimizer.optimize_portfolio(request)
    if not response.optimization_result.optimization_success:
        print("Optimization failed - check constraints")
except Exception as e:
    print(f"Optimization error: {e}")
```

## Performance Considerations

### Computational Efficiency
- Use appropriate lookback periods
- Cache covariance calculations
- Parallel processing for comparisons

### Memory Management
- Limit historical data retention
- Use efficient data structures
- Regular garbage collection

### Scalability
- Database query optimization
- Asynchronous processing
- Distributed computing support

---

The Portfolio Optimization & Analytics system provides institutional-grade portfolio management capabilities with comprehensive risk controls, performance analytics, and integration with the live trading system.
