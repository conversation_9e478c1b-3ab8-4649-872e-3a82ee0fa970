#!/usr/bin/env python3
"""
AI-Powered Stock Analysis System
Main entry point for the application
"""

import asyncio
import sys
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.utils.logger import get_logger
from src.utils.config import config

logger = get_logger(__name__)

async def main():
    """Main application entry point."""
    logger.info("Starting AI-Powered Stock Analysis System")
    
    # Print configuration summary
    logger.info(f"Environment: {config.get('environment', 'development')}")
    logger.info(f"Database: {config.get('database.host')}:{config.get('database.port')}")
    logger.info(f"Enabled strategies: {[name for name in ['rsi', 'pivot_points', 'macd', 'moving_averages', 'breakout'] if config.is_strategy_enabled(name)]}")
    
    try:
        # Import and run the main application
        from src.stock_ai_agent import StockAIAgent
        
        agent = StockAIAgent()
        await agent.run()
        
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
    except Exception as e:
        logger.error(f"Application error: {e}")
        raise
    finally:
        logger.info("Application shutdown complete")

if __name__ == "__main__":
    asyncio.run(main())
