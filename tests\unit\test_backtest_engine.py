"""
Unit tests for Backtesting Engine.
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
from unittest.mock import Mock, patch, MagicMock
import backtrader as bt

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from src.backtesting.backtest_engine import BacktestEngine, BacktraderStrategy, PositionSizer, CommissionScheme
from src.backtesting.backtest_utils import PerformanceMetrics, BacktestValidator
from src.strategies.base_strategy import TradingSignal, SignalType

class TestBacktestEngine:
    """Test cases for Backtesting Engine."""
    
    @pytest.fixture
    def engine(self):
        """Create backtest engine instance."""
        return BacktestEngine()
    
    @pytest.fixture
    def sample_price_data(self):
        """Create sample price data."""
        dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
        np.random.seed(42)
        
        prices = []
        current_price = 100.0
        
        for _ in range(len(dates)):
            change = np.random.normal(0, 0.02)
            current_price *= (1 + change)
            prices.append(current_price)
        
        data = []
        for i, date in enumerate(dates[:len(prices)]):
            data.append({
                'date': date.date(),
                'open_price': prices[i] * 0.999,
                'high_price': prices[i] * 1.01,
                'low_price': prices[i] * 0.99,
                'close_price': prices[i],
                'volume': np.random.randint(100000, 1000000),
                'sma_20': prices[i] * 1.001 if i > 20 else None,
                'rsi_14': 50 + np.random.normal(0, 10) if i > 14 else None
            })
        
        return data
    
    @pytest.fixture
    def mock_strategy(self):
        """Create mock strategy."""
        strategy = Mock()
        strategy.calculate_signals.return_value = [
            TradingSignal(
                symbol="TEST",
                signal_type=SignalType.BUY,
                signal_strength=0.8,
                price=100.0,
                target_price=110.0,
                stop_loss=95.0
            )
        ]
        return strategy
    
    def test_engine_initialization(self, engine):
        """Test engine initialization."""
        assert engine.initial_capital == 1000000  # Default capital
        assert engine.commission == 0.001  # Default commission
        assert isinstance(engine.results, dict)
    
    @patch('src.backtesting.backtest_engine.db_manager')
    @patch('src.backtesting.backtest_engine.StockCRUD')
    @patch('src.backtesting.backtest_engine.PriceCRUD')
    def test_prepare_data(self, mock_price_crud, mock_stock_crud, mock_db_manager, engine, sample_price_data):
        """Test data preparation for backtesting."""
        # Mock database responses
        mock_session = Mock()
        mock_db_manager.get_session.return_value.__enter__.return_value = mock_session
        
        mock_stock = Mock()
        mock_stock.id = 1
        mock_stock_crud.get_stock_by_symbol.return_value = mock_stock
        
        # Mock price data
        mock_prices = []
        for data_point in sample_price_data[:100]:  # Use first 100 points
            mock_price = Mock()
            for key, value in data_point.items():
                setattr(mock_price, key, value)
            mock_prices.append(mock_price)
        
        mock_price_crud.get_price_history.return_value = mock_prices
        
        # Test data preparation
        start_date = date(2023, 1, 1)
        end_date = date(2023, 12, 31)
        
        data_feed = engine.prepare_data("TEST", start_date, end_date)
        
        assert data_feed is not None
        mock_stock_crud.get_stock_by_symbol.assert_called_once_with(mock_session, "TEST")
        mock_price_crud.get_price_history.assert_called_once()
    
    def test_position_sizer(self):
        """Test custom position sizer."""
        sizer = PositionSizer()
        
        # Mock broker and data
        mock_broker = Mock()
        mock_broker.getposition.return_value.size = 0
        
        mock_data = Mock()
        mock_data.close = [100.0]
        
        # Mock strategy with stop loss
        mock_strategy = Mock()
        mock_strategy._current_stop_loss = 95.0
        
        sizer.broker = mock_broker
        sizer.strategy = mock_strategy
        
        # Test position sizing
        cash = 100000
        size = sizer._getsizing(None, cash, mock_data, True)
        
        assert size > 0
        assert isinstance(size, int)
    
    def test_commission_scheme(self):
        """Test custom commission scheme."""
        commission = CommissionScheme()
        
        # Test commission calculation
        size = 100
        price = 100.0
        
        comm = commission._getcommission(size, price, False)
        
        assert comm >= 20.0  # Minimum commission
        assert comm == max(size * price * 0.001, 20.0)
    
    @patch('src.backtesting.backtest_engine.strategy_registry')
    @patch('src.backtesting.backtest_engine.db_manager')
    def test_single_strategy_backtest(self, mock_db_manager, mock_strategy_registry, engine, mock_strategy):
        """Test single strategy backtesting."""
        # Mock strategy registry
        mock_strategy_registry.create_strategy.return_value = mock_strategy
        
        # Mock database
        mock_session = Mock()
        mock_db_manager.get_session.return_value.__enter__.return_value = mock_session
        
        # Mock data preparation
        with patch.object(engine, 'prepare_data') as mock_prepare:
            mock_data_feed = Mock()
            mock_prepare.return_value = mock_data_feed
            
            # Mock Cerebro
            with patch('src.backtesting.backtest_engine.bt.Cerebro') as mock_cerebro_class:
                mock_cerebro = Mock()
                mock_cerebro_class.return_value = mock_cerebro
                
                # Mock broker
                mock_broker = Mock()
                mock_broker.getvalue.side_effect = [1000000, 1100000]  # Initial and final values
                mock_cerebro.broker = mock_broker
                
                # Mock strategy result
                mock_result = Mock()
                mock_result.analyzers.trades.get_analysis.return_value = {
                    'total': {'total': 10},
                    'won': {'total': 7, 'pnl': {'average': 1000, 'total': 7000}},
                    'lost': {'total': 3, 'pnl': {'average': -500, 'total': -1500}}
                }
                mock_result.analyzers.sharpe.get_analysis.return_value = {'sharperatio': 1.5}
                mock_result.analyzers.drawdown.get_analysis.return_value = {'max': {'drawdown': 5.0}}
                mock_result.analyzers.returns.get_analysis.return_value = {}
                mock_result.analyzers.sqn.get_analysis.return_value = {'sqn': 2.0}
                
                mock_cerebro.run.return_value = [mock_result]
                
                # Run backtest
                result = engine.run_single_strategy_backtest(
                    "test_strategy", "TEST", date(2023, 1, 1), date(2023, 12, 31)
                )
                
                # Verify results
                assert 'error' not in result
                assert result['strategy_name'] == 'test_strategy'
                assert result['symbol'] == 'TEST'
                assert result['total_return'] == 10.0  # (1100000 - 1000000) / 1000000 * 100
                assert result['total_trades'] == 10
                assert result['winning_trades'] == 7
                assert result['losing_trades'] == 3

class TestPerformanceMetrics:
    """Test cases for Performance Metrics."""
    
    def test_sharpe_ratio_calculation(self):
        """Test Sharpe ratio calculation."""
        # Create sample returns
        returns = pd.Series([0.01, 0.02, -0.01, 0.015, 0.005] * 50)  # 250 days
        
        sharpe = PerformanceMetrics.calculate_sharpe_ratio(returns)
        
        assert isinstance(sharpe, float)
        assert sharpe > 0  # Should be positive for positive average returns
    
    def test_sortino_ratio_calculation(self):
        """Test Sortino ratio calculation."""
        returns = pd.Series([0.01, 0.02, -0.01, 0.015, 0.005] * 50)
        
        sortino = PerformanceMetrics.calculate_sortino_ratio(returns)
        
        assert isinstance(sortino, float)
        assert sortino > 0
    
    def test_max_drawdown_calculation(self):
        """Test maximum drawdown calculation."""
        # Create equity curve with known drawdown
        equity = pd.Series([100, 110, 105, 95, 90, 100, 120])
        
        max_dd, start_idx, end_idx = PerformanceMetrics.calculate_max_drawdown(equity)
        
        assert isinstance(max_dd, float)
        assert max_dd > 0
        assert start_idx < end_idx
    
    def test_var_calculation(self):
        """Test Value at Risk calculation."""
        returns = pd.Series(np.random.normal(0.001, 0.02, 1000))
        
        var = PerformanceMetrics.calculate_var(returns)
        
        assert isinstance(var, float)
        assert var < 0  # VaR should be negative (loss)
    
    def test_cvar_calculation(self):
        """Test Conditional Value at Risk calculation."""
        returns = pd.Series(np.random.normal(0.001, 0.02, 1000))
        
        cvar = PerformanceMetrics.calculate_cvar(returns)
        
        assert isinstance(cvar, float)
        assert cvar < 0  # CVaR should be negative (loss)

class TestBacktestValidator:
    """Test cases for Backtest Validator."""
    
    def test_validate_results_valid(self):
        """Test validation of valid results."""
        valid_results = {
            'total_return': 15.5,
            'sharpe_ratio': 1.2,
            'max_drawdown': 8.5,
            'total_trades': 50,
            'win_rate': 65.0
        }
        
        validation = BacktestValidator.validate_results(valid_results)
        
        assert validation['is_valid'] == True
        assert len(validation['errors']) == 0
    
    def test_validate_results_missing_fields(self):
        """Test validation with missing required fields."""
        invalid_results = {
            'total_return': 15.5,
            # Missing other required fields
        }
        
        validation = BacktestValidator.validate_results(invalid_results)
        
        assert validation['is_valid'] == False
        assert len(validation['errors']) > 0
    
    def test_validate_results_warnings(self):
        """Test validation warnings for suspicious results."""
        suspicious_results = {
            'total_return': 1500.0,  # Extremely high return
            'sharpe_ratio': 5.0,     # Very high Sharpe ratio
            'max_drawdown': 60.0,    # High drawdown
            'total_trades': 5,       # Low trade count
            'win_rate': 95.0         # Extremely high win rate
        }
        
        validation = BacktestValidator.validate_results(suspicious_results)
        
        assert len(validation['warnings']) > 0
        assert len(validation['recommendations']) > 0
    
    def test_detect_overfitting(self):
        """Test overfitting detection."""
        overfitted_results = {
            'win_rate': 98.0,
            'sharpe_ratio': 6.0,
            'max_drawdown': 1.0,
            'profit_factor': 8.0,
            'total_return': 500.0,
            'total_trades': 100
        }
        
        overfitting_analysis = BacktestValidator.detect_overfitting(overfitted_results)
        
        assert overfitting_analysis['overfitting_detected'] == True
        assert len(overfitting_analysis['signals']) > 0
        assert overfitting_analysis['confidence'] > 0.5

class TestBacktraderStrategy:
    """Test cases for Backtrader Strategy wrapper."""
    
    def test_strategy_initialization(self):
        """Test strategy initialization."""
        mock_strategy_instance = Mock()
        
        bt_strategy = BacktraderStrategy()
        bt_strategy.params.strategy_instance = mock_strategy_instance
        bt_strategy.params.symbol = "TEST"
        
        # Initialize strategy
        bt_strategy.__init__()
        
        assert bt_strategy.strategy_instance == mock_strategy_instance
        assert bt_strategy.symbol == "TEST"
        assert bt_strategy.trade_count == 0
    
    def test_notify_trade(self):
        """Test trade notification handling."""
        bt_strategy = BacktraderStrategy()
        bt_strategy.params.strategy_instance = Mock()
        bt_strategy.params.symbol = "TEST"
        bt_strategy.__init__()
        
        # Mock closed trade
        mock_trade = Mock()
        mock_trade.isclosed = True
        mock_trade.pnl = 1000.0
        mock_trade.size = 100
        mock_trade.price = 100.0
        mock_trade.dtopen = 737000  # Mock date number
        mock_trade.dtclose = 737005
        mock_trade.barlen = 5
        
        # Notify trade
        bt_strategy.notify_trade(mock_trade)
        
        assert bt_strategy.trade_count == 1
        assert bt_strategy.winning_trades == 1
        assert bt_strategy.total_pnl == 1000.0
        assert len(bt_strategy.trades) == 1

if __name__ == "__main__":
    pytest.main([__file__])
