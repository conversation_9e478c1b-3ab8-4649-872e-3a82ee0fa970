"""
Portfolio Performance Analytics.
Comprehensive performance measurement, attribution analysis, and risk metrics.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime, date, timedelta
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')

from ..data.database import db_manager
from ..data.crud import PriceCRUD, StockCRUD
from ..utils.logger import get_logger

logger = get_logger(__name__)

@dataclass
class PerformanceMetrics:
    """Portfolio performance metrics."""
    total_return: float
    annualized_return: float
    volatility: float
    sharpe_ratio: float
    sortino_ratio: float
    max_drawdown: float
    calmar_ratio: float
    var_95: float  # Value at Risk (95%)
    cvar_95: float  # Conditional VaR (95%)
    skewness: float
    kurtosis: float
    beta: float
    alpha: float
    information_ratio: float
    tracking_error: float
    win_rate: float
    profit_factor: float
    recovery_factor: float
    ulcer_index: float

@dataclass
class AttributionAnalysis:
    """Performance attribution analysis."""
    asset_contributions: Dict[str, float]
    sector_contributions: Dict[str, float]
    allocation_effect: Dict[str, float]
    selection_effect: Dict[str, float]
    interaction_effect: Dict[str, float]
    total_active_return: float
    benchmark_return: float
    portfolio_return: float

@dataclass
class RiskDecomposition:
    """Portfolio risk decomposition."""
    total_risk: float
    systematic_risk: float
    idiosyncratic_risk: float
    factor_exposures: Dict[str, float]
    risk_contributions: Dict[str, float]
    marginal_risk_contributions: Dict[str, float]
    component_var: Dict[str, float]

class PerformanceAnalytics:
    """Portfolio performance analytics engine."""
    
    def __init__(self):
        """Initialize performance analytics."""
        self.risk_free_rate = 0.06  # 6% risk-free rate
        self.trading_days_per_year = 252
        
        logger.info("Performance Analytics initialized")
    
    def calculate_returns(
        self, 
        prices: np.ndarray, 
        method: str = "simple"
    ) -> np.ndarray:
        """Calculate returns from price series."""
        try:
            if method == "simple":
                returns = np.diff(prices) / prices[:-1]
            elif method == "log":
                returns = np.diff(np.log(prices))
            else:
                raise ValueError(f"Unknown return calculation method: {method}")
            
            return returns
            
        except Exception as e:
            logger.error(f"Error calculating returns: {e}")
            return np.array([])
    
    def calculate_portfolio_returns(
        self, 
        weights: np.ndarray, 
        asset_returns: np.ndarray
    ) -> np.ndarray:
        """Calculate portfolio returns from asset weights and returns."""
        try:
            # asset_returns should be (time, assets)
            # weights should be (assets,)
            portfolio_returns = np.dot(asset_returns, weights)
            
            return portfolio_returns
            
        except Exception as e:
            logger.error(f"Error calculating portfolio returns: {e}")
            return np.array([])
    
    def calculate_performance_metrics(
        self, 
        returns: np.ndarray,
        benchmark_returns: Optional[np.ndarray] = None
    ) -> PerformanceMetrics:
        """Calculate comprehensive performance metrics."""
        try:
            if len(returns) == 0:
                raise ValueError("Empty returns array")
            
            # Basic metrics
            total_return = np.prod(1 + returns) - 1
            annualized_return = (1 + total_return) ** (self.trading_days_per_year / len(returns)) - 1
            volatility = np.std(returns) * np.sqrt(self.trading_days_per_year)
            
            # Risk-adjusted metrics
            excess_returns = returns - self.risk_free_rate / self.trading_days_per_year
            sharpe_ratio = np.mean(excess_returns) / np.std(returns) * np.sqrt(self.trading_days_per_year)
            
            # Sortino ratio (downside deviation)
            downside_returns = returns[returns < 0]
            downside_deviation = np.std(downside_returns) * np.sqrt(self.trading_days_per_year) if len(downside_returns) > 0 else 0
            sortino_ratio = (annualized_return - self.risk_free_rate) / downside_deviation if downside_deviation > 0 else 0
            
            # Drawdown metrics
            cumulative_returns = np.cumprod(1 + returns)
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdowns = (cumulative_returns - running_max) / running_max
            max_drawdown = np.min(drawdowns)
            
            # Calmar ratio
            calmar_ratio = annualized_return / abs(max_drawdown) if max_drawdown != 0 else 0
            
            # VaR and CVaR
            var_95 = np.percentile(returns, 5)
            cvar_95 = np.mean(returns[returns <= var_95])
            
            # Higher moments
            skewness = self._calculate_skewness(returns)
            kurtosis = self._calculate_kurtosis(returns)
            
            # Market-relative metrics
            beta = 1.0
            alpha = 0.0
            information_ratio = 0.0
            tracking_error = 0.0
            
            if benchmark_returns is not None and len(benchmark_returns) == len(returns):
                beta = self._calculate_beta(returns, benchmark_returns)
                alpha = annualized_return - (self.risk_free_rate + beta * (np.mean(benchmark_returns) * self.trading_days_per_year - self.risk_free_rate))
                
                active_returns = returns - benchmark_returns
                tracking_error = np.std(active_returns) * np.sqrt(self.trading_days_per_year)
                information_ratio = np.mean(active_returns) / np.std(active_returns) * np.sqrt(self.trading_days_per_year) if np.std(active_returns) > 0 else 0
            
            # Win rate
            win_rate = np.sum(returns > 0) / len(returns)
            
            # Profit factor
            positive_returns = returns[returns > 0]
            negative_returns = returns[returns < 0]
            profit_factor = np.sum(positive_returns) / abs(np.sum(negative_returns)) if len(negative_returns) > 0 else np.inf
            
            # Recovery factor
            recovery_factor = total_return / abs(max_drawdown) if max_drawdown != 0 else 0
            
            # Ulcer Index
            ulcer_index = np.sqrt(np.mean(drawdowns ** 2))
            
            return PerformanceMetrics(
                total_return=total_return,
                annualized_return=annualized_return,
                volatility=volatility,
                sharpe_ratio=sharpe_ratio,
                sortino_ratio=sortino_ratio,
                max_drawdown=max_drawdown,
                calmar_ratio=calmar_ratio,
                var_95=var_95,
                cvar_95=cvar_95,
                skewness=skewness,
                kurtosis=kurtosis,
                beta=beta,
                alpha=alpha,
                information_ratio=information_ratio,
                tracking_error=tracking_error,
                win_rate=win_rate,
                profit_factor=profit_factor,
                recovery_factor=recovery_factor,
                ulcer_index=ulcer_index
            )
            
        except Exception as e:
            logger.error(f"Error calculating performance metrics: {e}")
            raise
    
    def calculate_attribution_analysis(
        self,
        portfolio_weights: Dict[str, float],
        benchmark_weights: Dict[str, float],
        asset_returns: Dict[str, np.ndarray],
        benchmark_returns: np.ndarray
    ) -> AttributionAnalysis:
        """Calculate performance attribution analysis."""
        try:
            symbols = list(portfolio_weights.keys())
            
            # Calculate returns
            portfolio_return = 0.0
            benchmark_return = np.mean(benchmark_returns)
            
            asset_contributions = {}
            allocation_effect = {}
            selection_effect = {}
            interaction_effect = {}
            
            for symbol in symbols:
                if symbol in asset_returns:
                    asset_return = np.mean(asset_returns[symbol])
                    portfolio_weight = portfolio_weights.get(symbol, 0.0)
                    benchmark_weight = benchmark_weights.get(symbol, 0.0)
                    
                    # Asset contribution to portfolio return
                    asset_contributions[symbol] = portfolio_weight * asset_return
                    portfolio_return += asset_contributions[symbol]
                    
                    # Attribution effects
                    allocation_effect[symbol] = (portfolio_weight - benchmark_weight) * benchmark_return
                    selection_effect[symbol] = benchmark_weight * (asset_return - benchmark_return)
                    interaction_effect[symbol] = (portfolio_weight - benchmark_weight) * (asset_return - benchmark_return)
            
            # Sector attribution (simplified)
            sector_contributions = {}
            with db_manager.get_session() as db:
                for symbol in symbols:
                    stock = StockCRUD.get_stock_by_symbol(db, symbol)
                    if stock and stock.sector:
                        sector = stock.sector
                        if sector not in sector_contributions:
                            sector_contributions[sector] = 0.0
                        sector_contributions[sector] += asset_contributions.get(symbol, 0.0)
            
            total_active_return = portfolio_return - benchmark_return
            
            return AttributionAnalysis(
                asset_contributions=asset_contributions,
                sector_contributions=sector_contributions,
                allocation_effect=allocation_effect,
                selection_effect=selection_effect,
                interaction_effect=interaction_effect,
                total_active_return=total_active_return,
                benchmark_return=benchmark_return,
                portfolio_return=portfolio_return
            )
            
        except Exception as e:
            logger.error(f"Error in attribution analysis: {e}")
            raise
    
    def calculate_risk_decomposition(
        self,
        weights: np.ndarray,
        covariance_matrix: np.ndarray,
        symbols: List[str]
    ) -> RiskDecomposition:
        """Calculate portfolio risk decomposition."""
        try:
            # Total portfolio risk
            portfolio_variance = np.dot(weights.T, np.dot(covariance_matrix, weights))
            total_risk = np.sqrt(portfolio_variance)
            
            # Risk contributions
            marginal_contributions = np.dot(covariance_matrix, weights) / total_risk
            risk_contributions = weights * marginal_contributions
            
            # Component VaR (simplified)
            component_var = {}
            risk_contributions_dict = {}
            marginal_risk_dict = {}
            
            for i, symbol in enumerate(symbols):
                risk_contributions_dict[symbol] = risk_contributions[i]
                marginal_risk_dict[symbol] = marginal_contributions[i]
                component_var[symbol] = weights[i] * marginal_contributions[i]
            
            # Factor decomposition (simplified - would need factor model)
            systematic_risk = total_risk * 0.7  # Approximate
            idiosyncratic_risk = total_risk * 0.3  # Approximate
            
            factor_exposures = {
                'market': 1.0,  # Would calculate from factor model
                'size': 0.0,
                'value': 0.0,
                'momentum': 0.0
            }
            
            return RiskDecomposition(
                total_risk=total_risk,
                systematic_risk=systematic_risk,
                idiosyncratic_risk=idiosyncratic_risk,
                factor_exposures=factor_exposures,
                risk_contributions=risk_contributions_dict,
                marginal_risk_contributions=marginal_risk_dict,
                component_var=component_var
            )
            
        except Exception as e:
            logger.error(f"Error in risk decomposition: {e}")
            raise
    
    def calculate_rolling_metrics(
        self,
        returns: np.ndarray,
        window: int = 60,
        metrics: List[str] = None
    ) -> Dict[str, np.ndarray]:
        """Calculate rolling performance metrics."""
        try:
            if metrics is None:
                metrics = ['return', 'volatility', 'sharpe', 'max_drawdown']
            
            rolling_metrics = {}
            
            for i in range(window, len(returns) + 1):
                window_returns = returns[i-window:i]
                
                if 'return' in metrics:
                    if 'return' not in rolling_metrics:
                        rolling_metrics['return'] = []
                    rolling_metrics['return'].append(np.mean(window_returns) * self.trading_days_per_year)
                
                if 'volatility' in metrics:
                    if 'volatility' not in rolling_metrics:
                        rolling_metrics['volatility'] = []
                    rolling_metrics['volatility'].append(np.std(window_returns) * np.sqrt(self.trading_days_per_year))
                
                if 'sharpe' in metrics:
                    if 'sharpe' not in rolling_metrics:
                        rolling_metrics['sharpe'] = []
                    excess_returns = window_returns - self.risk_free_rate / self.trading_days_per_year
                    sharpe = np.mean(excess_returns) / np.std(window_returns) * np.sqrt(self.trading_days_per_year)
                    rolling_metrics['sharpe'].append(sharpe)
                
                if 'max_drawdown' in metrics:
                    if 'max_drawdown' not in rolling_metrics:
                        rolling_metrics['max_drawdown'] = []
                    cumulative = np.cumprod(1 + window_returns)
                    running_max = np.maximum.accumulate(cumulative)
                    drawdowns = (cumulative - running_max) / running_max
                    rolling_metrics['max_drawdown'].append(np.min(drawdowns))
            
            # Convert to numpy arrays
            for metric in rolling_metrics:
                rolling_metrics[metric] = np.array(rolling_metrics[metric])
            
            return rolling_metrics
            
        except Exception as e:
            logger.error(f"Error calculating rolling metrics: {e}")
            return {}
    
    def _calculate_skewness(self, returns: np.ndarray) -> float:
        """Calculate skewness of returns."""
        try:
            mean_return = np.mean(returns)
            std_return = np.std(returns)
            
            if std_return == 0:
                return 0.0
            
            skewness = np.mean(((returns - mean_return) / std_return) ** 3)
            return skewness
            
        except Exception as e:
            logger.error(f"Error calculating skewness: {e}")
            return 0.0
    
    def _calculate_kurtosis(self, returns: np.ndarray) -> float:
        """Calculate kurtosis of returns."""
        try:
            mean_return = np.mean(returns)
            std_return = np.std(returns)
            
            if std_return == 0:
                return 0.0
            
            kurtosis = np.mean(((returns - mean_return) / std_return) ** 4) - 3
            return kurtosis
            
        except Exception as e:
            logger.error(f"Error calculating kurtosis: {e}")
            return 0.0
    
    def _calculate_beta(self, returns: np.ndarray, benchmark_returns: np.ndarray) -> float:
        """Calculate beta relative to benchmark."""
        try:
            covariance = np.cov(returns, benchmark_returns)[0, 1]
            benchmark_variance = np.var(benchmark_returns)
            
            if benchmark_variance == 0:
                return 1.0
            
            beta = covariance / benchmark_variance
            return beta
            
        except Exception as e:
            logger.error(f"Error calculating beta: {e}")
            return 1.0
    
    def generate_performance_report(
        self,
        portfolio_returns: np.ndarray,
        benchmark_returns: Optional[np.ndarray] = None,
        portfolio_weights: Optional[Dict[str, float]] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> Dict[str, Any]:
        """Generate comprehensive performance report."""
        try:
            # Calculate performance metrics
            performance_metrics = self.calculate_performance_metrics(portfolio_returns, benchmark_returns)
            
            # Calculate rolling metrics
            rolling_metrics = self.calculate_rolling_metrics(portfolio_returns)
            
            # Attribution analysis (if weights provided)
            attribution = None
            if portfolio_weights and benchmark_returns is not None:
                # Create dummy benchmark weights (equal weight)
                benchmark_weights = {symbol: 1.0/len(portfolio_weights) for symbol in portfolio_weights.keys()}
                
                # Get asset returns (simplified)
                asset_returns = {}
                for symbol in portfolio_weights.keys():
                    # Would get actual asset returns from database
                    asset_returns[symbol] = portfolio_returns  # Simplified
                
                attribution = self.calculate_attribution_analysis(
                    portfolio_weights, benchmark_weights, asset_returns, benchmark_returns
                )
            
            # Create comprehensive report
            report = {
                'performance_metrics': performance_metrics,
                'rolling_metrics': rolling_metrics,
                'attribution_analysis': attribution,
                'period': {
                    'start_date': start_date.isoformat() if start_date else None,
                    'end_date': end_date.isoformat() if end_date else None,
                    'total_days': len(portfolio_returns)
                },
                'summary': {
                    'total_return': performance_metrics.total_return,
                    'annualized_return': performance_metrics.annualized_return,
                    'volatility': performance_metrics.volatility,
                    'sharpe_ratio': performance_metrics.sharpe_ratio,
                    'max_drawdown': performance_metrics.max_drawdown,
                    'win_rate': performance_metrics.win_rate
                }
            }
            
            return report

        except Exception as e:
            logger.error(f"Error generating performance report: {e}")
            raise

# Global performance analytics instance
performance_analytics = PerformanceAnalytics()
