"""Database models for the stock analysis system."""

from sqlalchemy import (
    Column, Integer, String, Float, DateTime, Date, Boolean, 
    Text, ForeignKey, Index, UniqueConstraint, CheckConstraint,
    DECIMAL, BigInteger
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime, date
from typing import Optional

from .database import Base

class StockMetadata(Base):
    """Stock metadata and company information."""
    __tablename__ = "stock_metadata"
    
    id = Column(Integer, primary_key=True, index=True)
    symbol = Column(String(20), unique=True, nullable=False, index=True)
    company_name = Column(String(200), nullable=False)
    series = Column(String(10), nullable=False, default='EQ')
    isin = Column(String(12), unique=True, nullable=False)
    face_value = Column(DECIMAL(10, 2), nullable=False)
    market_lot = Column(Integer, nullable=False, default=1)
    listing_date = Column(Date, nullable=False)
    sector = Column(String(100))
    industry = Column(String(100))
    market_cap = Column(BigInteger)  # in crores
    is_active = Column(Boolean, default=True, nullable=False)
    is_nifty50 = Column(Boolean, default=False, nullable=False)
    is_nifty100 = Column(Boolean, default=False, nullable=False)
    is_nifty500 = Column(Boolean, default=False, nullable=False)
    
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relationships
    daily_prices = relationship("DailyPrice", back_populates="stock")
    corporate_actions = relationship("CorporateAction", back_populates="stock")
    strategy_signals = relationship("StrategySignal", back_populates="stock")
    
    __table_args__ = (
        Index('idx_stock_symbol_active', 'symbol', 'is_active'),
        Index('idx_stock_nifty50', 'is_nifty50'),
    )

class DailyPrice(Base):
    """Daily stock price data with technical indicators."""
    __tablename__ = "daily_prices"
    
    id = Column(Integer, primary_key=True, index=True)
    stock_id = Column(Integer, ForeignKey("stock_metadata.id"), nullable=False)
    date = Column(Date, nullable=False, index=True)
    
    # OHLCV data
    open_price = Column(DECIMAL(10, 2), nullable=False)
    high_price = Column(DECIMAL(10, 2), nullable=False)
    low_price = Column(DECIMAL(10, 2), nullable=False)
    close_price = Column(DECIMAL(10, 2), nullable=False)
    volume = Column(BigInteger, nullable=False)
    turnover = Column(DECIMAL(15, 2))
    
    # Adjusted prices (for corporate actions)
    adj_open = Column(DECIMAL(10, 2))
    adj_high = Column(DECIMAL(10, 2))
    adj_low = Column(DECIMAL(10, 2))
    adj_close = Column(DECIMAL(10, 2))
    adj_volume = Column(BigInteger)
    
    # Technical indicators
    sma_20 = Column(DECIMAL(10, 2))
    sma_50 = Column(DECIMAL(10, 2))
    sma_200 = Column(DECIMAL(10, 2))
    ema_12 = Column(DECIMAL(10, 2))
    ema_26 = Column(DECIMAL(10, 2))
    
    rsi_14 = Column(DECIMAL(5, 2))
    macd = Column(DECIMAL(10, 4))
    macd_signal = Column(DECIMAL(10, 4))
    macd_histogram = Column(DECIMAL(10, 4))
    
    bb_upper = Column(DECIMAL(10, 2))  # Bollinger Bands
    bb_middle = Column(DECIMAL(10, 2))
    bb_lower = Column(DECIMAL(10, 2))
    
    adx = Column(DECIMAL(5, 2))  # Average Directional Index
    atr = Column(DECIMAL(10, 4))  # Average True Range
    
    # Volume indicators
    volume_sma_20 = Column(BigInteger)
    volume_ratio = Column(DECIMAL(5, 2))  # Current volume / 20-day average
    
    # Pivot points
    pivot_point = Column(DECIMAL(10, 2))
    resistance_1 = Column(DECIMAL(10, 2))
    resistance_2 = Column(DECIMAL(10, 2))
    support_1 = Column(DECIMAL(10, 2))
    support_2 = Column(DECIMAL(10, 2))
    
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relationships
    stock = relationship("StockMetadata", back_populates="daily_prices")
    
    __table_args__ = (
        UniqueConstraint('stock_id', 'date', name='uq_stock_date'),
        Index('idx_daily_price_stock_date', 'stock_id', 'date'),
        Index('idx_daily_price_date', 'date'),
        CheckConstraint('high_price >= low_price', name='check_high_low'),
        CheckConstraint('volume >= 0', name='check_volume_positive'),
    )

class CorporateAction(Base):
    """Corporate actions like splits, dividends, bonuses."""
    __tablename__ = "corporate_actions"
    
    id = Column(Integer, primary_key=True, index=True)
    stock_id = Column(Integer, ForeignKey("stock_metadata.id"), nullable=False)
    
    action_type = Column(String(20), nullable=False)  # SPLIT, DIVIDEND, BONUS, RIGHTS
    ex_date = Column(Date, nullable=False, index=True)
    record_date = Column(Date)
    
    # Split/Bonus details
    numerator = Column(Integer)  # For splits: new shares
    denominator = Column(Integer)  # For splits: old shares
    
    # Dividend details
    dividend_amount = Column(DECIMAL(10, 2))  # Per share dividend
    
    # Additional details
    description = Column(Text)
    is_processed = Column(Boolean, default=False, nullable=False)
    
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relationships
    stock = relationship("StockMetadata", back_populates="corporate_actions")
    
    __table_args__ = (
        Index('idx_corporate_action_stock_date', 'stock_id', 'ex_date'),
        Index('idx_corporate_action_type', 'action_type'),
        CheckConstraint("action_type IN ('SPLIT', 'DIVIDEND', 'BONUS', 'RIGHTS')", 
                       name='check_action_type'),
    )

class StrategySignal(Base):
    """Trading signals generated by strategies."""
    __tablename__ = "strategy_signals"
    
    id = Column(Integer, primary_key=True, index=True)
    stock_id = Column(Integer, ForeignKey("stock_metadata.id"), nullable=False)
    
    strategy_name = Column(String(50), nullable=False, index=True)
    signal_type = Column(String(10), nullable=False)  # BUY, SELL, HOLD
    signal_strength = Column(DECIMAL(3, 2), nullable=False)  # 0.0 to 1.0
    
    price = Column(DECIMAL(10, 2), nullable=False)
    target_price = Column(DECIMAL(10, 2))
    stop_loss = Column(DECIMAL(10, 2))
    
    signal_date = Column(Date, nullable=False, index=True)
    signal_time = Column(DateTime, default=func.now(), nullable=False)
    
    # Strategy-specific parameters
    parameters = Column(Text)  # JSON string of strategy parameters
    
    # Signal validation
    is_valid = Column(Boolean, default=True, nullable=False)
    confidence_score = Column(DECIMAL(3, 2))  # ML confidence if applicable
    
    created_at = Column(DateTime, default=func.now(), nullable=False)
    
    # Relationships
    stock = relationship("StockMetadata", back_populates="strategy_signals")
    
    __table_args__ = (
        Index('idx_strategy_signal_stock_date', 'stock_id', 'signal_date'),
        Index('idx_strategy_signal_strategy_type', 'strategy_name', 'signal_type'),
        CheckConstraint("signal_type IN ('BUY', 'SELL', 'HOLD')", 
                       name='check_signal_type'),
        CheckConstraint('signal_strength >= 0.0 AND signal_strength <= 1.0',
                       name='check_signal_strength'),
    )

class BacktestResult(Base):
    """Backtesting results for strategies."""
    __tablename__ = "backtest_results"

    id = Column(Integer, primary_key=True, index=True)
    strategy_name = Column(String(50), nullable=False, index=True)

    # Test parameters
    start_date = Column(Date, nullable=False)
    end_date = Column(Date, nullable=False)
    initial_capital = Column(DECIMAL(15, 2), nullable=False)

    # Performance metrics
    total_return = Column(DECIMAL(8, 4))  # Percentage
    annual_return = Column(DECIMAL(8, 4))
    sharpe_ratio = Column(DECIMAL(6, 4))
    max_drawdown = Column(DECIMAL(8, 4))
    calmar_ratio = Column(DECIMAL(6, 4))

    # Trade statistics
    total_trades = Column(Integer, nullable=False)
    winning_trades = Column(Integer, nullable=False)
    losing_trades = Column(Integer, nullable=False)
    win_rate = Column(DECIMAL(5, 2))  # Percentage

    avg_win = Column(DECIMAL(10, 2))
    avg_loss = Column(DECIMAL(10, 2))
    profit_factor = Column(DECIMAL(6, 4))

    # Risk metrics
    volatility = Column(DECIMAL(6, 4))
    var_95 = Column(DECIMAL(10, 2))  # Value at Risk 95%

    # Configuration used
    parameters = Column(Text)  # JSON string of strategy parameters

    created_at = Column(DateTime, default=func.now(), nullable=False)

    __table_args__ = (
        Index('idx_backtest_strategy_date', 'strategy_name', 'start_date', 'end_date'),
    )

class PortfolioPosition(Base):
    """Current portfolio positions."""
    __tablename__ = "portfolio_positions"

    id = Column(Integer, primary_key=True, index=True)
    stock_id = Column(Integer, ForeignKey("stock_metadata.id"), nullable=False)

    quantity = Column(Integer, nullable=False)
    avg_price = Column(DECIMAL(10, 2), nullable=False)
    current_price = Column(DECIMAL(10, 2))

    # P&L tracking
    unrealized_pnl = Column(DECIMAL(12, 2))
    realized_pnl = Column(DECIMAL(12, 2), default=0)

    # Position details
    entry_date = Column(Date, nullable=False)
    entry_strategy = Column(String(50))

    # Risk management
    stop_loss = Column(DECIMAL(10, 2))
    target_price = Column(DECIMAL(10, 2))

    is_active = Column(Boolean, default=True, nullable=False)

    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)

    # Relationships
    stock = relationship("StockMetadata")

    __table_args__ = (
        Index('idx_portfolio_stock_active', 'stock_id', 'is_active'),
    )

class TradeHistory(Base):
    """Historical trade records."""
    __tablename__ = "trade_history"

    id = Column(Integer, primary_key=True, index=True)
    stock_id = Column(Integer, ForeignKey("stock_metadata.id"), nullable=False)

    trade_type = Column(String(10), nullable=False)  # BUY, SELL
    quantity = Column(Integer, nullable=False)
    price = Column(DECIMAL(10, 2), nullable=False)

    trade_date = Column(Date, nullable=False, index=True)
    trade_time = Column(DateTime, default=func.now(), nullable=False)

    # Strategy and signal info
    strategy_name = Column(String(50))
    signal_id = Column(Integer, ForeignKey("strategy_signals.id"))

    # Transaction costs
    commission = Column(DECIMAL(8, 2), default=0)
    taxes = Column(DECIMAL(8, 2), default=0)

    # P&L (for sell trades)
    pnl = Column(DECIMAL(12, 2))
    pnl_percentage = Column(DECIMAL(6, 2))

    created_at = Column(DateTime, default=func.now(), nullable=False)

    # Relationships
    stock = relationship("StockMetadata")
    signal = relationship("StrategySignal")

    __table_args__ = (
        Index('idx_trade_stock_date', 'stock_id', 'trade_date'),
        Index('idx_trade_strategy', 'strategy_name'),
        CheckConstraint("trade_type IN ('BUY', 'SELL')", name='check_trade_type'),
        CheckConstraint('quantity > 0', name='check_quantity_positive'),
        CheckConstraint('price > 0', name='check_price_positive'),
    )

class RLModel(Base):
    """RL model storage for trained agents."""
    __tablename__ = "rl_models"

    id = Column(Integer, primary_key=True, autoincrement=True)
    strategy_name = Column(String(50), nullable=False)
    model_type = Column(String(20), nullable=False)  # PPO, DQN, etc.
    model_path = Column(String(255), nullable=False)

    # Model configuration
    parameters = Column(Text)  # JSON string of parameter bounds
    hyperparameters = Column(Text)  # JSON string of hyperparameters

    # Performance metrics
    performance_metrics = Column(Text)  # JSON string of performance data
    training_iterations = Column(Integer, default=0)
    avg_reward = Column(DECIMAL(8, 4), default=0)

    # Model status
    is_active = Column(Boolean, default=True)
    version = Column(Integer, default=1)

    # Timestamps
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    last_trained = Column(DateTime)

    __table_args__ = (
        Index('idx_rl_model_strategy', 'strategy_name'),
        Index('idx_rl_model_active', 'is_active'),
        UniqueConstraint('strategy_name', 'version', name='uq_strategy_version'),
    )

class RLTrainingHistory(Base):
    """Training history for RL models."""
    __tablename__ = "rl_training_history"

    id = Column(Integer, primary_key=True, autoincrement=True)
    model_id = Column(Integer, ForeignKey("rl_models.id"))

    # Training session info
    training_date = Column(Date, nullable=False)
    strategy_name = Column(String(50), nullable=False)
    training_iterations = Column(Integer, nullable=False)

    # Training metrics
    avg_reward = Column(DECIMAL(8, 4))
    final_reward = Column(DECIMAL(8, 4))
    training_time_minutes = Column(Integer)

    # Training configuration
    parameters_optimized = Column(Text)  # JSON string
    training_symbols = Column(Text)  # JSON array of symbols used

    # Performance improvement
    improvement_over_baseline = Column(DECIMAL(6, 2))  # Percentage improvement

    created_at = Column(DateTime, default=func.now(), nullable=False)

    # Relationships
    model = relationship("RLModel")

    __table_args__ = (
        Index('idx_rl_training_date', 'training_date'),
        Index('idx_rl_training_strategy', 'strategy_name'),
    )

class RLParameterAdaptation(Base):
    """Real-time parameter adaptations by RL agents."""
    __tablename__ = "rl_parameter_adaptations"

    id = Column(Integer, primary_key=True, autoincrement=True)
    model_id = Column(Integer, ForeignKey("rl_models.id"))
    stock_id = Column(Integer, ForeignKey("stock_metadata.id"))

    # Adaptation info
    adaptation_date = Column(Date, nullable=False)
    strategy_name = Column(String(50), nullable=False)

    # Market conditions at time of adaptation
    market_conditions = Column(Text)  # JSON string of market state

    # Adapted parameters
    original_parameters = Column(Text)  # JSON string
    adapted_parameters = Column(Text)  # JSON string
    adaptation_reason = Column(String(100))  # volatility, trend_change, etc.

    # Performance tracking
    expected_improvement = Column(DECIMAL(6, 2))  # Expected improvement %
    actual_performance = Column(DECIMAL(6, 2))  # Actual performance if measured

    created_at = Column(DateTime, default=func.now(), nullable=False)

    # Relationships
    model = relationship("RLModel")
    stock = relationship("StockMetadata")

    __table_args__ = (
        Index('idx_rl_adaptation_date', 'adaptation_date'),
        Index('idx_rl_adaptation_stock', 'stock_id'),
        Index('idx_rl_adaptation_strategy', 'strategy_name'),
    )
