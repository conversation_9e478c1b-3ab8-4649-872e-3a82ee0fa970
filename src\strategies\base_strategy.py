"""
Base strategy class and framework for all trading strategies.
Provides common functionality for signal generation, performance tracking, and risk management.
"""

from abc import ABC, abstractmethod
from datetime import datetime, date
from typing import Dict, List, Any, Optional, Tuple
from enum import Enum
import pandas as pd
import numpy as np
from dataclasses import dataclass
from decimal import Decimal

from ..data.database import db_manager
from ..data.models import StockMetadata, DailyPrice, StrategySignal
from ..data.crud import StockCRUD, PriceCRUD, SignalCRUD
from ..utils.logger import get_logger
from ..utils.config import config

logger = get_logger(__name__)

class SignalType(Enum):
    """Trading signal types."""
    BUY = "BUY"
    SELL = "SELL"
    HOLD = "HOLD"

class SignalStrength(Enum):
    """Signal strength levels."""
    WEAK = 0.3
    MODERATE = 0.6
    STRONG = 0.8
    VERY_STRONG = 1.0

@dataclass
class TradingSignal:
    """Trading signal data structure."""
    symbol: str
    signal_type: SignalType
    signal_strength: float
    price: float
    target_price: Optional[float] = None
    stop_loss: Optional[float] = None
    confidence_score: Optional[float] = None
    parameters: Optional[Dict[str, Any]] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

@dataclass
class PositionSize:
    """Position sizing information."""
    shares: int
    capital_allocated: float
    risk_amount: float
    position_percentage: float

@dataclass
class RiskMetrics:
    """Risk management metrics."""
    max_position_size: float
    stop_loss_percentage: float
    take_profit_percentage: float
    max_drawdown: float
    sharpe_ratio: float
    win_rate: float

class BaseStrategy(ABC):
    """Base class for all trading strategies."""
    
    def __init__(self, name: str, parameters: Optional[Dict[str, Any]] = None):
        """Initialize base strategy.
        
        Args:
            name: Strategy name
            parameters: Strategy-specific parameters
        """
        self.name = name
        self.parameters = parameters or {}
        self.config = config.get_strategy_config(name.lower())
        self.risk_config = config.get_risk_management_config()
        
        # Performance tracking
        self.signals_generated = []
        self.performance_metrics = {}
        
        # Risk management
        self.max_position_size = self.risk_config.get('max_single_position', 0.05)
        self.stop_loss_pct = self.risk_config.get('stop_loss_percentage', 0.04)
        self.take_profit_pct = self.risk_config.get('take_profit_percentage', 0.15)
        
        logger.info(f"Initialized {self.name} strategy with parameters: {self.parameters}")
    
    @abstractmethod
    def calculate_signals(self, data: pd.DataFrame) -> List[TradingSignal]:
        """Calculate trading signals for given data.
        
        Args:
            data: DataFrame with OHLCV and technical indicator data
            
        Returns:
            List of trading signals
        """
        pass
    
    @abstractmethod
    def get_required_indicators(self) -> List[str]:
        """Get list of required technical indicators.
        
        Returns:
            List of indicator names required by this strategy
        """
        pass
    
    def validate_data(self, data: pd.DataFrame) -> bool:
        """Validate that data contains required indicators.
        
        Args:
            data: DataFrame to validate
            
        Returns:
            True if data is valid, False otherwise
        """
        required_columns = ['date', 'open_price', 'high_price', 'low_price', 'close_price', 'volume']
        required_indicators = self.get_required_indicators()
        
        all_required = required_columns + required_indicators
        
        missing_columns = [col for col in all_required if col not in data.columns]
        
        if missing_columns:
            logger.error(f"Missing required columns for {self.name}: {missing_columns}")
            return False
        
        # Check for sufficient data points
        if len(data) < 50:  # Minimum 50 data points
            logger.warning(f"Insufficient data points for {self.name}: {len(data)}")
            return False
        
        return True
    
    def calculate_position_size(
        self, 
        signal: TradingSignal, 
        portfolio_value: float,
        current_positions: Dict[str, float] = None
    ) -> PositionSize:
        """Calculate position size based on risk management rules.
        
        Args:
            signal: Trading signal
            portfolio_value: Current portfolio value
            current_positions: Current position sizes by symbol
            
        Returns:
            Position sizing information
        """
        current_positions = current_positions or {}
        
        # Calculate risk amount (2% of portfolio by default)
        risk_percentage = self.risk_config.get('position_size', 0.02)
        risk_amount = portfolio_value * risk_percentage
        
        # Calculate position size based on stop loss
        if signal.stop_loss and signal.price > signal.stop_loss:
            price_risk = signal.price - signal.stop_loss
            shares = int(risk_amount / price_risk)
        else:
            # Fallback to percentage-based sizing
            max_position_value = portfolio_value * self.max_position_size
            shares = int(max_position_value / signal.price)
        
        # Ensure we don't exceed maximum position size
        capital_allocated = shares * signal.price
        position_percentage = capital_allocated / portfolio_value
        
        if position_percentage > self.max_position_size:
            shares = int((portfolio_value * self.max_position_size) / signal.price)
            capital_allocated = shares * signal.price
            position_percentage = self.max_position_size
        
        return PositionSize(
            shares=shares,
            capital_allocated=capital_allocated,
            risk_amount=risk_amount,
            position_percentage=position_percentage
        )
    
    def apply_risk_management(self, signal: TradingSignal) -> TradingSignal:
        """Apply risk management rules to a signal.
        
        Args:
            signal: Original trading signal
            
        Returns:
            Signal with risk management applied
        """
        if signal.signal_type == SignalType.BUY:
            # Set stop loss if not already set
            if not signal.stop_loss:
                signal.stop_loss = signal.price * (1 - self.stop_loss_pct)
            
            # Set target price if not already set
            if not signal.target_price:
                signal.target_price = signal.price * (1 + self.take_profit_pct)
        
        elif signal.signal_type == SignalType.SELL:
            # For sell signals, reverse the logic
            if not signal.stop_loss:
                signal.stop_loss = signal.price * (1 + self.stop_loss_pct)
            
            if not signal.target_price:
                signal.target_price = signal.price * (1 - self.take_profit_pct)
        
        return signal
    
    def filter_signals(self, signals: List[TradingSignal]) -> List[TradingSignal]:
        """Filter signals based on quality and risk criteria.
        
        Args:
            signals: List of raw signals
            
        Returns:
            Filtered list of signals
        """
        filtered_signals = []
        
        for signal in signals:
            # Filter by signal strength
            min_strength = self.parameters.get('min_signal_strength', 0.5)
            if signal.signal_strength < min_strength:
                continue
            
            # Filter by confidence score if available
            if signal.confidence_score is not None:
                min_confidence = self.parameters.get('min_confidence_score', 0.6)
                if signal.confidence_score < min_confidence:
                    continue
            
            # Apply risk management
            signal = self.apply_risk_management(signal)
            
            filtered_signals.append(signal)
        
        return filtered_signals
    
    def generate_signals_for_stock(self, symbol: str, lookback_days: int = 100) -> List[TradingSignal]:
        """Generate signals for a specific stock.
        
        Args:
            symbol: Stock symbol
            lookback_days: Number of days of historical data to use
            
        Returns:
            List of trading signals
        """
        try:
            with db_manager.get_session() as db:
                # Get stock metadata
                stock = StockCRUD.get_stock_by_symbol(db, symbol)
                if not stock:
                    logger.error(f"Stock {symbol} not found")
                    return []
                
                # Get historical data
                end_date = date.today()
                start_date = end_date - pd.Timedelta(days=lookback_days)
                
                price_data = PriceCRUD.get_price_history(db, stock.id, start_date, end_date)
                
                if not price_data:
                    logger.warning(f"No price data found for {symbol}")
                    return []
                
                # Convert to DataFrame
                data = pd.DataFrame([{
                    'date': p.date,
                    'open_price': float(p.open_price),
                    'high_price': float(p.high_price),
                    'low_price': float(p.low_price),
                    'close_price': float(p.close_price),
                    'volume': p.volume,
                    'sma_20': float(p.sma_20) if p.sma_20 else None,
                    'sma_50': float(p.sma_50) if p.sma_50 else None,
                    'sma_200': float(p.sma_200) if p.sma_200 else None,
                    'ema_12': float(p.ema_12) if p.ema_12 else None,
                    'ema_26': float(p.ema_26) if p.ema_26 else None,
                    'rsi_14': float(p.rsi_14) if p.rsi_14 else None,
                    'macd': float(p.macd) if p.macd else None,
                    'macd_signal': float(p.macd_signal) if p.macd_signal else None,
                    'macd_histogram': float(p.macd_histogram) if p.macd_histogram else None,
                    'bb_upper': float(p.bb_upper) if p.bb_upper else None,
                    'bb_middle': float(p.bb_middle) if p.bb_middle else None,
                    'bb_lower': float(p.bb_lower) if p.bb_lower else None,
                    'volume_ratio': float(p.volume_ratio) if p.volume_ratio else None,
                    'pivot_point': float(p.pivot_point) if p.pivot_point else None,
                    'resistance_1': float(p.resistance_1) if p.resistance_1 else None,
                    'resistance_2': float(p.resistance_2) if p.resistance_2 else None,
                    'support_1': float(p.support_1) if p.support_1 else None,
                    'support_2': float(p.support_2) if p.support_2 else None,
                } for p in price_data])
                
                data = data.sort_values('date').reset_index(drop=True)
                
                # Validate data
                if not self.validate_data(data):
                    return []
                
                # Calculate signals
                signals = self.calculate_signals(data)
                
                # Add symbol to signals
                for signal in signals:
                    signal.symbol = symbol
                
                # Filter signals
                filtered_signals = self.filter_signals(signals)
                
                logger.info(f"Generated {len(filtered_signals)} signals for {symbol} using {self.name}")
                return filtered_signals
                
        except Exception as e:
            logger.error(f"Error generating signals for {symbol}: {e}")
            return []

    def store_signals(self, signals: List[TradingSignal]) -> bool:
        """Store signals in database.

        Args:
            signals: List of signals to store

        Returns:
            True if successful, False otherwise
        """
        try:
            with db_manager.get_session() as db:
                for signal in signals:
                    # Get stock ID
                    stock = StockCRUD.get_stock_by_symbol(db, signal.symbol)
                    if not stock:
                        continue

                    # Prepare signal data
                    signal_data = {
                        'stock_id': stock.id,
                        'strategy_name': self.name,
                        'signal_type': signal.signal_type.value,
                        'signal_strength': signal.signal_strength,
                        'price': signal.price,
                        'target_price': signal.target_price,
                        'stop_loss': signal.stop_loss,
                        'signal_date': signal.timestamp.date(),
                        'signal_time': signal.timestamp,
                        'confidence_score': signal.confidence_score,
                        'parameters': str(signal.parameters) if signal.parameters else None,
                        'is_valid': True
                    }

                    # Store signal
                    SignalCRUD.create_signal(db, signal_data)

                logger.info(f"Stored {len(signals)} signals for {self.name}")
                return True

        except Exception as e:
            logger.error(f"Error storing signals: {e}")
            return False

    def calculate_performance_metrics(self, signals: List[TradingSignal]) -> Dict[str, Any]:
        """Calculate performance metrics for generated signals.

        Args:
            signals: List of signals to analyze

        Returns:
            Dictionary of performance metrics
        """
        if not signals:
            return {}

        try:
            # Basic signal statistics
            total_signals = len(signals)
            buy_signals = sum(1 for s in signals if s.signal_type == SignalType.BUY)
            sell_signals = sum(1 for s in signals if s.signal_type == SignalType.SELL)
            hold_signals = sum(1 for s in signals if s.signal_type == SignalType.HOLD)

            # Signal strength distribution
            strengths = [s.signal_strength for s in signals]
            avg_strength = np.mean(strengths)

            # Confidence scores if available
            confidences = [s.confidence_score for s in signals if s.confidence_score is not None]
            avg_confidence = np.mean(confidences) if confidences else None

            metrics = {
                'total_signals': total_signals,
                'buy_signals': buy_signals,
                'sell_signals': sell_signals,
                'hold_signals': hold_signals,
                'buy_percentage': (buy_signals / total_signals * 100) if total_signals > 0 else 0,
                'sell_percentage': (sell_signals / total_signals * 100) if total_signals > 0 else 0,
                'avg_signal_strength': avg_strength,
                'avg_confidence_score': avg_confidence,
                'strategy_name': self.name,
                'calculation_time': datetime.now().isoformat()
            }

            return metrics

        except Exception as e:
            logger.error(f"Error calculating performance metrics: {e}")
            return {}

    def get_strategy_summary(self) -> Dict[str, Any]:
        """Get comprehensive strategy summary.

        Returns:
            Dictionary with strategy information and performance
        """
        return {
            'name': self.name,
            'parameters': self.parameters,
            'config': self.config,
            'required_indicators': self.get_required_indicators(),
            'risk_management': {
                'max_position_size': self.max_position_size,
                'stop_loss_percentage': self.stop_loss_pct,
                'take_profit_percentage': self.take_profit_pct
            },
            'performance_metrics': self.performance_metrics,
            'signals_generated_count': len(self.signals_generated)
        }

    def run_strategy(self, symbols: List[str]) -> Dict[str, Any]:
        """Run strategy on multiple symbols.

        Args:
            symbols: List of stock symbols to analyze

        Returns:
            Dictionary with results and performance metrics
        """
        logger.info(f"Running {self.name} strategy on {len(symbols)} symbols")

        all_signals = []
        results_by_symbol = {}

        for symbol in symbols:
            try:
                signals = self.generate_signals_for_stock(symbol)
                all_signals.extend(signals)
                results_by_symbol[symbol] = {
                    'signals_count': len(signals),
                    'signals': signals
                }

            except Exception as e:
                logger.error(f"Error running strategy for {symbol}: {e}")
                results_by_symbol[symbol] = {
                    'error': str(e),
                    'signals_count': 0,
                    'signals': []
                }

        # Store all signals
        if all_signals:
            self.store_signals(all_signals)

        # Calculate overall performance metrics
        performance_metrics = self.calculate_performance_metrics(all_signals)
        self.performance_metrics = performance_metrics
        self.signals_generated = all_signals

        return {
            'strategy_name': self.name,
            'symbols_processed': len(symbols),
            'total_signals': len(all_signals),
            'results_by_symbol': results_by_symbol,
            'performance_metrics': performance_metrics,
            'execution_time': datetime.now().isoformat()
        }

class StrategyManager:
    """Manages multiple trading strategies."""

    def __init__(self):
        self.strategies = {}
        self.enabled_strategies = []
        self._load_enabled_strategies()

    def _load_enabled_strategies(self):
        """Load enabled strategies from configuration."""
        strategy_configs = config.get('strategies', {})

        for strategy_name, strategy_config in strategy_configs.items():
            if strategy_config.get('enabled', False):
                self.enabled_strategies.append(strategy_name)

        logger.info(f"Enabled strategies: {self.enabled_strategies}")

    def register_strategy(self, strategy: BaseStrategy):
        """Register a strategy with the manager.

        Args:
            strategy: Strategy instance to register
        """
        self.strategies[strategy.name] = strategy
        logger.info(f"Registered strategy: {strategy.name}")

    def get_strategy(self, name: str) -> Optional[BaseStrategy]:
        """Get strategy by name.

        Args:
            name: Strategy name

        Returns:
            Strategy instance or None if not found
        """
        return self.strategies.get(name)

    def run_all_strategies(self, symbols: List[str]) -> Dict[str, Any]:
        """Run all enabled strategies on given symbols.

        Args:
            symbols: List of stock symbols

        Returns:
            Results from all strategies
        """
        logger.info(f"Running all enabled strategies on {len(symbols)} symbols")

        results = {}

        for strategy_name in self.enabled_strategies:
            strategy = self.strategies.get(strategy_name)
            if strategy:
                try:
                    results[strategy_name] = strategy.run_strategy(symbols)
                except Exception as e:
                    logger.error(f"Error running strategy {strategy_name}: {e}")
                    results[strategy_name] = {'error': str(e)}
            else:
                logger.warning(f"Strategy {strategy_name} not registered")

        return {
            'execution_time': datetime.now().isoformat(),
            'strategies_run': len(results),
            'symbols_processed': len(symbols),
            'results': results
        }

    def get_latest_signals(self, strategy_name: Optional[str] = None, limit: int = 100) -> List[Dict[str, Any]]:
        """Get latest signals from database.

        Args:
            strategy_name: Filter by strategy name (optional)
            limit: Maximum number of signals to return

        Returns:
            List of signal dictionaries
        """
        try:
            with db_manager.get_session() as db:
                signals = SignalCRUD.get_latest_signals(db, strategy_name, None, limit)

                return [{
                    'id': signal.id,
                    'symbol': signal.stock.symbol,
                    'strategy_name': signal.strategy_name,
                    'signal_type': signal.signal_type,
                    'signal_strength': float(signal.signal_strength),
                    'price': float(signal.price),
                    'target_price': float(signal.target_price) if signal.target_price else None,
                    'stop_loss': float(signal.stop_loss) if signal.stop_loss else None,
                    'signal_date': signal.signal_date.isoformat(),
                    'signal_time': signal.signal_time.isoformat(),
                    'confidence_score': float(signal.confidence_score) if signal.confidence_score else None
                } for signal in signals]

        except Exception as e:
            logger.error(f"Error getting latest signals: {e}")
            return []

# Global strategy manager instance
strategy_manager = StrategyManager()
