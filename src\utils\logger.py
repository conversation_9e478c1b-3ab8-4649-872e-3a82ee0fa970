"""Logging configuration and utilities."""

import sys
from pathlib import Path
from loguru import logger
from .config import config

def setup_logging():
    """Setup logging configuration based on config settings."""
    
    # Remove default handler
    logger.remove()
    
    # Get logging configuration
    log_level = config.get('logging.level', 'INFO')
    log_format = config.get('logging.format', 
                           "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}")
    rotation = config.get('logging.rotation', '1 day')
    retention = config.get('logging.retention', '30 days')
    
    # Console handler
    logger.add(
        sys.stdout,
        format=log_format,
        level=log_level,
        colorize=True
    )
    
    # File handler for general logs
    logs_dir = Path(__file__).parent.parent.parent / "logs"
    logs_dir.mkdir(exist_ok=True)
    
    logger.add(
        logs_dir / "stock_analyzer.log",
        format=log_format,
        level=log_level,
        rotation=rotation,
        retention=retention,
        compression="zip"
    )
    
    # Separate file for errors
    logger.add(
        logs_dir / "errors.log",
        format=log_format,
        level="ERROR",
        rotation=rotation,
        retention=retention,
        compression="zip"
    )
    
    # Trading signals log
    logger.add(
        logs_dir / "trading_signals.log",
        format=log_format,
        level="INFO",
        rotation=rotation,
        retention=retention,
        compression="zip",
        filter=lambda record: "TRADING_SIGNAL" in record["message"]
    )
    
    logger.info("Logging system initialized")

def get_logger(name: str):
    """Get a logger instance with the given name."""
    return logger.bind(name=name)

# Initialize logging on import
setup_logging()
