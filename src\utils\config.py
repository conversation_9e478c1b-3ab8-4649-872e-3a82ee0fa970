"""Configuration management utilities."""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from dotenv import load_dotenv
from loguru import logger

class ConfigManager:
    """Manages application configuration from YAML files and environment variables."""
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize configuration manager.
        
        Args:
            config_path: Path to configuration YAML file
        """
        # Load environment variables
        load_dotenv()
        
        # Set default config path
        if config_path is None:
            config_path = Path(__file__).parent.parent.parent / "config" / "config.yaml"
        
        self.config_path = Path(config_path)
        self._config = self._load_config()
        
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file with environment variable substitution."""
        try:
            with open(self.config_path, 'r') as file:
                config_content = file.read()
                
            # Replace environment variables in format ${VAR_NAME}
            config_content = self._substitute_env_vars(config_content)
            
            config = yaml.safe_load(config_content)
            logger.info(f"Configuration loaded from {self.config_path}")
            return config
            
        except FileNotFoundError:
            logger.error(f"Configuration file not found: {self.config_path}")
            return {}
        except yaml.YAMLError as e:
            logger.error(f"Error parsing YAML configuration: {e}")
            return {}
    
    def _substitute_env_vars(self, content: str) -> str:
        """Substitute environment variables in configuration content."""
        import re
        
        def replace_env_var(match):
            var_name = match.group(1)
            return os.getenv(var_name, match.group(0))
        
        return re.sub(r'\$\{([^}]+)\}', replace_env_var, content)
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value using dot notation.
        
        Args:
            key: Configuration key in dot notation (e.g., 'database.host')
            default: Default value if key not found
            
        Returns:
            Configuration value
        """
        keys = key.split('.')
        value = self._config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def get_database_config(self) -> Dict[str, Any]:
        """Get database configuration."""
        return self.get('database', {})
    
    def get_strategy_config(self, strategy_name: str) -> Dict[str, Any]:
        """Get configuration for a specific strategy."""
        return self.get(f'strategies.{strategy_name}', {})
    
    def get_backtesting_config(self) -> Dict[str, Any]:
        """Get backtesting configuration."""
        return self.get('backtesting', {})
    
    def get_ml_config(self) -> Dict[str, Any]:
        """Get machine learning configuration."""
        return self.get('ml', {})
    
    def get_risk_management_config(self) -> Dict[str, Any]:
        """Get risk management configuration."""
        return self.get('risk_management', {})
    
    def is_strategy_enabled(self, strategy_name: str) -> bool:
        """Check if a strategy is enabled."""
        return self.get(f'strategies.{strategy_name}.enabled', False)

    def get_rl_config(self) -> Dict[str, Any]:
        """Get RL configuration."""
        return self.get('rl', {
            'training_enabled': True,
            'training_frequency': 'weekly',
            'num_iterations': 50,
            'episodes_per_iteration': 3,
            'learning_rate': 0.0003,
            'batch_size': 64,
            'gamma': 0.99,
            'clip_epsilon': 0.2
        })

    def get_broker_config(self) -> Dict[str, Any]:
        """Get broker configuration."""
        return self.get('broker', {
            'broker_name': 'mock',  # 'zerodha', 'mock'
            'api_key': '',
            'api_secret': '',
            'access_token': '',
            'sandbox_mode': True
        })

    def get_trading_config(self) -> Dict[str, Any]:
        """Get trading configuration."""
        return self.get('trading', {
            'trading_enabled': False,  # Set to True for live trading
            'default_mode': 'PAPER',  # 'PAPER', 'LIVE', 'SIMULATION'
            'signal_processing_interval': 30,  # seconds
            'signal_generation_interval': 300,  # seconds (5 minutes)
            'market_hours': {
                'start': '09:15',
                'end': '15:30'
            },
            'max_order_value': 1000000,  # ₹10L
            'max_position_size': 500000,  # ₹5L
            'order_timeout_minutes': 60
        })

    def get_risk_config(self) -> Dict[str, Any]:
        """Get risk management configuration."""
        return self.get('risk', {
            'max_position_size': 500000,  # ₹5L per position
            'max_portfolio_concentration': 0.15,  # 15% per stock
            'max_sector_concentration': 0.30,  # 30% per sector
            'daily_loss_limit': 50000,  # ₹50K daily loss limit
            'max_drawdown_limit': 0.10,  # 10% max drawdown
            'max_portfolio_var': 100000,  # ₹1L VaR limit
            'max_position_volatility': 0.30,  # 30% volatility limit
            'max_correlation': 0.80  # 80% correlation limit
        })

    def reload(self) -> None:
        """Reload configuration from file."""
        self._config = self._load_config()
        logger.info("Configuration reloaded")

# Global configuration instance
config = ConfigManager()
